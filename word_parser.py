from docx import Document
import os
import sys
from docx.shared import Inches

def parse_word_document(input_path, output_text_path, output_image_dir=None):
    """
    解析Word文档，并分别保存文字、表格和图片
    
    :param input_path: 输入Word文档路径
    :param output_text_path: 输出文本文件路径
    :param output_image_dir: 输出图片文件夹路径
    """
    # 创建图片输出目录
    if output_image_dir:
        os.makedirs(output_image_dir, exist_ok=True)
    
    # 加载原始文档
    doc = Document(input_path)

    # 打开文本文件准备写入
    with open(output_text_path, 'w', encoding='utf-8') as text_file:
        image_counter = 1  # 图片计数器
        write_to_file = True  # 控制是否写入文件的标志
        
        # 遍历文档中的所有段落和表格
        for element in doc.element.body:
            # 处理段落
            if element.tag.endswith('p'):
                # Find the corresponding paragraph more safely
                paragraph = next((p for p in doc.paragraphs if p._element == element), None)
                if paragraph is not None and paragraph.text.strip():
                    # 检查是否应该写入文件
                    if write_to_file:
                        text_file.write(paragraph.text + '\n')
                
            # 处理表格
            elif element.tag.endswith('tbl'):
                # 找到对应的表格，使用更安全的方法
                table_index = None
                for i, table in enumerate(doc.tables):
                    if table._element == element:
                        table_index = i
                        break
                
                if table_index is not None:
                    table = doc.tables[table_index]
                    # 写入表格内容
                    if write_to_file:
                        text_file.write("=== 表格开始 ===\n")
                        
                        # 处理每一行
                        for row in table.rows:
                            # 使用字典去重，保持原始顺序
                            row_cells = list(dict.fromkeys(cell.text.strip() for cell in row.cells if cell.text.strip()))
                            
                            # 使用管道符连接非空单元格
                            if row_cells:
                                row_text = "|".join(row_cells)
                                text_file.write(row_text + '\n')
                        text_file.write("=== 表格结束 ===\n")

def extract_images(doc, output_dir, keywords):
    """
    从文档提取图片到指定目录，直到所有关键词都检测到
    
    :param doc: Document对象
    :param output_dir: 图片输出目录
    :param keywords: 关键词状态字典
    """
        
    # 获取文档中的所有关系
    rels = doc.part.rels
    
    for rel in rels.values():
        # print(rel.reltype)            
        # 只处理图片关系
        if "image" in rel.reltype:
            # 获取图片数据
            image_data = rel.target_part.blob
            
            # 确定图片扩展名
            ext = get_image_extension(rel.target_part.content_type)
            
            # 生成图片文件名
            img_name = f"image_{len(os.listdir(output_dir)) + 1}{ext}"
            img_path = os.path.join(output_dir, img_name)
            
            # 保存图片
            with open(img_path, "wb") as f:
                f.write(image_data)

def get_image_extension(content_type):
    """
    根据内容类型获取图片扩展名
    
    :param content_type: 图片内容类型
    :return: 图片扩展名
    """
    extensions = {
        "image/png": ".png",
        "image/jpeg": ".jpg",
        "image/gif": ".gif",
        "image/bmp": ".bmp",
        "image/tiff": ".tiff",
        "image/x-wmf": ".wmf",
        "image/svg+xml": ".svg"
    }
    return extensions.get(content_type, ".jpg")  # 默认使用.jpg

if __name__ == "__main__":
    if len(sys.argv) < 4:
        print("使用方法: python script.py <输入文件路径> <输出文本路径> <输出图片目录>")
        sys.exit(1)
        
    input_file = sys.argv[1]
    output_text = sys.argv[2]
    output_images = sys.argv[3]
    
    if not os.path.exists(input_file):
        print(f"错误: 输入文件不存在 - {input_file}", file=sys.stderr)
        sys.exit(1)
        
    parse_word_document(input_file, output_text, output_images)
    # parse_word_document('1.docx', 'deploy/output_text.txt', 'deploy/output_images')