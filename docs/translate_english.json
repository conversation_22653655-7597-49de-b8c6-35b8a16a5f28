{"print亮黄": "PrintBrightYellow", "print亮绿": "PrintBrightGreen", "print亮红": "PrintBrightRed", "print红": "PrintRed", "print绿": "PrintGreen", "print黄": "PrintYellow", "print蓝": "PrintBlue", "print紫": "PrintPurple", "print靛": "PrintIndigo", "print亮蓝": "PrintBrightBlue", "print亮紫": "PrintBrightPurple", "print亮靛": "PrintBrightIndigo", "读文章写摘要": "ReadArticleWriteSummary", "批量生成函数注释": "BatchGenerateFunctionComments", "生成函数注释": "GenerateFunctionComments", "解析项目本身": "ParseProjectItself", "解析项目源代码": "ParseProjectSourceCode", "解析一个Python项目": "ParsePythonProject", "解析一个C项目的头文件": "ParseCProjectHeaderFiles", "解析一个C项目": "ParseCProject", "解析一个Golang项目": "ParseGolangProject", "解析一个Rust项目": "ParseRustProject", "解析一个Java项目": "ParseJavaProject", "解析一个前端项目": "ParseFrontendProject", "高阶功能模板函数": "HighOrderFunctionTemplateFunctions", "高级功能函数模板": "AdvancedFunctionTemplate", "全项目切换英文": "SwitchToEnglishForTheWholeProject", "代码重写为全英文_多线程": "RewriteCodeToEnglish_MultiThreaded", "Latex英文润色": "EnglishProofreadingForLatex", "Latex全文润色": "FullTextProofreadingForLatex", "同时问询": "SimultaneousInquiry", "询问多个大语言模型": "InquiryMultipleLargeLanguageModels", "解析一个Lua项目": "ParsingLuaProject", "解析一个CSharp项目": "ParsingCSharpProject", "总结word文档": "SummarizingWordDocuments", "解析ipynb文件": "ParsingIpynbFiles", "解析JupyterNotebook": "ParsingJupyterNotebook", "载入Conversation_To_File": "LoadConversationHistoryArchive", "删除所有本地对话历史记录": "DeleteAllLocalConversationHistoryRecords", "Markdown英译中": "TranslateMarkdownFromEnglishToChinese", "批量总结PDF文档": "BatchSummarizePDFDocuments", "批量总结PDF文档pdfminer": "BatchSummarizePDFDocumentsUsingPdfminer", "批量翻译PDF文档": "BatchTranslatePDFDocuments", "谷歌检索小助手": "GoogleSearchAssistant", "理解PDF文档内容标准文件输入": "UnderstandPdfDocumentContentStandardFileInput", "理解PDF文档内容": "UnderstandPdfDocumentContent", "Latex中文润色": "LatexChineseProofreading", "Latex中译英": "LatexChineseToEnglish", "Latex全文翻译": "LatexFullTextTranslation", "Latex英译中": "LatexEnglishToChinese", "Markdown中译英": "MarkdownChineseToEnglish", "下载arxiv论文并翻译摘要": "DownloadArxivPaperAndTranslateAbstract", "下载arxiv论文翻译摘要": "DownloadArxivPaperTranslateAbstract", "连接网络回答问题": "ConnectToNetworkToAnswerQuestions", "联网的ChatGPT": "ChatGPTConnectedToNetwork", "解析任意code项目": "ParseAnyCodeProject", "读取知识库作答": "ReadKnowledgeArchiveAnswerQuestions", "知识库问答": "UpdateKnowledgeArchive", "同时问询_指定模型": "InquireSimultaneously_SpecifiedModel", "图片生成": "ImageGeneration", "test_解析ipynb文件": "Test_ParseIpynbFile", "把字符太少的块清除为回车": "ClearBlocksWithTooFewCharactersToNewline", "清理多余的空行": "CleanUpExcessBlankLines", "合并小写开头的段落块": "MergeLowercaseStartingParagraphBlocks", "多文件润色": "ProofreadMultipleFiles", "多文件翻译": "TranslateMultipleFiles", "解析docx": "ParseDocx", "解析PDF": "ParsePDF", "解析Paper": "ParsePaper", "ipynb解释": "IpynbExplanation", "解析源代码新": "ParsingSourceCodeNew", "避免代理网络产生意外污染": "Avoid unexpected pollution caused by proxy networks", "无": "None", "查询代理的地理位置": "Query the geographic location of the proxy", "返回的结果是": "The returned result is", "代理配置": "Proxy configuration", "代理所在地": "Location of the proxy", "未知": "Unknown", "IP查询频率受限": "IP query frequency is limited", "代理所在地查询超时": "Timeout when querying the location of the proxy", "代理可能无效": "Proxy may be invalid", "一键更新协议": "One-click protocol update", "备份和下载": "Backup and download", "覆盖和重启": "Overwrite and restart", "由于您没有设置config_private.py私密配置": "Since you have not set the config_private.py private configuration", "现将您的现有配置移动至config_private.py以防止配置丢失": "Now move your existing configuration to config_private.py to prevent configuration loss", "另外您可以随时在history子文件夹下找回旧版的程序": "In addition, you can always retrieve the old version of the program in the history subfolder", "代码已经更新": "Code has been updated", "即将更新pip包依赖……": "Will update pip package dependencies soon...", "pip包依赖安装出现问题": "Problem occurred during installation of pip package dependencies", "需要手动安装新增的依赖库": "Need to manually install the newly added dependency library", "然后在用常规的": "Then use the regular", "的方式启动": "way to start", "更新完成": "Update completed", "您可以随时在history子文件夹下找回旧版的程序": "You can always retrieve the old version of the program in the history subfolder", "5s之后重启": "<PERSON><PERSON> after 5 seconds", "假如重启失败": "If restart fails", "您可能需要手动安装新增的依赖库": "You may need to manually install new dependencies", "查询版本和用户意见": "Check version and user feedback", "新功能": "New features", "新版本可用": "New version available", "新版本": "New version", "当前版本": "Current version", "Github更新地址": "Github update address", "是否一键更新代码": "Update code with one click?", "Y+回车=确认": "Y+Enter=Confirm", "输入其他/无输入+回车=不更新": "Enter other/No input+Enter=No update", "更新失败": "Update failed", "自动更新程序": "Automatic update program", "已禁用": "Disabled", "正在执行一些模块的预热": "Some modules are being preheated", "模块预热": "Module preheating", "例如": "For example", "此key无效": "This key is invalid", "可同时填写多个API-KEY": "Multiple API-KEYs can be filled in at the same time", "用英文逗号分割": "Separated by commas", "改为True应用代理": "Change to True to apply proxy", "如果直接在海外服务器部署": "If deployed directly on overseas servers", "此处不修改": "Do not modify here", "填写格式是": "Format for filling in is", "协议": "Protocol", "地址": "Address", "端口": "Port", "填写之前不要忘记把USE_PROXY改成True": "Don't forget to change USE_PROXY to True before filling in", "常见协议无非socks5h/http": "Common protocols are nothing but socks5h/http", "例如 v2**y 和 ss* 的默认本地协议是socks5h": "For example, the default local protocol for v2**y and ss* is socks5h", "而cl**h 的默认本地协议是http": "While the default local protocol for cl**h is http", "懂的都懂": "Those who understand, understand", "不懂就填localhost或者127.0.0.1肯定错不了": "If you don't understand, just fill in localhost or 127.0.0.1 and you won't go wrong", "localhost意思是代理软件安装在本机上": "localhost means that the proxy software is installed on the local machine", "在代理软件的设置里找": "Look for it in the settings of the proxy software", "虽然不同的代理软件界面不一样": "Although the interface of different proxy software is different", "但端口号都应该在最显眼的位置上": "But the port number should be in the most prominent position", "代理网络的地址": "Address of the proxy network", "打开你的*学*网软件查看代理的协议": "Open your *learning* software to view the proxy protocol", "、地址": "and address", "和端口": "and port", "多线程函数插件中": "In the multi-threaded function plugin", "默认允许多少路线程同时访问OpenAI": "How many threads are allowed to access OpenAI at the same time by default", "Free trial users的限制是每分钟3次": "The limit for free trial users is 3 times per minute", "Pay-as-you-go users的限制是每分钟3500次": "The limit for Pay-as-you-go users is 3500 times per minute", "一言以蔽之": "In short", "免费用户填3": "Free users should fill in 3", "设置用户名和密码": "Set username and password", "相关功能不稳定": "Related functions are unstable", "与gradio版本和网络都相关": "Related to gradio version and network", "如果本地使用不建议加这个": "Not recommended to add this for local use", "重新URL重新定向": "Redirect URL", "实现更换API_URL的作用": "Realize the function of changing API_URL", "常规情况下": "Under normal circumstances", "不要修改!!": "Do not modify!!", "高危设置！通过修改此设置": "High-risk setting! By modifying this setting", "您将把您的API-KEY和对话隐私完全暴露给您设定的中间人！": "You will completely expose your API-KEY and conversation privacy to the middleman you set!", "如果需要在二级路径下运行": "If you need to run under the second-level path", "需要配合修改main.py才能生效!": "Need to be modified in conjunction with main.py to take effect!", "如果需要使用newbing": "If you need to use newbing", "把newbing的长长的cookie放到这里": "Put the long cookie of newbing here", "sk-此处填API密钥": "sk-Fill in API key here", "默认按钮颜色是 secondary": "The default button color is secondary", "前言": "Preface", "后语": "Postscript", "按钮颜色": "Button color", "预处理": "Preprocessing", "清除换行符": "Remove line breaks", "英语学术润色": "English academic polishing", "中文学术润色": "Chinese academic polishing", "查找语法错误": "Find syntax errors", "中译英": "Chinese to English translation", "学术中英互译": "Academic Chinese-English Translation", "英译中": "English to Chinese translation", "找图片": "Find image", "解释代码": "Explain code", "作为一名中文学术论文写作改进助理": "As a Chinese academic paper writing improvement assistant", "你的任务是改进所提供文本的拼写、语法、清晰、简洁和整体可读性": "Your task is to improve the spelling, grammar, clarity, conciseness and overall readability of the provided text", "同时分解长句": "Also, break down long sentences", "减少重复": "Reduce repetition", "并提供改进建议": "And provide improvement suggestions", "请只提供文本的更正版本": "Please only provide corrected versions of the text", "避免包括解释": "Avoid including explanations", "请编辑以下文本": "Please edit the following text", "翻译成地道的中文": "Translate into authentic Chinese", "我需要你找一张网络图片": "I need you to find a web image", "使用Unsplash API": "Use Unsplash API", "英语关键词": "English keywords", "获取图片URL": "Get image URL", "然后请使用Markdown格式封装": "Then please wrap it in Markdown format", "并且不要有反斜线": "And do not use backslashes", "不要用代码块": "Do not use code blocks", "现在": "Now", "请按以下描述给我发送图片": "Please send me the image following the description below", "请解释以下代码": "Please explain the following code", "HotReload 的意思是热更新": "HotReload means hot update", "修改函数插件后": "After modifying the function plugin", "不需要重启程序": "No need to restart the program", "代码直接生效": "The code takes effect directly", "第一组插件": "First group of plugins", "调用时": "When calling", "唤起高级参数输入区": "Invoke the advanced parameter input area", "默认False": "Default is False", "高级参数输入区的显示提示": "Display prompt in the advanced parameter input area", "加入下拉菜单中": "Add to the drop-down menu", "修改函数插件代码后": "After modifying the function plugin code", "第二组插件": "Second group of plugins", "经过充分测试": "Fully tested", "第三组插件": "Third group of plugins", "尚未充分测试的函数插件": "Function plugins that have not been fully tested yet", "放在这里": "Put it here", "第n组插件": "Nth group of plugins", "解析整个Python项目": "Parse the entire Python project", "先上传存档或输入路径": "Upload archive or enter path first", "请谨慎操作": "Please operate with caution", "测试功能": "Test function", "解析Jupyter Notebook文件": "Parse Jupyter Notebook files", "批量总结Word文档": "<PERSON><PERSON> summarize Word documents", "解析整个C++项目头文件": "Parse the entire C++ project header file", "解析整个C++项目": "Parse the entire C++ project", "解析整个Go项目": "Parse the entire Go project", "解析整个Rust项目": "Parse the entire Go project", "解析整个Java项目": "Parse the entire Java project", "解析整个前端项目": "Parse the entire front-end project", "css等": "CSS, etc.", "解析整个Lua项目": "Parse the entire Lua project", "解析整个CSharp项目": "Parse the entire C# project", "读Tex论文写摘要": "Read Tex paper and write abstract", "Markdown/Readme英译中": "Translate Markdown/Readme from English to Chinese", "保存当前的对话": "Save the current conversation", "多线程Demo": "Multithreading demo", "解析此项目本身": "Parse this project itself", "源码自译解": "Translate the source code", "老旧的Demo": "Old demo", "把本项目源代码切换成全英文": "Switch the source code of this project to English", "插件demo": "Plugin demo", "历史上的今天": "Today in history", "若输入0": "If 0 is entered", "则不解析notebook中的Markdown块": "Do not parse Markdown blocks in the notebook", "多线程": "Multithreading", "询问多个GPT模型": "Inquire multiple GPT models", "谷歌学术检索助手": "Google Scholar search assistant", "输入谷歌学术搜索页url": "Enter the URL of Google Scholar search page", "模仿ChatPDF": "Imitate ChatPDF", "英文Latex项目全文润色": "English Latex project full text proofreading", "输入路径或上传压缩包": "Input path or upload compressed package", "中文Latex项目全文润色": "Chinese Latex project full text proofreading", "Latex项目全文中译英": "Latex project full text translation from Chinese to English", "Latex项目全文英译中": "Latex project full text translation from English to Chinese", "批量MarkdownChineseToEnglish": "<PERSON><PERSON> Chinese to English", "一键DownloadArxivPaperAndTranslateAbstract": "One-click Download Arxiv Paper and Translate Abstract", "先在input输入编号": "Enter the number in input first", "如1812.10695": "e.g. 1812.10695", "先输入问题": "Enter the question first", "再点击按钮": "Then click the button", "需要访问谷歌": "Access to Google is required", "手动指定和筛选源代码文件类型": "Manually specify and filter the source code file type", "输入时用逗号隔开": "Separate with commas when entering", "*代表通配符": "* stands for wildcard", "加了^代表不匹配": "Adding ^ means not matching", "不输入代表全部匹配": "Not entering means matching all", "手动指定询问哪些模型": "Manually specify which models to ask", "支持任意数量的llm接口": "Support any number of llm interfaces", "用&符号分隔": "Separate with & symbol", "例如chatglm&gpt-3.5-turbo&api2d-gpt-4": "e.g. chatglm&gpt-3.5-turbo&api2d-gpt-4", "先切换模型到openai或api2d": "Switch the model to openai or api2d first", "在这里输入分辨率": "Enter the resolution here", "如1024x1024": "e.g. 1024x1024", "默认": "<PERSON><PERSON><PERSON>", "建议您复制一个config_private.py放自己的秘密": "We suggest you to copy a config_private.py file to keep your secrets, such as API and proxy URLs, from being accidentally uploaded to Github and seen by others.", "如API和代理网址": "Such as API and proxy URLs", "避免不小心传github被别人看到": "Avoid being accidentally uploaded to <PERSON><PERSON><PERSON> and seen by others", "如果WEB_PORT是-1": "If WEB_PORT is -1", "则随机选取WEB端口": "then a random port will be selected for WEB", "问询记录": "Inquiry record", "python 版本建议3.9+": "Python version recommended 3.9+", "越新越好": "The newer the better", "一些普通功能模块": "Some common functional modules", "高级函数插件": "Advanced function plugins", "处理markdown文本格式的转变": "Transformation of markdown text format", "做一些外观色彩上的调整": "Make some adjustments in appearance and color", "代理与自动更新": "Proxy and automatic update", "功能区显示开关与功能区的互动": "Interaction between display switch and function area", "整理反复出现的控件句柄组合": "Organize repeated control handle combinations", "提交按钮、重置按钮": "Submit button, reset button", "基础功能区的回调函数注册": "Registration of callback functions in basic function area", "文件上传区": "File upload area", "接收文件后与chatbot的互动": "Interaction with chatbot after receiving files", "函数插件-固定按钮区": "Function plugin - fixed button area", "函数插件-下拉菜单与随变按钮的互动": "Interaction between dropdown menu and dynamic button in function plugin", "是否唤起高级插件参数区": "Whether to call the advanced plugin parameter area", "随变按钮的回调函数注册": "Registration of callback functions for dynamic buttons", "终止按钮的回调函数注册": "Callback function registration for the stop button", "gradio的inbrowser触发不太稳定": "In-browser triggering of gradio is not very stable", "回滚代码到原始的浏览器打开函数": "Roll back code to the original browser open function", "打开浏览器": "Open browser", "ChatGPT 学术优化": "GPT Academic", "代码开源和更新": "Code open source and updates", "地址🚀": "Address 🚀", "感谢热情的": "Thanks to the enthusiastic", "开发者们❤️": "Developers ❤️", "请注意自我隐私保护哦！": "Please pay attention to self-privacy protection!", "当前模型": "Current model", "输入区": "Input area", "提交": "Submit", "重置": "Reset", "停止": "Stop", "清除": "Clear", "按Enter提交": "Submit by pressing Enter", "按Shift+Enter换行": "Press Shift+Enter to line break", "基础功能区": "Basic function area", "函数插件区": "Function plugin area", "注意": "Attention", "以下“红颜色”标识的函数插件需从输入区读取路径作为参数": "The function plugins marked in 'red' below need to read the path from the input area as a parameter", "更多函数插件": "More function plugins", "点击这里搜索插件列表": "Click Here to Search the Plugin List", "高级参数输入区": "Advanced parameter input area", "这里是特殊函数插件的高级参数输入区": "Here is the advanced parameter input area for special function plugins", "请先从插件列表中选择": "Please select from the plugin list first", "点击展开“文件上传区”": "Click to expand the 'file upload area'", "上传本地文件可供红色函数插件调用": "Upload local files for red function plugins to use", "任何文件": "Any file", "但推荐上传压缩文件": "But it is recommended to upload compressed files", "更换模型 & SysPrompt & 交互界面布局": "Change model & SysPrompt & interactive interface layout", "浮动输入区": "Floating input area", "输入清除键": "Input clear key", "插件参数区": "Plugin parameter area", "显示/隐藏功能区": "Show/hide function area", "更换LLM模型/请求源": "Change LLM model/request source", "备选输入区": "Alternative input area", "输入区2": "Input area 2", "已重置": "Reset", "插件": "Plugin", "的高级参数说明": "Advanced parameter description for plugin", "没有提供高级参数功能说明": "No advanced parameter function description provided", "不需要高级参数": "No advanced parameters needed", "如果浏览器没有自动打开": "If the browser does not open automatically", "请复制并转到以下URL": "Please copy and go to the following URL", "亮色主题": "Light theme", "暗色主题": "Dark theme", "一-鿿": "One-click", "GPT输出格式错误": "GPT output format error", "稍后可能需要再试一次": "May need to try again later", "gradio可用颜色列表": "Gradio available color list", "石板色": "Slate color", "灰色": "<PERSON>", "锌色": "Zinc color", "中性色": "Neutral color", "石头色": "Stone color", "红色": "Red", "橙色": "Orange", "琥珀色": "Amber", "黄色": "Yellow", "酸橙色": "Lime color", "绿色": "Green", "祖母绿": "Turquoise", "青蓝色": "<PERSON>an blue", "青色": "<PERSON><PERSON>", "天蓝色": "Sky blue", "蓝色": "Blue", "靛蓝色": "Indigo", "紫罗兰色": "Violet", "紫色": "Purple", "洋红色": "Ma<PERSON><PERSON>", "粉红色": "Pink", "玫瑰色": "<PERSON>", "添加一个萌萌的看板娘": "Add a cute mascot", "gradio版本较旧": "Gradio version is outdated", "不能自定义字体和颜色": "Cannot customize font and color", "引入一个有cookie的chatbot": "Introduce a chatbot with cookies", "刷新界面": "Refresh the page", "稍微留一点余地": "Leave a little room", "否则在回复时会因余量太少出问题": "Otherwise, there will be problems with insufficient space when replying", "这个bug没找到触发条件": "The trigger condition for this bug has not been found", "暂时先这样顶一下": "Temporarily handle it this way", "使用 lru缓存 加快转换速度": "Use LRU cache to speed up conversion", "输入了已经经过转化的字符串": "Input a string that has already been converted", "已经被转化过": "Has already been converted", "不需要再次转化": "No need to convert again", "有$标识的公式符号": "Formula symbol with $ sign", "且没有代码段": "And there is no code section", "的标识": "Identifier of", "排除了以上两个情况": "Exclude the above two cases", "我们": "We", "输入部分太自由": "The input part is too free", "预处理一波": "Preprocess it", "当代码输出半截的时候": "When the code output is halfway", "试着补上后个": "Try to fill in the latter", "第三方库": "Third-party library", "需要预先pip install rarfile": "Need to pip install rarfile in advance", "此外": "In addition", "Windows上还需要安装winrar软件": "WinRAR software needs to be installed on Windows", "配置其Path环境变量": "Configure its Path environment variable", "需要预先pip install py7zr": "Need to pip install py7zr in advance", "随机负载均衡": "Random load balancing", "优先级1. 获取环境变量作为配置": "Priority 1. Get environment variables as configuration", "读取默认值作为数据类型转换的参考": "Read the default value as a reference for data type conversion", "优先级2. 获取config_private中的配置": "Priority 2. Get the configuration in config_private", "优先级3. 获取config中的配置": "Priority 3. Get the configuration in config", "在读取API_KEY时": "When reading API_KEY", "检查一下是不是忘了改config": "Check if you forgot to change the config", "当输入部分的token占比小于限制的3/4时": "When the token proportion of the input part is less than 3/4 of the limit", "裁剪时": "When trimming", "1. 把input的余量留出来": "1. Leave the surplus of input", "2. 把输出用的余量留出来": "2. Leave the surplus used for output", "3. 如果余量太小了": "3. If the surplus is too small", "直接清除历史": "Clear the history directly", "当输入部分的token占比": "When the token proportion of the input part", "限制的3/4时": "is 3/4 of the limit", "截断时的颗粒度": "Granularity when truncating", "第一部分": "First part", "函数插件输入输出接驳区": "Function plugin input and output docking area", "带Cookies的Chatbot类": "Chatbot class with cookies", "为实现更多强大的功能做基础": "Laying the foundation for implementing more powerful functions", "装饰器函数": "Decorator function", "用于重组输入参数": "Used to restructure input parameters", "改变输入参数的顺序与结构": "Change the order and structure of input parameters", "刷新界面用 yield from update_ui": "Refresh the interface using yield from update_ui", "将插件中出的所有问题显示在界面上": "Display all questions from the plugin on the interface", "实现插件的热更新": "Implement hot update of the plugin", "打印traceback": "Print traceback", "为了安全而隐藏绝对地址": "Hide absolute address for security reasons", "正常": "Normal", "刷新用户界面": "Refresh the user interface", "在传递chatbot的过程中不要将其丢弃": "Do not discard it when passing the chatbot", "必要时": "If necessary", "可用clear将其清空": "It can be cleared with clear if necessary", "然后用for+append循环重新赋值": "Then reassign with for+append loop", "捕捉函数f中的异常并封装到一个生成器中返回": "Capture exceptions in function f and encapsulate them into a generator to return", "并显示到聊天当中": "And display it in the chat", "插件调度异常": "Plugin scheduling exception", "异常原因": "Exception reason", "当前代理可用性": "Current proxy availability", "异常": "Exception", "将文本按照段落分隔符分割开": "Split the text into paragraphs according to the paragraph separator", "生成带有段落标签的HTML代码": "Generate HTML code with paragraph tags", "用多种方式组合": "Combine in various ways", "将markdown转化为好看的html": "Convert markdown to nice-looking HTML", "接管gradio默认的markdown处理方式": "Take over the default markdown handling of gradio", "处理文件的上传": "Handle file uploads", "自动解压": "Automatically decompress", "将生成的报告自动投射到文件上传区": "Automatically project the generated report to the file upload area", "当历史上下文过长时": "Automatically truncate when the historical context is too long", "自动截断": "Automatic truncation", "获取设置": "Get settings", "根据当前的模型类别": "According to the current model category", "抽取可用的api-key": "Extract available API keys", "* 此函数未来将被弃用": "* This function will be deprecated in the future", "不详": "Unknown", "将对话记录history以Markdown格式写入文件中": "Write the conversation record history to a file in Markdown format", "如果没有指定文件名": "If no file name is specified", "则使用当前时间生成文件名": "Generate a file name using the current time", "chatGPT分析报告": "chatGPT analysis report", "chatGPT 分析报告": "chatGPT analysis report", "以上材料已经被写入": "The above materials have been written", "向chatbot中添加错误信息": "Add error information to the chatbot", "将Markdown格式的文本转换为HTML格式": "Convert Markdown format text to HTML format", "如果包含数学公式": "If it contains mathematical formulas", "则先将公式转换为HTML格式": "Convert the formula to HTML format first", "解决一个mdx_math的bug": "Fix a bug in mdx_math", "单$包裹begin命令时多余": "Redundant when wrapping begin command with single $", "在gpt输出代码的中途": "In the middle of outputting code with GPT", "输出了前面的": "Output the front part", "但还没输出完后面的": "But haven't output the back part yet", "补上后面的": "Complete the back part", "GPT模型返回的回复字符串": "Reply string returned by GPT model", "返回一个新的字符串": "Return a new string", "将输出代码片段的“后面的": "Append the back part of output code snippet", "”补上": "to it", "将输入和输出解析为HTML格式": "Parse input and output as HTML format", "将y中最后一项的输入部分段落化": "Paragraphize the input part of the last item in y", "并将输出部分的Markdown和数学公式转换为HTML格式": "And convert the output part of Markdown and math formulas to HTML format", "返回当前系统中可用的未使用端口": "Return an available unused port in the current system", "需要安装pip install rarfile来解压rar文件": "Need to install pip install rarfile to extract rar files", "需要安装pip install py7zr来解压7z文件": "Need to install pip install py7zr to extract 7z files", "当文件被上传时的回调函数": "Callback function when a file is uploaded", "我上传了文件": "I uploaded a file", "请查收": "Please check", "收到以下文件": "Received the following files", "调用路径参数已自动修正到": "The call path parameter has been automatically corrected to", "现在您点击任意“红颜色”标识的函数插件时": "Now when you click any function plugin with a 'red' label", "以上文件将被作为输入参数": "The above files will be used as input parameters", "汇总报告如何远程获取": "How to remotely access the summary report", "汇总报告已经添加到右侧“文件上传区”": "The summary report has been added to the 'file upload area' on the right", "可能处于折叠状态": "It may be in a collapsed state", "检测到": "Detected", "个": "items", "您提供的api-key不满足要求": "The api-key you provided does not meet the requirements", "不包含任何可用于": "Does not contain any that can be used for", "的api-key": "api-key", "您可能选择了错误的模型或请求源": "You may have selected the wrong model or request source", "环境变量可以是": "Environment variables can be", "优先": "preferred", "也可以直接是": "or can be directly", "例如在windows cmd中": "For example, in windows cmd", "既可以写": "it can be written as", "也可以写": "or as", "尝试加载": "Attempting to load", "默认值": "Default value", "修正值": "Corrected value", "环境变量": "Environment variable", "不支持通过环境变量设置!": "Setting through environment variables is not supported!", "加载失败!": "Loading failed!", "如": " e.g., ", "成功读取环境变量": "Successfully read environment variable: ", "本项目现已支持OpenAI和API2D的api-key": "This project now supports api-keys for OpenAI and API2D", "也支持同时填写多个api-key": "It also supports filling in multiple api-keys at the same time", "您既可以在config.py中修改api-key": "You can modify the api-key in config.py", "也可以在问题输入区输入临时的api-key": "You can also enter a temporary api-key in the question input area", "然后回车键提交后即可生效": "After submitting with the enter key, it will take effect", "您的 API_KEY 是": "Your API_KEY is", "*** API_KEY 导入成功": "*** API_KEY imported successfully", "请在config文件中修改API密钥之后再运行": "Please modify the API key in the config file before running", "网络代理状态": "Network proxy status", "未配置": "Not configured", "无代理状态下很可能无法访问OpenAI家族的模型": "", "建议": "Suggestion", "检查USE_PROXY选项是否修改": "Check if the USE_PROXY option has been modified", "已配置": "Configured", "配置信息如下": "Configuration information is as follows", "proxies格式错误": "Proxies format error", "请注意proxies选项的格式": "Please note the format of the proxies option", "不要遗漏括号": "Do not miss the parentheses", "这段代码定义了一个名为DummyWith的空上下文管理器": "This code defines an empty context manager named <PERSON><PERSON><PERSON><PERSON>", "它的作用是……额……就是不起作用": "Its purpose is...um...to not do anything", "即在代码结构不变得情况下取代其他的上下文管理器": "That is, to replace other context managers without changing the code structure", "上下文管理器是一种Python对象": "Context managers are a type of Python object", "用于与with语句一起使用": "Used in conjunction with the with statement", "以确保一些资源在代码块执行期间得到正确的初始化和清理": "To ensure that some resources are properly initialized and cleaned up during code block execution", "上下文管理器必须实现两个方法": "Context managers must implement two methods", "分别为 __enter__": "They are __enter__", "和 __exit__": "and __exit__", "在上下文执行开始的情况下": "At the beginning of the context execution", "方法会在代码块被执行前被调用": "The method is called before the code block is executed", "而在上下文执行结束时": "While at the end of the context execution", "方法则会被调用": "The method is called", "把gradio的运行地址更改到指定的二次路径上": "Change the running address of Gradio to the specified secondary path", "通过裁剪来缩短历史记录的长度": "Shorten the length of the history by trimming", "此函数逐渐地搜索最长的条目进行剪辑": "This function gradually searches for the longest entry to clip", "直到历史记录的标记数量降低到阈值以下": "Until the number of history markers is reduced to below the threshold", "应急食品是“原神”游戏中的角色派蒙的外号": "Emergency Food is the nickname of the character <PERSON><PERSON><PERSON> in the game Genshin Impact", "安全第一条": "Safety first", "后面两句是": "The next two sentences are", "亲人两行泪": "Two lines of tears for loved ones", "test_解析一个Cpp项目": "test_Parse a Cpp project", "test_联网回答问题": "test_Answer questions online", "这是什么": "What is this?", "这个文件用于函数插件的单元测试": "This file is used for unit testing of function plugins", "运行方法 python crazy_functions/crazy_functions_test.py": "Run the command 'python crazy_functions/crazy_functions_test.py'", "AutoGPT是什么": "What is AutoGPT?", "当前问答": "Current Q&A", "程序完成": "Program completed", "回车退出": "Press Enter to exit", "退出": "Exit", "当 输入部分的token占比 小于 全文的一半时": "When the proportion of tokens in the input part is less than half of the entire text", "只裁剪历史": "Trim only history", "用户反馈": "User feedback", "第一种情况": "First scenario", "顺利完成": "Completed smoothly", "第二种情况": "Second scenario", "Token溢出": "Token overflow", "选择处理": "Choose processing", "尝试计算比例": "Attempt to calculate ratio", "尽可能多地保留文本": "Retain text as much as possible", "返回重试": "Return and retry", "选择放弃": "Choose to give up", "放弃": "Give up", "第三种情况": "Third scenario", "其他错误": "Other errors", "重试几次": "Retry several times", "提交任务": "Submit task", "yield一次以刷新前端页面": "Yield once to refresh the front-end page", "“喂狗”": "Feed the dog", "看门狗": "Watchdog", "如果最后成功了": "If successful in the end", "则删除报错信息": "Delete error message", "读取配置文件": "Read configuration file", "屏蔽掉 chatglm的多线程": "Disable chatglm's multi-threading", "可能会导致严重卡顿": "May cause serious lag", "跨线程传递": "Cross-thread communication", "子线程任务": "Sub-thread task", "也许等待十几秒后": "Perhaps after waiting for more than ten seconds", "情况会好转": "The situation will improve", "开始重试": "Start retrying", "异步任务开始": "Asynchronous task starts", "更好的UI视觉效果": "Better UI visual effects", "每个线程都要“喂狗”": "Each thread needs to \"feed the dog\"", "在前端打印些好玩的东西": "Print some fun things in the front end", "异步任务结束": "Asynchronous task ends", "是否在结束时": "Whether to display the result on the interface when ending", "在界面上显示结果": "Display the result on the interface", "递归": "Recursion", "列表递归接龙": "List recursion chaining", "第1次尝试": "1st attempt", "将双空行": "Use double blank lines as splitting points", "作为切分点": "As a splitting point", "第2次尝试": "2nd attempt", "将单空行": "Use single blank lines", "第3次尝试": "3rd attempt", "将英文句号": "Use English periods", "这个中文的句号是故意的": "This Chinese period is intentional", "作为一个标识而存在": "Exists as an identifier", "第4次尝试": "4th attempt", "将中文句号": "Chinese period", "第5次尝试": "5th attempt", "没办法了": "No other way", "随便切一下敷衍吧": "Cut it randomly and perfunctorily", "Index 0 文本": "Index 0 Text", "Index 1 字体": "Index 1 Font", "Index 2 框框": "Index 2 Box", "是否丢弃掉 不是正文的内容": "Whether to discard non-main text content", "比正文字体小": "Smaller than main text font", "如参考文献、脚注、图注等": "Such as references, footnotes, captions, etc.", "小于正文的": "Less than main text", "时": "When", "判定为不是正文": "Determined as non-main text", "有些文章的正文部分字体大小不是100%统一的": "In some articles, the font size of the main text is not 100% consistent", "有肉眼不可见的小变化": "Small changes invisible to the naked eye", "第 1 步": "Step 1", "搜集初始信息": "Collect initial information", "获取页面上的文本信息": "Get text information on the page", "块元提取": "Block element extraction", "第 2 步": "Step 2", "获取正文主字体": "Get main text font", "第 3 步": "Step 3", "切分和重新整合": "Split and reassemble", "尝试识别段落": "Attempt to identify paragraphs", "单行 + 字体大": "Single line + Large font", "尝试识别section": "Attempt to recognize section", "第 4 步": "Step 4", "乱七八糟的后处理": "Messy post-processing", "清除重复的换行": "Remove duplicate line breaks", "换行 -": "Line break -", "双换行": "Double line break", "第 5 步": "Step 5", "展示分割效果": "Display segmentation effect", "网络的远程文件": "Remote file on the network", "直接给定文件": "Directly given file", "本地路径": "Local path", "递归搜索": "Recursive search", "请求GPT模型同时维持用户界面活跃": "Request GPT model while keeping the user interface active", "输入参数 Args": "Input parameter Args", "以_array结尾的输入变量都是列表": "Input variables ending in _array are all lists", "列表长度为子任务的数量": "The length of the list is the number of sub-tasks", "执行时": "When executing", "会把列表拆解": "The list will be broken down", "放到每个子线程中分别执行": "And executed separately in each sub-thread", "输入": "Input", "展现在报告中的输入": "Input displayed in the report", "借助此参数": "With the help of this parameter", "在汇总报告中隐藏啰嗦的真实输入": "Hide verbose real input in the summary report", "增强报告的可读性": "Enhance the readability of the report", "GPT参数": "GPT parameters", "浮点数": "Floating point number", "用户界面对话窗口句柄": "Handle of the user interface dialog window", "用于数据流可视化": "Used for data flow visualization", "历史": "History", "对话历史列表": "List of conversation history", "系统输入": "System input", "列表": "List", "用于输入给GPT的前提提示": "Prompt for input to GPT", "比如你是翻译官怎样怎样": "For example, if you are a translator, how to...", "刷新时间间隔频率": "Refresh time interval frequency", "建议低于1": "Suggested to be less than 1", "不可高于3": "Cannot be higher than 3", "仅仅服务于视觉效果": "Only serves for visual effects", "是否自动处理token溢出的情况": "Whether to automatically handle token overflow", "如果选择自动处理": "If selected to handle automatically", "则会在溢出时暴力截断": "It will be forcefully truncated when overflow occurs", "默认开启": "Default enabled", "失败时的重试次数": "Number of retries when failed", "输出 Returns": "Output Returns", "输出": "Output", "GPT返回的结果": "<PERSON><PERSON>t returned by GPT", "检测到程序终止": "Program termination detected", "警告": "Warning", "文本过长将进行截断": "Text will be truncated if too long", "Token溢出数": "Token overflow count", "在执行过程中遭遇问题": "Encountered a problem during execution", "重试中": "Retrying", "请稍等": "Please wait", "请求GPT模型的": "Requesting GPT model", "版": "version", "具备以下功能": "Features include", "实时在UI上反馈远程数据流": "Real-time feedback of remote data streams on UI", "使用线程池": "Using thread pool", "可调节线程池的大小避免openai的流量限制错误": "The size of the thread pool can be adjusted to avoid openai traffic limit errors", "处理中途中止的情况": "Handling mid-process interruptions", "网络等出问题时": "When there are network issues", "会把traceback和已经接收的数据转入输出": "Traceback and received data will be outputted", "每个子任务的输入": "Input for each subtask", "每个子任务展现在报告中的输入": "Input displayed in the report for each subtask", "llm_kwargs参数": "llm_kwargs parameter", "历史对话输入": "Historical conversation input", "双层列表": "Double-layer list", "第一层列表是子任务分解": "The first layer of the list is the decomposition of subtasks", "第二层列表是对话历史": "The second layer of the list is the conversation history", "最大线程数": "Maximum number of threads", "如果子任务非常多": "If there are many subtasks", "需要用此选项防止高频地请求openai导致错误": "Use this option to prevent frequent requests to OpenAI that may cause errors", "数据流的显示最后收到的多少个字符": "Display the last few characters received in the data stream", "是否在输入过长时": "Automatically truncate text when input is too long", "自动缩减文本": "Automatically shorten the text", "在结束时": "At the end", "把完整输入-输出结果显示在聊天框": "Display the complete input-output results in the chat box", "子任务失败时的重试次数": "Number of retries when a subtask fails", "每个子任务的输出汇总": "Summary of output for each subtask", "如果某个子任务出错": "If a subtask encounters an error", "response中会携带traceback报错信息": "Traceback error information will be included in the response", "方便调试和定位问题": "Facilitate debugging and problem locating", "请开始多线程操作": "Please start multi-threaded operation", "等待中": "Waiting", "执行中": "Executing", "已成功": "Successful", "截断重试": "Truncated retry", "线程": "<PERSON><PERSON><PERSON>", "此线程失败前收到的回答": "Answer received by this thread before failure", "输入过长已放弃": "Input is too long and has been abandoned", "OpenAI绑定信用卡可解除频率限制": "Binding a credit card to OpenAI can remove frequency restrictions", "等待重试": "Waiting for retry", "已失败": "Failed", "多线程操作已经开始": "Multi-threaded operation has started", "完成情况": "Completion status", "存在一行极长的文本！": "There is an extremely long line of text!", "当无法用标点、空行分割时": "When punctuation and blank lines cannot be used for separation", "我们用最暴力的方法切割": "We use the most brutal method to cut", "Tiktoken未知错误": "<PERSON>iktok unknown error", "这个函数用于分割pdf": "This function is used to split PDF", "用了很多trick": "Used a lot of tricks", "逻辑较乱": "The logic is messy", "效果奇好": "The effect is very good", "**输入参数说明**": "**Input Parameter Description**", "需要读取和清理文本的pdf文件路径": "The path of the PDF file that needs to be read and cleaned", "**输出参数说明**": "**Output Parameter Description**", "清理后的文本内容字符串": "Cleaned text content string", "第一页清理后的文本内容列表": "List of cleaned text content on the first page", "**函数功能**": "**Functionality**", "读取pdf文件并清理其中的文本内容": "Read the PDF file and clean its text content", "清理规则包括": "Cleaning rules include", "提取所有块元的文本信息": "Extract text information from all block elements", "并合并为一个字符串": "And merge into one string", "去除短块": "Remove short blocks", "字符数小于100": "Character count is less than 100", "并替换为回车符": "And replace with a carriage return", "合并小写字母开头的段落块并替换为空格": "Merge paragraph blocks that start with lowercase letters and replace with spaces", "将每个换行符替换为两个换行符": "Replace each line break with two line breaks", "使每个段落之间有两个换行符分隔": "Separate each paragraph with two line breaks", "提取文本块主字体": "Main font of extracted text block", "提取字体大小是否近似相等": "Whether the font sizes of extracted text are approximately equal", "这个函数是用来获取指定目录下所有指定类型": "This function is used to get all files of a specified type in a specified directory", "如.md": "such as .md", "的文件": "files", "并且对于网络上的文件": "and for files on the internet", "也可以获取它": "it can also be obtained", "下面是对每个参数和返回值的说明": "Below are explanations for each parameter and return value", "参数": "Parameters", "路径或网址": "Path or URL", "表示要搜索的文件或者文件夹路径或网络上的文件": "Indicates the file or folder path to be searched or the file on the internet", "字符串": "String", "表示要搜索的文件类型": "Indicates the file type to be searched", "默认是.md": "default is .md", "返回值": "Return value", "布尔值": "Boolean value", "表示函数是否成功执行": "Indicates whether the function is executed successfully", "文件路径列表": "List of file paths", "里面包含以指定类型为后缀名的所有文件的绝对路径": "Contains the absolute paths of all files with the specified type as the suffix", "表示文件所在的文件夹路径": "Indicates the folder path where the file is located", "如果是网络上的文件": "If it is a file on the internet", "就是临时文件夹的路径": "it is the path of the temporary folder", "该函数详细注释已添加": "Detailed comments for this function have been added", "请确认是否满足您的需要": "Please confirm if it meets your needs", "读取Latex文件": "Read Latex file", "删除其中的所有注释": "Remove all comments from it", "定义注释的正则表达式": "Define the regular expression of comments", "使用正则表达式查找注释": "Use regular expressions to find comments", "并替换为空字符串": "And replace them with an empty string", "记录删除注释后的文本": "Record the text after removing comments", "拆分过长的latex文件": "Split long latex files", "抽取摘要": "Extract abstract", "单线": "Single line", "获取文章meta信息": "Get article meta information", "多线程润色开始": "Multithreading polishing begins", "并行任务数量限制": "Parallel task number limit", "最多同时执行5个": "Up to 5 can be executed at the same time", "其他的排队等待": "Others are queued and waiting", "整理结果": "Organize the results", "基本信息": "Basic information", "功能、贡献者": "Function, contributor", "尝试导入依赖": "Attempt to import dependencies", "如果缺少依赖": "If dependencies are missing", "则给出安装建议": "Give installation suggestions", "清空历史": "Clear history", "以免输入溢出": "To avoid input overflow", "将长文本分离开来": "Separate long text", "以下是一篇学术论文中的一段内容": "The following is a paragraph from an academic paper", "请将此部分润色以满足学术标准": "Please polish this section to meet academic standards", "提高语法、清晰度和整体可读性": "Improve grammar, clarity, and overall readability", "不要修改任何LaTeX命令": "Do not modify any LaTeX commands", "例如\\section": "such as \\section", "\\cite和方程式": "\\cite and equations", "润色": "Polishing", "你是一位专业的中文学术论文作家": "You are a professional Chinese academic paper writer", "完成了吗": "Are you done?", "函数插件功能": "Function plugin feature", "对整个Latex项目进行润色": "Polish the entire Latex project", "函数插件贡献者": "Function plugin contributor", "解析项目": "Parsing project", "导入软件依赖失败": "Failed to import software dependencies", "使用该模块需要额外依赖": "Using this module requires additional dependencies", "安装方法": "Installation method", "空空如也的输入栏": "Empty input field", "找不到本地项目或无权访问": "Cannot find local project or do not have access", "找不到任何.tex文件": "Cannot find any .tex files", "OpenAI所允许的最大并行过载": "Maximum parallel overload allowed by OpenAI", "翻译": "Translation", "对整个Latex项目进行翻译": "Translate the entire Latex project", "提取摘要": "Extract abstract", "下载PDF文档": "Download PDF document", "翻译摘要等": "Translate abstract, etc.", "写入文件": "Writing to file", "重置文件的创建时间": "Resetting file creation time", "下载编号": "Download number", "自动定位": "Auto-locating", "不能识别的URL！": "Unrecognized URL!", "下载中": "Downloading", "下载完成": "Download complete", "正在获取文献名！": "Getting article name!", "年份获取失败": "Failed to get year", "authors获取失败": "Failed to get authors", "获取成功": "Successfully retrieved", "函数插件作者": "Function plugin author", "正在提取摘要并下载PDF文档……": "Extracting abstract and downloading PDF document...", "下载pdf文件未成功": "PDF file download unsuccessful", "请你阅读以下学术论文相关的材料": "Please read the following academic paper related materials", "翻译为中文": "Translate to Chinese", "材料如下": "Materials are as follows", "论文": "Paper", "PDF文件也已经下载": "PDF file has also been downloaded", "剩下的情况都开头除去": "Remove the beginning of the remaining situation", "结尾除去一次": "Remove the end once", "第1步": "Step 1", "第2步": "Step 2", "第3步": "Step 3", "集合文件": "Collection file", "第4步": "Step 4", "随便显示点什么防止卡顿的感觉": "Display something randomly to prevent lagging", "第5步": "Step 5", "Token限制下的截断与处理": "Truncation and processing under Token restriction", "第6步": "Step 6", "任务函数": "Task function", "分解代码文件": "Decompose code files", "第7步": "Step 7", "所有线程同时开始执行任务函数": "All threads start executing task functions simultaneously", "第8步": "Step 8", "循环轮询各个线程是否执行完毕": "Loop and poll whether each thread has finished executing", "第9步": "Step 9", "把结果写入文件": "Write the results to a file", "这里其实不需要join了": "Join is not needed here", "肯定已经都结束了": "They must have all finished", "失败": "Failure", "第10步": "Step 10", "备份一个文件": "Backup a file", "接下来请将以下代码中包含的所有中文转化为英文": "Please translate all Chinese in the following code into English", "只输出转化后的英文代码": "Output only the translated English code", "请用代码块输出代码": "Please output the code using code blocks", "等待多线程操作": "Waiting for multi-threaded operations", "中间过程不予显示": "Intermediate processes will not be displayed", "聊天显示框的句柄": "Chat display box handle", "用于显示给用户": "Displayed to the user", "聊天历史": "Chat history", "前情提要": "Context summary", "给gpt的静默提醒": "Silent reminder to GPT", "当前软件运行的端口号": "Current software running port number", "这是什么功能": "What is this function", "生成图像": "Generate image", "请先把模型切换至gpt-xxxx或者api2d-xxxx": "Please switch the model to gpt-xxxx or api2d-xxxx first", "如果中文效果不理想": "If the Chinese effect is not ideal", "尝试Prompt": "Try Prompt", "正在处理中": "Processing", "图像中转网址": "Image transfer URL", "中转网址预览": "Transfer URL preview", "本地文件地址": "Local file address", "本地文件预览": "Local file preview", "chatGPT对话历史": "ChatGPT conversation history", "对话历史": "Conversation history", "对话历史写入": "Conversation history written", "存档文件详情": "Archive file details", "载入对话": "Load conversation", "条": "条", "上下文": "Context", "保存当前对话": "Save current conversation", "您可以调用“LoadConversationHistoryArchive”还原当下的对话": "You can call 'LoadConversationHistoryArchive' to restore the current conversation", "警告！被保存的对话历史可以被使用该系统的任何人查阅": "Warning! The saved conversation history can be viewed by anyone using this system", "正在查找对话历史文件": "Looking for conversation history file", "html格式": "HTML format", "找不到任何html文件": "No HTML files found", "但本地存储了以下历史文件": "But the following history files are stored locally", "您可以将任意一个文件路径粘贴到输入区": "You can paste any file path into the input area", "然后重试": "and try again", "载入对话历史文件": "Load conversation history file", "对话历史文件损坏！": "Conversation history file is corrupted!", "删除所有历史对话文件": "Delete all history conversation files", "已删除": "Deleted", "pip install python-docx 用于docx格式": "pip install python-docx for docx format", "跨平台": "Cross-platform", "pip install pywin32 用于doc格式": "pip install pywin32 for doc format", "仅支持Win平台": "Only supports Win platform", "打开文件": "Open file", "rar和7z格式正常": "RAR and 7z formats are normal", "故可以只分析文章内容": "So you can only analyze the content of the article", "不输入文件名": "Do not enter the file name", "已经对该文章的所有片段总结完毕": "All segments of the article have been summarized", "如果文章被切分了": "If the article is cut into pieces", "检测输入参数": "Checking input parameters", "如没有给定输入参数": "If no input parameters are given", "直接退出": "Exit directly", "搜索需要处理的文件清单": "Search for the list of files to be processed", "如果没找到任何文件": "If no files are found", "开始正式执行任务": "Start executing the task formally", "请对下面的文章片段用中文做概述": "Please summarize the following article fragment in Chinese", "文章内容是": "The content of the article is", "请对下面的文章片段做概述": "Please summarize the following article fragment", "的第": "The", "个片段": "fragment", "总结文章": "Summarize the article", "根据以上的对话": "According to the conversation above", "的主要内容": "The main content of", "所有文件都总结完成了吗": "Are all files summarized?", "如果是.doc文件": "If it is a .doc file", "请先转化为.docx格式": "Please convert it to .docx format first", "找不到任何.docx或doc文件": "Cannot find any .docx or .doc files", "读取Markdown文件": "Read Markdown file", "拆分过长的Markdown文件": "Split overlong Markdown file", "什么都没有": "Nothing at all", "对整个Markdown项目进行翻译": "Translate the entire Markdown project", "找不到任何.md文件": "Cannot find any .md files", "句子结束标志": "End of sentence marker", "尽量是完整的一个section": "Try to use a complete section", "比如introduction": "such as introduction", "experiment等": "experiment, etc.", "必要时再进行切割": "cut if necessary", "的长度必须小于 2500 个 Token": "its length must be less than 2500 tokens", "尝试": "try", "按照章节切割PDF": "cut PDF by sections", "从摘要中提取高价值信息": "extract high-value information from the abstract", "放到history中": "put it in history", "迭代地历遍整个文章": "iterate through the entire article", "提取精炼信息": "extract concise information", "用户提示": "user prompt", "初始值是摘要": "initial value is the abstract", "i_say=真正给chatgpt的提问": "i_say=questions actually asked to chatgpt", "i_say_show_user=给用户看的提问": "i_say_show_user=questions shown to the user", "迭代上一次的结果": "iterate over the previous result", "提示": "prompt", "整理history": "organize history", "接下来两句话只显示在界面上": "the next two sentences are only displayed on the interface", "不起实际作用": "do not have an actual effect", "设置一个token上限": "set a token limit", "防止回答时Token溢出": "prevent token overflow when answering", "注意这里的历史记录被替代了": "note that the history record here has been replaced", "首先你在英文语境下通读整篇论文": "First, read the entire paper in an English context", "收到": "Received", "文章极长": "Article is too long", "不能达到预期效果": "Cannot achieve expected results", "接下来": "Next", "你是一名专业的学术教授": "You are a professional academic professor", "利用以上信息": "Utilize the above information", "使用中文回答我的问题": "Answer my questions in Chinese", "理解PDF论文内容": "Understand the content of a PDF paper", "并且将结合上下文内容": "And will combine with the context", "进行学术解答": "Provide academic answers", "请对下面的程序文件做一个概述": "Please provide an overview of the program file below", "并对文件中的所有函数生成注释": "And generate comments for all functions in the file", "使用markdown表格输出结果": "Output the results using markdown tables", "文件内容是": "The file content is", "在此处替换您要搜索的关键词": "Replace the keywords you want to search here", "爬取搜索引擎的结果": "Crawl the results of search engines", "依次访问网页": "Visit web pages in order", "最多收纳多少个网页的结果": "Include results from how many web pages at most", "ChatGPT综合": "ChatGPT synthesis", "裁剪输入": "Trim the input", "从最长的条目开始裁剪": "Start trimming from the longest entry", "防止爆token": "Prevent token explosion", "无法连接到该网页": "Cannot connect to the webpage", "请结合互联网信息回答以下问题": "Please answer the following questions based on internet information", "请注意": "Please note", "您正在调用一个": "You are calling a", "函数插件": "function plugin", "的模板": "template", "该模板可以实现ChatGPT联网信息综合": "This template can achieve ChatGPT network information integration", "该函数面向希望实现更多有趣功能的开发者": "This function is aimed at developers who want to implement more interesting features", "它可以作为创建新功能函数的模板": "It can be used as a template for creating new feature functions", "您若希望分享新的功能模组": "If you want to share new feature modules", "请不吝PR！": "Please don't hesitate to PR!", "第": "The", "份搜索结果": "search results", "从以上搜索结果中抽取信息": "Extract information from the above search results", "然后回答问题": "Then answer the question", "请从给定的若干条搜索结果中抽取信息": "Please extract information from the given search results", "对最相关的两个搜索结果进行总结": "Summarize the two most relevant search results", "拆分过长的IPynb文件": "Splitting overly long IPynb files", "的分析如下": "analysis is as follows", "解析的结果如下": "The parsing result is as follows", "对IPynb文件进行解析": "Parse the IPynb file", "找不到任何.ipynb文件": "Cannot find any .ipynb files", "第一步": "Step one", "逐个文件分析": "Analyze each file", "读取文件": "Read the file", "装载请求内容": "Load the request content", "文件读取完成": "File reading completed", "对每一个源代码文件": "For each source code file", "生成一个请求线程": "Generate a request thread", "发送到chatgpt进行分析": "Send to chatgpt for analysis", "全部文件解析完成": "All files parsed", "结果写入文件": "Write results to file", "准备对工程源代码进行汇总分析": "Prepare to summarize and analyze project source code", "第二步": "Step two", "综合": "Synthesis", "单线程": "Single thread", "分组+迭代处理": "Grouping + iterative processing", "10个文件为一组": "10 files per group", "只保留文件名节省token": "Keep only file names to save tokens", "裁剪input": "Trim input", "迭代之前的分析": "Analysis before iteration", "将要匹配的模式": "Pattern to match", "不输入即全部匹配": "Match all if not input", "将要忽略匹配的文件后缀": "File suffixes to ignore in matching", "避免解析压缩文件": "Avoid parsing compressed files", "将要忽略匹配的文件名": "File names to ignore in matching", "生成正则表达式": "Generate regular expression", "若上传压缩文件": "If uploading compressed files", "先寻找到解压的文件夹路径": "First find the path of the decompressed folder", "从而避免解析压缩文件": "Thus avoid parsing compressed files", "按输入的匹配模式寻找上传的非压缩文件和已解压的文件": "Find uncompressed and decompressed files uploaded according to the input matching pattern", "源文件太多": "Too many source files", "超过512个": "Exceeds 512", "请缩减输入文件的数量": "Please reduce the number of input files", "或者": "Or", "您也可以选择删除此行警告": "You can also choose to delete this line of warning", "并修改代码拆分file_manifest列表": "And modify the code to split the file_manifest list", "从而实现分批次处理": "To achieve batch processing", "接下来请你逐文件分析下面的工程": "Next, please analyze the following project file by file", "请对下面的程序文件做一个概述文件名是": "Please give an overview of the following program files, the file name is", "你是一个程序架构分析师": "You are a program architecture analyst", "正在分析一个源代码项目": "Analyzing a source code project", "你的回答必须简单明了": "Your answer must be concise and clear", "完成": "Completed", "逐个文件分析已完成": "Analysis of each file has been completed", "正在开始汇总": "Starting to summarize", "用一张Markdown表格简要描述以下文件的功能": "Briefly describe the functions of the following files in a Markdown table", "根据以上分析": "Based on the above analysis", "用一句话概括程序的整体功能": "Summarize the overall function of the program in one sentence", "对程序的整体功能和构架重新做出概括": "Redescribe the overall function and architecture of the program", "由于输入长度限制": "Due to input length limitations", "可能需要分组处理": "Group processing may be required", "本组文件为": "This group of files is", "+ 已经汇总的文件组": "+ Files group already summarized", "正在分析一个项目的源代码": "Analyzing source code of a project", "找不到任何python文件": "No Python files found", "找不到任何.h头文件": "No .h header files found", "找不到任何java文件": "No Java files found", "找不到任何前端相关文件": "No front-end related files found", "找不到任何golang文件": "No Golang files found", "找不到任何rust文件": "No Rust files found", "找不到任何lua文件": "No Lua files found", "找不到任何CSharp文件": "No CSharp files found", "找不到任何文件": "No files found", "正在同时咨询ChatGPT和ChatGLM……": "Consulting ChatGPT and ChatGLM simultaneously...", "发送 GET 请求": "Sending GET request", "解析网页内容": "Parsing webpage content", "获取所有文章的标题和作者": "Getting titles and authors of all articles", "引用次数是链接中的文本": "The number of citations is in the link text", "直接取出来": "Take it out directly", "摘要在 .gs_rs 中的文本": "The summary is in the .gs_rs text", "需要清除首尾空格": "Need to remove leading and trailing spaces", "是否在arxiv中": "Is it in arxiv?", "不在arxiv中无法获取完整摘要": "Cannot get complete summary if it is not in arxiv", "分析用户提供的谷歌学术": "Analyzing Google Scholar provided by the user", "搜索页面中": "In the search page", "出现的所有文章": "All articles that appear", "插件初始化中": "Plugin initializing", "下面是一些学术文献的数据": "Below are some academic literature data", "当你想发送一张照片时": "When you want to send a photo", "使用 Unsplash API": "Use Unsplash API", "匹配^数字^": "Match ^number^", "将匹配到的数字作为替换值": "Replace the matched number as the replacement value", "替换操作": "Replacement operation", "质能方程式": "Mass-energy equivalence equation", "知乎": "<PERSON><PERSON><PERSON>", "你好": "Hello", "这是必应": "This is Bing", "质能方程是描述质量与能量之间的当量关系的方程": "The mass-energy equivalence equation describes the equivalent relationship between mass and energy", "用tex格式": "In tex format", "质能方程可以写成$$E=mc^2$$": "The mass-energy equivalence equation can be written as $$E=mc^2$$", "其中$E$是能量": "Where $E$ is energy", "$m$是质量": "$m$ is mass", "$c$是光速": "$c$ is the speed of light", "Endpoint 重定向": "Endpoint redirection", "兼容旧版的配置": "Compatible with old version configuration", "新版配置": "New version configuration", "获取tokenizer": "Get tokenizer", "如果只询问1个大语言模型": "If only one large language model is queried", "如果同时InquiryMultipleLargeLanguageModels": "If InquiryMultipleLargeLanguageModels is queried at the same time", "观察窗": "Observation window", "该文件中主要包含2个函数": "There are mainly 2 functions in this file", "是所有LLM的通用接口": "It is a common interface for all LLMs", "它们会继续向下调用更底层的LLM模型": "They will continue to call lower-level LLM models", "处理多模型并行等细节": "Handling details such as multi-model parallelism", "不具备多线程能力的函数": "Functions without multi-threading capability", "正常对话时使用": "Used in normal conversation", "具备完备的交互功能": "Fully interactive", "不可多线程": "Not multi-threaded", "具备多线程调用能力的函数": "Functions with multi-threading capability", "在函数插件中被调用": "Called in function plugins", "灵活而简洁": "Flexible and concise", "正在加载tokenizer": "Loading tokenizer", "如果是第一次运行": "If it is the first time running", "可能需要一点时间下载参数": "It may take some time to download parameters", "加载tokenizer完毕": "Loading tokenizer completed", "警告！API_URL配置选项将被弃用": "Warning! The API_URL configuration option will be deprecated", "请更换为API_URL_REDIRECT配置": "Please replace it with the API_URL_REDIRECT configuration", "将错误显示出来": "Display errors", "发送至LLM": "Send to LLM", "等待回复": "Waiting for reply", "一次性完成": "Completed in one go", "不显示中间过程": "Do not display intermediate processes", "但内部用stream的方法避免中途网线被掐": "But internally use the stream method to avoid the network being cut off midway", "是本次问询的输入": "This is the input of this inquiry", "系统静默prompt": "System silent prompt", "LLM的内部调优参数": "LLM's internal tuning parameters", "是之前的对话列表": "history is the list of previous conversations", "用于负责跨越线程传递已经输出的部分": "Used to transfer the already output part across threads", "大部分时候仅仅为了fancy的视觉效果": "Most of the time it's just for fancy visual effects", "留空即可": "Leave it blank", "观测窗": "Observation window", "TGUI不支持函数插件的实现": "TGUI does not support the implementation of function plugins", "说": "Say", "流式获取输出": "Get output in a streaming way", "用于基础的对话功能": "Used for basic conversation functions", "inputs 是本次问询的输入": "inputs are the inputs for this inquiry", "temperature是LLM的内部调优参数": "Temperature is an internal tuning parameter of LLM", "history 是之前的对话列表": "history is the list of previous conversations", "注意无论是inputs还是history": "Note that both inputs and history", "内容太长了都会触发token数量溢出的错误": "An error of token overflow will be triggered if the content is too long", "chatbot 为WebUI中显示的对话列表": "chatbot is the conversation list displayed in WebUI", "修改它": "Modify it", "然后yield出去": "Then yield it out", "可以直接修改对话界面内容": "You can directly modify the conversation interface content", "additional_fn代表点击的哪个按钮": "additional_fn represents which button is clicked", "按钮见functional.py": "See functional.py for buttons", "子进程执行": "Subprocess execution", "第一次运行": "First run", "加载参数": "Load parameters", "进入任务等待状态": "Enter task waiting state", "收到消息": "Received message", "开始请求": "Start requesting", "中途接收可能的终止指令": "Receive possible termination command in the middle", "如果有的话": "If any", "请求处理结束": "Request processing ends", "开始下一个循环": "Start the next loop", "主进程执行": "Main process execution", "chatglm 没有 sys_prompt 接口": "ChatGLM has no sys_prompt interface", "因此把prompt加入 history": "Therefore, add prompt to history", "的耐心": "Patience", "设置5秒即可": "Set 5 seconds", "热更新prompt": "Hot update prompt", "获取预处理函数": "Get preprocessing function", "处理历史信息": "Process historical information", "开始接收chatglm的回复": "Start receiving replies from ChatGLM", "总结输出": "Summary output", "ChatGLM尚未加载": "ChatGLM has not been loaded", "加载需要一段时间": "Loading takes some time", "取决于": "Depending on", "的配置": "Configuration", "ChatGLM消耗大量的内存": "ChatGLM consumes a lot of memory", "或显存": "Or video memory", "也许会导致低配计算机卡死 ……": "May cause low-end computers to freeze...", "依赖检测通过": "Dependency check passed", "缺少ChatGLM的依赖": "Missing dependency for ChatGLM", "如果要使用ChatGLM": "If you want to use ChatGLM", "除了基础的pip依赖以外": "In addition to the basic pip dependencies", "您还需要运行": "You also need to run", "安装ChatGLM的依赖": "Install dependencies for ChatGLM", "Call ChatGLM fail 不能正常加载ChatGLM的参数": "Call ChatGLM fail, unable to load parameters for ChatGLM", "不能正常加载ChatGLM的参数！": "Unable to load parameters for ChatGLM!", "多线程方法": "Multithreading method", "函数的说明请见 request_llms/bridge_all.py": "For function details, please see request_llms/bridge_all.py", "程序终止": "Program terminated", "单线程方法": "Single-threaded method", "等待ChatGLM响应中": "Waiting for response from ChatGLM", "ChatGLM响应异常": "ChatGLM response exception", "借鉴了 https": "Referenced from https", "config_private.py放自己的秘密如API和代理网址": "Put your own secrets such as API and proxy address in config_private.py", "读取时首先看是否存在私密的config_private配置文件": "When reading, first check if there is a private config_private configuration file", "不受git管控": "Not controlled by git", "则覆盖原config文件": "Then overwrite the original config file", "看门狗的耐心": "The patience of the watchdog", "失败了": "Failed", "重试一次": "Retry once", "再失败就没办法了": "If it fails again, there is no way", "api2d 正常完成": "api2d completed normally", "把已经获取的数据显示出去": "Display the data already obtained", "如果超过期限没有喂狗": "If the dog is not fed beyond the deadline", "则终止": "then terminate", "非OpenAI官方接口的出现这样的报错": "such errors occur in non-OpenAI official interfaces", "OpenAI和API2D不会走这里": "OpenAI and API2D will not go here", "数据流的第一帧不携带content": "The first frame of the data stream does not carry content", "前者API2D的": "The former is API2D", "判定为数据流的结束": "Judged as the end of the data stream", "gpt_replying_buffer也写完了": "gpt_replying_buffer is also written", "处理数据流的主体": "Processing the body of the data stream", "如果这里抛出异常": "If an exception is thrown here", "一般是文本过长": "It is usually because the text is too long", "详情见get_full_error的输出": "See the output of get_full_error for details", "清除当前溢出的输入": "Clear the current overflow input", "是本次输入": "It is the input of this time", "是本次输出": "It is the output of this time", "history至少释放二分之一": "Release at least half of the history", "清除历史": "Clear the history", "该文件中主要包含三个函数": "This file mainly contains three functions", "高级实验性功能模块调用": "Calling advanced experimental function modules", "不会实时显示在界面上": "Will not be displayed on the interface in real time", "参数简单": "The parameters are simple", "可以多线程并行": "Can be multi-threaded and parallel", "方便实现复杂的功能逻辑": "Convenient for implementing complex functional logic", "在实验过程中发现调用predict_no_ui处理长文档时": "It was found during the experiment that when calling predict_no_ui to process long documents,", "和openai的连接容易断掉": "Connection to OpenAI is prone to disconnection", "这个函数用stream的方式解决这个问题": "This function solves the problem using stream", "同样支持多线程": "Also supports multi-threading", "网络错误": "Network error", "检查代理服务器是否可用": "Check if the proxy server is available", "以及代理设置的格式是否正确": "And if the format of the proxy settings is correct", "格式须是": "The format must be", "缺一不可": "All parts are necessary", "获取完整的从Openai返回的报错": "Get the complete error message returned from OpenAI", "发送至chatGPT": "Send to chatGPT", "chatGPT的内部调优参数": "Internal tuning parameters of chatGPT", "请求超时": "Request timed out", "正在重试": "Retrying", "OpenAI拒绝了请求": "OpenAI rejected the request", "用户取消了程序": "User canceled the program", "意外Json结构": "Unexpected JSON structure", "正常结束": "Normal termination", "但显示Token不足": "But shows insufficient token", "导致输出不完整": "Resulting in incomplete output", "请削减单次输入的文本量": "Please reduce the amount of text input per request", "temperature是chatGPT的内部调优参数": "Temperature is an internal tuning parameter of chatGPT", "输入已识别为openai的api_key": "The input has been recognized as OpenAI's api_key", "api_key已导入": "api_key has been imported", "缺少api_key": "Missing api_key", "MOSS尚未加载": "MOSS has not been loaded yet", "MOSS消耗大量的内存": "MOSS consumes a lot of memory", "缺少MOSS的依赖": "Lack of dependencies for MOSS", "如果要使用MOSS": "If you want to use MOSS", "安装MOSS的依赖": "Install dependencies for MOSS", "Call MOSS fail 不能正常加载MOSS的参数": "Call MOSS fail, unable to load MOSS parameters normally", "不能正常加载MOSS的参数！": "Unable to load MOSS parameters normally!", "等待MOSS响应中": "Waiting for MOSS response", "MOSS响应异常": "MOSS response exception", "读取配置": "Read configuration", "等待": "Waiting", "开始问问题": "Start asking questions", "追加历史": "Append history", "问题": "Question", "代理设置": "Proxy settings", "发送请求到子进程": "Send request to child process", "等待newbing回复的片段": "Waiting for the fragment of newbing reply", "结束": "End", "newbing回复的片段": "Fragment of newbing reply", "没有 sys_prompt 接口": "No sys_prompt interface", "来自EdgeGPT.py": "From EdgeGPT.py", "等待NewBing响应": "Waiting for NewBing response", "子进程Worker": "Child process Worker", "调用主体": "Call subject", "注意目前不能多人同时调用NewBing接口": "Note that currently multiple people cannot call the NewBing interface at the same time", "有线程锁": "There is a thread lock", "否则将导致每个人的NewBing问询历史互相渗透": "Otherwise, each person's NewBing inquiry history will penetrate each other", "调用NewBing时": "When calling NewBing", "会自动使用已配置的代理": "the configured proxy will be automatically used", "缺少的依赖": "Missing dependencies", "如果要使用Newbing": "If you want to use Newbing", "安装Newbing的依赖": "Install the dependencies for Newbing", "这个函数运行在子进程": "This function runs in a child process", "不能加载Newbing组件": "Cannot load Newbing components", "NEWBING_COOKIES未填写或有格式错误": "NEWBING_COOKIES is not filled in or has a format error", "Newbing失败": "Newbing failed", "这个函数运行在主进程": "This function runs in the main process", "第三部分": "Part III", "主进程统一调用函数接口": "The main process calls the function interface uniformly", "等待NewBing响应中": "Waiting for NewBing response", "NewBing响应缓慢": "NewBing response is slow", "尚未完成全部响应": "Not all responses have been completed yet", "请耐心完成后再提交新问题": "Please be patient and submit a new question after completing all responses", "NewBing响应异常": "NewBing response is abnormal", "请刷新界面重试": "Please refresh the page and try again", "完成全部响应": "All responses have been completed", "请提交新问题": "Please submit a new question", "LLM_MODEL 格式不正确！": "LLM_MODEL format is incorrect!", "对各个llm模型进行单元测试": "Unit testing for each LLM model", "如何理解传奇?": "How to understand legends?", "设定一个最小段落长度阈值": "Set a minimum paragraph length threshold", "对文本进行归一化处理": "Normalize the text", "分解连字": "Break ligatures", "替换其他特殊字符": "Replace other special characters", "替换跨行的连词": "Replace hyphens across lines", "根据前后相邻字符的特点": "Based on the characteristics of adjacent characters", "找到原文本中的换行符": "Find line breaks in the original text", "根据 heuristic 规则": "Based on heuristic rules", "用空格或段落分隔符替换原换行符": "Replace line breaks with spaces or paragraph separators", "带超时倒计时": "With timeout countdown", "根据给定的匹配结果来判断换行符是否表示段落分隔": "Determine whether line breaks indicate paragraph breaks based on given matching results", "如果换行符前为句子结束标志": "If the line break is preceded by a sentence-ending punctuation mark", "句号": "period", "感叹号": "exclamation mark", "问号": "question mark", "且下一个字符为大写字母": "and the next character is a capital letter", "则换行符更有可能表示段落分隔": "the line break is more likely to indicate a paragraph break", "也可以根据之前的内容长度来判断段落是否已经足够长": "Paragraph length can also be judged based on previous content length", "通过把连字": "By converting ligatures and other text special characters to their basic forms", "等文本特殊符号转换为其基本形式来对文本进行归一化处理": "normalize the text by converting special characters to their basic forms", "对从 PDF 提取出的原始文本进行清洗和格式化处理": "Clean and format the raw text extracted from PDF", "1. 对原始文本进行归一化处理": "1. Normalize the original text", "2. 替换跨行的连词": "2. Replace hyphens across lines", "3. 根据 heuristic 规则判断换行符是否是段落分隔": "3. Determine whether line breaks indicate paragraph breaks based on heuristic rules", "并相应地进行替换": "And replace accordingly", "接下来请你逐文件分析下面的论文文件": "Next, please analyze the following paper files one by one", "概括其内容": "Summarize its content", "请对下面的文章片段用中文做一个概述": "Please summarize the following article in Chinese", "请对下面的文章片段做一个概述": "Please summarize the following article", "根据以上你自己的分析": "According to your own analysis above", "对全文进行概括": "Summarize the entire text", "用学术性语言写一段中文摘要": "Write a Chinese abstract in academic language", "然后再写一段英文摘要": "Then write an English abstract", "包括": "Including", "找不到任何.tex或.pdf文件": "Cannot find any .tex or .pdf files", "读取pdf文件": "Read the pdf file", "返回文本内容": "Return the text content", "此版本使用pdfminer插件": "This version uses the pdfminer plugin", "带token约简功能": "With token reduction function", "递归地切割PDF文件": "Recursively split the PDF file", "为了更好的效果": "For better results", "我们剥离Introduction之后的部分": "We strip the part after Introduction", "如果有": "If there is", "多线": "Multi-threaded", "\\n 翻译": "\\n Translation", "整理报告的格式": "Organize the format of the report", "原文": "Original text", "更新UI": "Update UI", "准备文件的下载": "Prepare for file download", "重命名文件": "Rename file", "以下是一篇学术论文的基础信息": "The following is the basic information of an academic paper", "请从中提取出“标题”、“收录会议或期刊”、“作者”、“摘要”、“编号”、“作者邮箱”这六个部分": "Please extract the following six parts: \"Title\", \"Conference or Journal\", \"Author\", \"Abstract\", \"Number\", \"Author's Email\"", "请用markdown格式输出": "Please output in markdown format", "最后用中文翻译摘要部分": "Finally, translate the abstract into Chinese", "请提取": "Please extract", "请从": "Please extract from", "中提取出“标题”、“收录会议或期刊”等基本信息": "Please extract basic information such as \"Title\" and \"Conference or Journal\" from", "你需要翻译以下内容": "You need to translate the following content", "请你作为一个学术翻译": "As an academic translator, please", "负责把学术论文准确翻译成中文": "be responsible for accurately translating academic papers into Chinese", "注意文章中的每一句话都要翻译": "Please translate every sentence in the article", "一、论文概况": "<PERSON>. Overview of the paper", "二、论文翻译": "II. Translation of the paper", "给出输出文件清单": "Provide a list of output files", "第 0 步": "Step 0", "切割PDF": "Split PDF", "每一块": "Each block", "提取出以下内容": "Extract the following content", "1、英文题目；2、中文题目翻译；3、作者；4、arxiv公开": "1. English title; 2. Translation of Chinese title; 3. Author; 4. arxiv open access", "；4、引用数量": "Number of Citations", "；5、中文摘要翻译": "Translation of Chinese Abstract", "以下是信息源": "Here are the Information Sources", "请分析此页面中出现的所有文章": "Please Analyze all the Articles Appearing on this Page", "这是第": "This is <PERSON><PERSON> Number", "批": "", "你是一个学术翻译": "You are an Academic Translator", "请从数据中提取信息": "Please Extract Information from the Data", "你必须使用Markdown表格": "You Must Use Markdown Tables", "你必须逐个文献进行处理": "You Must Process Each Document One by One", "状态": "Status", "已经全部完成": "All Completed", "您可以试试让AI写一个Related Works": "You Can Try to Let AI Write a Related Works", "该函数只有20多行代码": "This Function Has Only 20+ Lines of Code", "此外我们也提供可同步处理大量文件的多线程Demo供您参考": "In addition, we also provide a multi-threaded demo that can process a large number of files synchronously for your reference", "历史中哪些事件发生在": "Which Events Happened in History on", "月": "Month", "日": "Day", "列举两条并发送相关图片": "List Two and Send Relevant Pictures", "发送图片时": "When Sending Pictures", "请使用Markdown": "Please Use Markdown", "将Unsplash API中的PUT_YOUR_QUERY_HERE替换成描述该事件的一个最重要的单词": "Replace PUT_YOUR_QUERY_HERE in the Unsplash API with the Most Important Word Describing the Event", "1. 临时解决方案": "1. Temporary Solution", "直接在输入区键入api_key": "Enter the api_key Directly in the Input Area", "然后回车提交": "Submit after pressing Enter", "2. 长效解决方案": "2. Long-term solution", "在config.py中配置": "Configure in config.py", "等待响应": "Waiting for response", "api-key不满足要求": "API key does not meet requirements", "远程返回错误": "Remote returns error", "Json解析不合常规": "Json parsing is not normal", "Reduce the length. 本次输入过长": "Reduce the length. The input is too long this time", "或历史数据过长. 历史缓存数据已部分释放": "Or the historical data is too long. Historical cached data has been partially released", "您可以请再次尝试.": "You can try again.", "若再次失败则更可能是因为输入过长.": "If it fails again, it is more likely due to input being too long.", "does not exist. 模型不存在": "Model does not exist", "或者您没有获得体验资格": "Or you do not have the qualification for experience", "Incorrect API key. OpenAI以提供了不正确的API_KEY为由": "Incorrect API key. OpenAI claims that an incorrect API_KEY was provided", "拒绝服务": "Service refused", "You exceeded your current quota. OpenAI以账户额度不足为由": "You exceeded your current quota. OpenAI claims that the account balance is insufficient", "Bad forward key. API2D账户额度不足": "Bad forward key. API2D account balance is insufficient", "Not enough point. API2D账户点数不足": "Not enough point. API2D account points are insufficient", "Json异常": "<PERSON>son exception", "整合所有信息": "Integrate all information", "选择LLM模型": "Select LLM model", "生成http请求": "Generate http request", "为发送请求做准备": "Prepare to send request", "你提供了错误的API_KEY": "You provided an incorrect API_KEY", "来保留函数的元信息": "Preserve the metadata of the function", "并定义了一个名为decorated的内部函数": "and define an inner function named decorated", "内部函数通过使用importlib模块的reload函数和inspect模块的getmodule函数来重新加载并获取函数模块": "The inner function reloads and retrieves the function module by using the reload function of the importlib module and the getmodule function of the inspect module", "然后通过getattr函数获取函数名": "Then it retrieves the function name using the getattr function", "并在新模块中重新加载函数": "and reloads the function in the new module", "最后": "Finally", "使用yield from语句返回重新加载过的函数": "it returns the reloaded function using the yield from statement", "并在被装饰的函数上执行": "and executes it on the decorated function", "最终": "Ultimately", "装饰器函数返回内部函数": "the decorator function returns the inner function", "这个内部函数可以将函数的原始定义更新为最新版本": "which can update the original definition of the function to the latest version", "并执行函数的新版本": "and execute the new version of the function", "第二部分": "Second part", "其他小工具": "Other utilities", "将结果写入markdown文件中": "Write the results to a markdown file", "将普通文本转换为Markdown格式的文本": "Convert plain text to Markdown formatted text", "向chatbot中添加简单的意外错误信息": "Add simple unexpected error messages to the chatbot", "Openai 限制免费用户每分钟20次请求": "Openai limits free users to 20 requests per minute", "降低请求频率中": "Reduce the request frequency", "只输出代码": "Output only the code", "文件名是": "The file name is", "文件代码是": "The file code is", "至少一个线程任务Token溢出而失败": "At least one thread task fails due to token overflow", "至少一个线程任务意外失败": "At least one thread task fails unexpectedly", "开始了吗": "Has it started?", "已完成": "Completed", "的转化": "conversion", "存入": "saved to", "生成一份任务执行报告": "Generate a task execution report", "文件保存到本地": "Save the file locally", "由于请求gpt需要一段时间": "As requesting GPT takes some time", "我们先及时地做一次界面更新": "Let's do a UI update in time", "界面更新": "UI update", "输入栏用户输入的文本": "Text entered by the user in the input field", "例如需要翻译的一段话": "For example, a paragraph that needs to be translated", "再例如一个包含了待处理文件的路径": "For example, a file path that contains files to be processed", "gpt模型参数": "GPT model parameters", "如温度和top_p等": "Such as temperature and top_p", "一般原样传递下去就行": "Generally pass it on as is", "插件模型的参数": "Plugin model parameters", "暂时没有用武之地": "No use for the time being", "找不到任何.tex或pdf文件": "Cannot find any .tex or .pdf files", "读取PDF文件": "Read PDF file", "输入中可能存在乱码": "There may be garbled characters in the input", "是否重置": "Whether to reset", "jittorllms 没有 sys_prompt 接口": "jittorllms does not have a sys_prompt interface", "开始接收jittorllms的回复": "Start receiving jittorllms responses", "jittorllms尚未加载": "jittorllms has not been loaded yet", "请避免混用多种jittor模型": "Please avoid mixing multiple jittor models", "否则可能导致显存溢出而造成卡顿": "Otherwise, it may cause a graphics memory overflow and cause stuttering", "jittorllms消耗大量的内存": "jittorllms consumes a lot of memory", "缺少jittorllms的依赖": "Missing dependencies for jittorllms", "如果要使用jittorllms": "If you want to use jittorllms", "和": "and", "两个指令来安装jittorllms的依赖": "Two commands to install jittorllms dependencies", "在项目根目录运行这两个指令": "Run these two commands in the project root directory", "安装jittorllms依赖后将完全破坏现有的pytorch环境": "Installing jittorllms dependencies will completely destroy the existing pytorch environment", "建议使用docker环境！": "It is recommended to use a docker environment!", "Call jittorllms fail 不能正常加载jittorllms的参数": "Call jittorllms fail, cannot load jittorllms parameters normally", "不能正常加载jittorllms的参数！": "Cannot load jittorllms parameters normally!", "触发重置": "Trigger reset", "等待jittorllms响应中": "Waiting for jitto<PERSON><PERSON>s response", "jittorllms响应异常": "Jittor LMS Response Exception", "这段代码来源 https": "This code is from https", "等待输入": "Waiting for input", "体验gpt-4可以试试api2d": "You can try API2d to experience GPT-4", "可选 ↓↓↓": "Optional ↓↓↓", "本地LLM模型如ChatGLM的执行方式 CPU/GPU": "Execution mode of local LLM models such as ChatGLM CPU/GPU", "设置gradio的并行线程数": "Set the number of parallel threads for Gradio", "不需要修改": "No modification is needed", "加一个live2d装饰": "Add a Live2D decoration", "HotReload的装饰器函数": "Decorator function of HotReload", "用于实现Python函数插件的热更新": "Used to implement hot updates of Python function plugins", "函数热更新是指在不停止程序运行的情况下": "Function hot update refers to updating function code in real-time without stopping program execution", "更新函数代码": "Update function code", "从而达到实时更新功能": "To achieve real-time update function", "在装饰器内部": "Inside the decorator", "使用wraps": "Use wraps", "代码高亮": "Code Highlighting", "网页的端口": "Web Port", "等待多久判定为超时": "Timeout Thr<PERSON>old", "-1代表随机端口": "-1 represents random port", "但大部分场合下并不需要修改": "However, it does not need to be modified in most cases", "发送请求到OpenAI后": "After sending the request to OpenAI", "上下布局": "Vertical Layout", "左右布局": "Horizontal Layout", "对话窗的高度": "Height of the Conversation Window", "重试的次数限制": "Retry Limit", "gpt4现在只对申请成功的人开放": "GPT-4 is now only open to those who have successfully applied", "提高限制请查询": "Please check for higher limits", "OpenAI模型选择是": "OpenAI Model Selection is", "网络卡顿、代理失败、KEY失效": "Network Lag, Proxy Failure, KEY Invalid", "窗口布局": "Window Layout", "以下配置可以优化体验": "The following configurations can optimize the experience", "OpenAI绑了信用卡的用户可以填 16 或者更高": "Users who have bound their credit card to OpenAI can fill in 16 or higher", "如果OpenAI不响应": "If OpenAI does not respond", "Latex英文纠错": "LatexEnglishCorrection", "总结音视频": "SummaryAudioVideo", "动画生成": "AnimationGeneration", "数学动画生成manim": "MathematicalAnimationGenerationManim", "test_数学动画生成manim": "test_MathematicalAnimationGenerationManim", "这里借用了 https": "Here uses https", "在相对论中": "In relativity", "找不到任何音频或视频文件": "Cannot find any audio or video files", "广义坐标": "Generalized coordinates", "导入依赖失败": "Failed to import dependencies", "相对速度": "Relative velocity", "循环监听已打开频道的消息": "Loop to listen to messages in an open channel", "秒 s": "Seconds s", "提取视频中的音频": "Extract audio from video", "解析为简体中文": "Parse to Simplified Chinese", "等待Claude响应": "Waiting for <PERSON>'s response", "请继续分析其他源代码": "Please continue to analyze other source code", "3. 勒让德变换公式": "3. <PERSON><PERSON><PERSON> transformation formula", "需要被切割的音频文件名": "Name of audio file to be cut", "Claude回复的片段": "Fragment replied by <PERSON>", "拉格朗日量": "<PERSON><PERSON><PERSON><PERSON>", "暂时不支持历史消息": "Historical messages are not supported temporarily", "从而更全面地理解项目的整体功能": "So as to have a more comprehensive understanding of the overall function of the project", "建议暂时不要使用": "It is recommended not to use it temporarily", "整理结果为压缩包": "Organize the results into a compressed package", "焦耳 J": "<PERSON><PERSON>", "其中 $t$ 为时间": "Where $t$ is time", "将三个方程变形为增广矩阵形式": "Transform three equations into augmented matrix form", "获取已打开频道的最新消息并返回消息列表": "Get the latest messages from the opened channel and return a list of messages", "str类型": "str type", "所有音频都总结完成了吗": "Are all audio summaries completed?", "SummaryAudioVideo内容": "SummaryAudioVideo content", "使用教程详情见 request_llms/README.md": "See request_llms/README.md for detailed usage instructions", "删除中间文件夹": "Delete intermediate folder", "Claude组件初始化成功": "Claude component initialized successfully", "$c$ 是光速": "$c$ is the speed of light", "参考文献转Bib": "Convert reference to Bib", "发送到openai音频解析终端": "Send to openai audio parsing terminal", "不能加载Claude组件": "Cannot load Claude component", "千克 kg": "Kilogram kg", "切割音频文件": "Cut audio file", "方法": "Method", "设置API_KEY": "Set API_KEY", "然后转移到指定的另一个路径中": "Then move to a specified path", "正在加载Claude组件": "Loading Claude component", "极端速度v下的一个相对独立观测者测得的时间": "The time measured by a relatively independent observer at extreme speed v", "广义速度": "Generalized velocity", "粒子的固有": "Intrinsic of particle", "一个包含所有切割音频片段文件路径的列表": "A list containing the file paths of all segmented audio clips", "计算文件总时长和切割点": "Calculate total duration and cutting points of the file", "总结音频": "Summarize audio", "作者": "Author", "音频内容是": "The content of the audio is", "\\frac{v^2}{c^2}}}$ 是洛伦兹因子": "$\\frac{v^2}{c^2}}}$ is the Lorentz factor", "辅助gpt生成代码": "Assist GPT in generating code", "读取文件内容到内存": "Read file content into memory", "以秒为单位": "In seconds", "米每秒 m/s": "Meters per second m/s", "物体的质量": "Mass of the object", "请对下面的音频片段做概述": "Please summarize the following audio clip", "t是原始坐标系下的物理量": "t is a physical quantity in the original coordinate system", "获取回复": "Get reply", "正在处理": "Processing", "将音频解析为简体中文": "Parse audio into Simplified Chinese", "音频解析结果": "Audio parsing result", "在这里放一些网上搜集的demo": "Put some demos collected online here", "”的主要内容": "The main content of ", "将": "Convert", "请用一句话概括这些文件的整体功能": "Please summarize the overall function of these files in one sentence", "P.S. 其他可用的模型还包括": "P.S. Other available models include", "创建存储切割音频的文件夹": "Create folder to store segmented audio", "片段": "Segment", "批量SummaryAudioVideo": "Batch Summary Audio Video", "单位": "Unit", "1. 等效质量-能量关系式": "1. Equivalent quality-energy relationship formula", "模型选择是": "Model selection is", "使用中文总结音频“": "Use Chinese to summarize audio", "音频文件名": "Audio file name", "LLM_MODEL是默认选中的模型": "LLM_MODEL is the default selected model", "异步方法": "Asynchronous method", "文本碎片重组为完整的tex文件": "Reassemble text fragments into a complete tex file", "请对这部分内容进行语法矫正": "Please correct the grammar of this part", "打开你的科学上网软件查看代理的协议": "Open your scientific Internet access software to view the proxy agreement", "调用openai api 使用whisper-1模型": "Call openai api to use whisper-1 model", "此处可以输入解析提示": "Parsing tips can be entered here", "报告如何远程获取": "Report how to obtain remotely", "将代码转为动画": "Convert code to animation", "Claude失败": "<PERSON> failed", "等待Claude响应中": "Waiting for <PERSON>'s response", "目前不支持历史消息查询": "Historical message queries are currently not supported", "把某个路径下所有文件压缩": "Compress all files under a certain path", "论文概况": "Overview of the paper", "参见https": "See https", "如果要使用Claude": "If you want to use Claude", "2. 洛伦兹变换式": "2. <PERSON><PERSON><PERSON> transformation formula", "通过调用conversations_open方法打开一个频道": "Open a channel by calling the conversations_open method", "当前参数": "Current parameters", "安装Claude的依赖": "Install Claude's dependencies", "生成的视频文件路径": "Generated video file path", "注意目前不能多人同时调用Claude接口": "Note that multiple people cannot currently call the Claude interface at the same time", "获取Slack消息失败": "Failed to get <PERSON><PERSON><PERSON> message", "翻译结果": "Translation result", "调用Claude时": "When calling <PERSON>", "已知某些代码的局部作用是": "It is known that the local effect of some code is", "根据给定的切割时长将音频文件切割成多个片段": "Cut the audio file into multiple segments according to the given cutting duration", "请稍候": "Please wait", "向已打开的频道发送一条文本消息": "Send a text message to the opened channel", "每个切割音频片段的时长": "The duration of each cut audio segment", "Claude响应缓慢": "<PERSON> responds slowly", "然后重启程序": "Then restart the program", "因为在同一个频道里存在多人使用时历史消息渗透问题": "Because there is a problem of historical message penetration when multiple people use it in the same channel", "其中": "Among them", "gpt写的": "Written by GP<PERSON>", "报告已经添加到右侧“文件上传区”": "The report has been added to the 'File Upload Area' on the right", "目前支持的格式": "Supported formats at present", "英文Latex项目全文纠错": "Full-text correction of English Latex projects", "光速": "Speed of light", "表示频道ID": "Representing channel ID", "读取音频文件": "Reading audio files", "数学AnimationGeneration": "Mathematical Animation Generation", "开始生成动画": "Start generating animation", "否则将导致每个人的Claude问询历史互相渗透": "Otherwise, everyone's <PERSON> inquiry history will be mutually infiltrated", "如果需要使用Slack Claude": "If you need to use Slack Claude", "防止丢失最后一条消息": "Prevent the last message from being lost", "开始": "Start", "Claude响应异常": "<PERSON> responds abnormally", "并将返回的频道ID保存在属性CHANNEL_ID中": "And save the returned channel ID in the property CHANNEL_ID", "4. 时间膨胀公式": "4. Time dilation formula", "属性": "Attribute", "一些常见的公式包括": "Some common formulas include", "时间": "Time", "物体的能量": "Energy of an object", "对整个Latex项目进行纠错": "Correcting the entire Latex project", "此插件处于开发阶段": "This plugin is in the development stage", "实现消息发送、接收等功能": "Implement message sending, receiving and other functions", "生成数学动画": "Generate mathematical animations", "设置OpenAI密钥和模型": "Set OpenAI key and model", "默认值为1000": "Default value is 1000", "调用whisper模型音频转文字": "Call whisper model to convert audio to text", "否则结束循环": "Otherwise end the loop", "等待Claude回复的片段": "Wait for the segment replied by <PERSON>", "这些公式描述了质量-能量转换、相对论引起的空间时变形、描述物理系统的拉格朗日力学、以及时间膨胀等现象": "These formulas describe phenomena such as mass-energy conversion, space-time deformation caused by relativity, Lagrangian mechanics describing physical systems, and time dilation.", "则无需填写NEWBING_COOKIES": "Then there is no need to fill in NEWBING_COOKIES", "SlackClient类用于与Slack API进行交互": "The SlackClient class is used to interact with the Slack API", "同时它必须被包含在AVAIL_LLM_MODELS切换列表中": "At the same time, it must be included in the AVAIL_LLM_MODELS switch list", "段音频完成了吗": "Is the segment audio completed?", "提取文件扩展名": "Extract the file extension", "段音频的第": "The", "段音频的主要内容": "The main content of the segment audio is", "z$ 分别是空间直角坐标系中的三个坐标": "z$, respectively, are the three coordinates in the spatial rectangular coordinate system", "这个是怎么识别的呢我也不清楚": "I'm not sure how this is recognized", "从现在起": "From now on", "连接bing搜索回答问题": "ConnectBingSearchAnswerQuestion", "联网的ChatGPT_bing版": "OnlineChatGPT_BingEdition", "Markdown翻译指定语言": "TranslateMarkdownToSpecifiedLanguage", "Langchain知识库": "LangchainKnowledgeBase", "Latex英文纠错加PDF对比": "CorrectEnglishInLatexWithPDFComparison", "Latex翻译中文并重新编译PDF": "TranslateChineseToEnglishInLatexAndRecompilePDF", "sprint亮靛": "SprintIndigo", "寻找Latex主文件": "FindLatexMainFile", "专业词汇声明": "ProfessionalTerminologyDeclaration", "Latex精细分解与转化": "DecomposeAndConvertLatex", "编译Latex": "CompileLatex", "如果您是论文原作者": "If you are the original author of the paper", "正在编译对比PDF": "Compiling the comparison PDF", "将 \\include 命令转换为 \\input 命令": "Converting the \\include command to the \\input command", "取评分最高者返回": "Returning the highest-rated one", "不要修改!! 高危设置！通过修改此设置": "Do not modify!! High-risk setting! By modifying this setting", "Tex源文件缺失！": "Tex source file is missing!", "6.25 加入判定latex模板的代码": "Added code to determine the latex template on June 25", "正在精细切分latex文件": "Finely splitting the latex file", "获取response失败": "Failed to get response", "手动指定语言": "Manually specify the language", "输入arxivID": "Enter arxivID", "对输入的word文档进行摘要生成": "Generate a summary of the input word document", "将指定目录下的PDF文件从英文翻译成中文": "Translate PDF files from English to Chinese in the specified directory", "如果分析错误": "If the analysis is incorrect", "尝试第": "Try the", "用户填3": "User fills in 3", "请在此处追加更细致的矫错指令": "Please append more detailed correction instructions here", "为了防止大语言模型的意外谬误产生扩散影响": "To prevent the accidental spread of errors in large language models", "前面是中文冒号": "The colon before is in Chinese", "内含已经翻译的Tex文档": "Contains a Tex document that has been translated", "成功啦": "Success!", "刷新页面即可以退出UpdateKnowledgeArchive模式": "Refresh the page to exit UpdateKnowledgeArchive mode", "或者不在环境变量PATH中": "Or not in the environment variable PATH", "--读取文件": "--Read the file", "才能继续下面的步骤": "To continue with the next steps", "代理数据解析失败": "Proxy data parsing failed", "详见项目主README.md": "See the main README.md of the project for details", "临时存储用于调试": "Temporarily stored for debugging", "屏蔽空行和太短的句子": "Filter out empty lines and sentences that are too short", "gpt 多线程请求": "GPT multi-threaded request", "编译已经开始": "Compilation has started", "无法找到一个主Tex文件": "Cannot find a main Tex file", "修复括号": "Fix parentheses", "请您不要删除或修改这行警告": "Please do not delete or modify this warning", "请登录OpenAI查看详情 https": "Please log in to OpenAI to view details at https", "调用函数": "Call a function", "请查看终端的输出或耐心等待": "Please check the output in the terminal or wait patiently", "LatexEnglishCorrection+高亮修正位置": "Latex English correction + highlight correction position", "行": "line", "Newbing 请求失败": "Newbing request failed", "转化PDF编译是否成功": "Check if the conversion to PDF and compilation were successful", "建议更换代理协议": "Recommend changing the proxy protocol", "========================================= 插件主程序1 =====================================================": "========================================= Plugin Main Program 1 =====================================================", "终端": "terminal", "请先上传文件素材": "Please upload file materials first", "前面是中文逗号": "There is a Chinese comma in front", "请尝试把以下指令复制到高级参数区": "Please try copying the following instructions to the advanced parameters section", "翻译-": "Translation -", "请耐心等待": "Please be patient", "将前后断行符脱离": "Remove line breaks before and after", "json等": "JSON, etc.", "生成中文PDF": "Generate Chinese PDF", "用红色标注处保留区": "Use red color to highlight the reserved area", "对比PDF编译是否成功": "Compare if the PDF compilation was successful", "回答完问题后": "After answering the question", "其他操作系统表现未知": "Unknown performance on other operating systems", "-构建知识库": "Build knowledge base", "还原原文": "Restore original text", "或者重启之后再度尝试": "Or try again after restarting", "免费": "Free", "仅在Windows系统进行了测试": "Tested only on Windows system", "欢迎加README中的QQ联系开发者": "Feel free to contact the developer via QQ in README", "当前知识库内的有效文件": "Valid files in the current knowledge base", "您可以到Github Issue区": "You can go to the Github Issue area", "刷新Gradio前端界面": "Refresh the Gradio frontend interface", "吸收title与作者以上的部分": "Include the title and the above part of the author", "给出一些判定模板文档的词作为扣分项": "Provide some words in the template document as deduction items", "--读取参数": "-- Read parameters", "然后进行问答": "And then perform question-answering", "根据自然语言执行插件命令": "Execute plugin commands based on natural language", "*{\\scriptsize\\textbf{警告": "*{\\scriptsize\\textbf{Warning", "但请查收结果": "But please check the results", "翻译内容可靠性无保障": "No guarantee of translation accuracy", "寻找主文件": "Find the main file", "消耗时间的函数": "Time-consuming function", "当前语言模型温度设定": "Current language model temperature setting", "这需要一段时间计算": "This requires some time to calculate", "为啥chatgpt会把cite里面的逗号换成中文逗号呀": "Why does ChatGPT change commas inside 'cite' to Chinese commas?", "发现已经存在翻译好的PDF文档": "Found an already translated PDF document", "待提取的知识库名称id": "Knowledge base name ID to be extracted", "文本碎片重组为完整的tex片段": "Reassemble text fragments into complete tex fragments", "注意事项": "Notes", "参数说明": "Parameter description", "或代理节点": "Or proxy node", "构建知识库": "Building knowledge base", "报错信息如下. 如果是与网络相关的问题": "Error message as follows. If it is related to network issues", "功能描述": "Function description", "禁止移除或修改此警告": "Removal or modification of this warning is prohibited", "ArXiv翻译": "ArXiv translation", "读取优先级": "Read priority", "包含documentclass关键字": "Contains the documentclass keyword", "根据文本使用GPT模型生成相应的图像": "Generate corresponding images using GPT model based on the text", "图像生成所用到的提示文本": "Prompt text used for image generation", "Your account is not active. OpenAI以账户失效为由": "Your account is not active. OpenAI states that it is due to account expiration", "快捷的调试函数": "Convenient debugging function", "在多Tex文档中": "In multiple Tex documents", "因此选择GenerateImage函数": "Therefore, choose the GenerateImage function", "当前工作路径为": "The current working directory is", "实际得到格式": "Obtained format in reality", "这段代码定义了一个名为TempProxy的空上下文管理器": "This code defines an empty context manager named TempProxy", "吸收其他杂项": "Absorb other miscellaneous items", "请输入要翻译成哪种语言": "Please enter which language to translate into", "的单词": "of the word", "正在尝试自动安装": "Attempting automatic installation", "如果有必要": "If necessary", "开始下载": "Start downloading", "项目Github地址 \\url{https": "Project GitHub address \\url{https", "将根据报错信息修正tex源文件并重试": "The Tex source file will be corrected and retried based on the error message", "发送至azure openai api": "Send to Azure OpenAI API", "吸收匿名公式": "Absorb anonymous formulas", "用该压缩包+ConversationHistoryArchive进行反馈": "Provide feedback using the compressed package + ConversationHistoryArchive", "需要特殊依赖": "Requires special dependencies", "还原部分原文": "Restore part of the original text", "构建完成": "Build completed", "解析arxiv网址失败": "Failed to parse arXiv URL", "输入问题后点击该插件": "Click the plugin after entering the question", "请求子进程": "Requesting subprocess", "请务必用 pip install -r requirements.txt 指令安装依赖": "Please make sure to install the dependencies using the 'pip install -r requirements.txt' command", "如果程序停顿5分钟以上": "If the program pauses for more than 5 minutes", "转化PDF编译已经成功": "Conversion to PDF compilation was successful", "虽然PDF生成失败了": "Although PDF generation failed", "分析上述回答": "Analyze the above answer", "吸收在42行以内的begin-end组合": "Absorb the begin-end combination within 42 lines", "推荐http": "Recommend http", "Latex没有安装": "Latex is not installed", "用latex编译为PDF对修正处做高亮": "Compile to PDF using LaTeX and highlight the corrections", "reverse 操作必须放在最后": "'reverse' operation must be placed at the end", "AZURE OPENAI API拒绝了请求": "AZURE OPENAI API rejected the request", "该项目的Latex主文件是": "The main LaTeX file of this project is", "You are associated with a deactivated account. OpenAI以账户失效为由": "You are associated with a deactivated account. OpenAI considers it as an account expiration", "它*必须*被包含在AVAIL_LLM_MODELS列表中": "It *must* be included in the AVAIL_LLM_MODELS list", "未知指令": "Unknown command", "尝试执行Latex指令失败": "Failed to execute the LaTeX command", "摘要生成后的文档路径": "Path of the document after summary generation", "GPT结果已输出": "GPT result has been outputted", "使用Newbing": "Using Newbing", "其他模型转化效果未知": "Unknown conversion effect of other models", "P.S. 但愿没人把latex模板放在里面传进来": "P.S. Hopefully, no one passes a LaTeX template in it", "定位主Latex文件": "Locate the main LaTeX file", "后面是英文冒号": "English colon follows", "文档越长耗时越长": "The longer the document, the longer it takes.", "压缩包": "Compressed file", "但通常不会出现在正文": "But usually does not appear in the body.", "正在预热文本向量化模组": "Preheating text vectorization module", "5刀": "5 dollars", "提问吧! 但注意": "Ask questions! But be careful", "发送至AZURE OPENAI API": "Send to AZURE OPENAI API", "请仔细鉴别并以原文为准": "Please carefully verify and refer to the original text", "如果需要使用AZURE 详情请见额外文档 docs\\use_azure.md": "If you need to use AZURE, please refer to the additional document docs\\use_azure.md for details", "使用正则表达式查找半行注释": "Use regular expressions to find inline comments", "只有第二步成功": "Only the second step is successful", "P.S. 顺便把CTEX塞进去以支持中文": "P.S. By the way, include CTEX to support Chinese", "安装方法https": "Installation method: https", "则跳过GPT请求环节": "Then skip the GPT request process", "请切换至“UpdateKnowledgeArchive”插件进行知识库访问": "Please switch to the 'UpdateKnowledgeArchive' plugin for knowledge base access", "=================================== 工具函数 ===============================================": "=================================== Utility functions ===============================================", "填入azure openai api的密钥": "Fill in the Azure OpenAI API key", "上传Latex压缩包": "Upload LaTeX compressed file", "远程云服务器部署": "Deploy to remote cloud server", "用黑色标注转换区": "Use black color to annotate the conversion area", "音频文件的路径": "Path to the audio file", "必须包含documentclass": "Must include documentclass", "再列出用户可能提出的三个问题": "List three more questions that the user might ask", "根据需要切换prompt": "Switch the prompt as needed", "将文件复制一份到下载区": "Make a copy of the file in the download area", "次编译": "Second compilation", "Latex文件融合完成": "LaTeX file merging completed", "返回": "Return", "后面是英文逗号": "Comma after this", "对不同latex源文件扣分": "Deduct points for different LaTeX source files", "失败啦": "Failed", "编译BibTex": "Compile BibTeX", "Linux下必须使用Docker安装": "Must install using Docker on Linux", "报错信息": "Error message", "删除或修改歧义文件": "Delete or modify ambiguous files", "-预热文本向量化模组": "- Preheating text vectorization module", "将每次对话记录写入Markdown格式的文件中": "Write each conversation record into a file in Markdown format", "其他类型文献转化效果未知": "Unknown conversion effect for other types of literature", "获取线程锁": "Acquire thread lock", "使用英文": "Use English", "如果存在调试缓存文件": "If there is a debug cache file", "您需要首先调用构建知识库": "You need to call the knowledge base building first", "原始PDF编译是否成功": "Whether the original PDF compilation is successful", "生成 azure openai api请求": "Generate Azure OpenAI API requests", "正在编译PDF": "Compiling PDF", "仅调试": "Debug only", "========================================= 插件主程序2 =====================================================": "========================================= Plugin Main Program 2 =====================================================", "多线程翻译开始": "Multithreaded translation begins", "出问题了": "There is a problem", "版权归原文作者所有": "Copyright belongs to the original author", "当前大语言模型": "Current large language model", "目前对机器学习类文献转化效果最好": "Currently, the best conversion effect for machine learning literature", "这个paper有个input命令文件名大小写错误！": "This paper has an input command with a filename case error!", "期望格式例如": "Expected format, for example", "解决部分词汇翻译不准确的问题": "Resolve the issue of inaccurate translation for some terms", "待注入的知识库名称id": "Name/ID of the knowledge base to be injected", "精细切分latex文件": "Fine-grained segmentation of LaTeX files", "永远给定None": "Always given None", "work_folder = Latex预处理": "work_folder = LaTeX preprocessing", "请直接去该路径下取回翻译结果": "Please directly go to the path to retrieve the translation results", "寻找主tex文件": "Finding the main .tex file", "模型参数": "Model parameters", "返回找到的第一个": "Return the first one found", "编译转化后的PDF": "Compile the converted PDF", "\\SEAFILE_LOCALŅ03047\\我的资料库\\music\\Akie秋绘-未来轮廓.mp3": "\\SEAFILE_LOCALŅ03047\\My Library\\music\\Akie秋绘-未来轮廓.mp3", "拆分过长的latex片段": "Splitting overly long LaTeX fragments", "没有找到任何可读取文件": "No readable files found", "暗色模式 / 亮色模式": "Dark mode / Light mode", "检测到arxiv文档连接": "Detected arXiv document link", "此插件Windows支持最佳": "This plugin has best support for Windows", "from crazy_functions.虚空终端 import 终端": "from crazy_functions.null_terminal import Terminal", "本地论文翻译": "Local paper translation", "输出html调试文件": "Output HTML debugging file", "以下所有配置也都支持利用环境变量覆写": "All the following configurations can also be overridden using environment variables", "PDF文件所在的路径": "Path of the PDF file", "也是可读的": "It is also readable", "将消耗较长时间下载中文向量化模型": "Downloading Chinese vectorization model will take a long time", "环境变量配置格式见docker-compose.yml": "See docker-compose.yml for the format of environment variable configuration", "编译文献交叉引用": "Compile bibliographic cross-references", "默认为default": "Default is 'default'", "或者使用此插件继续上传更多文件": "Or use this plugin to continue uploading more files", "该PDF由GPT-Academic开源项目调用大语言模型+Latex翻译插件一键生成": "This PDF is generated by the GPT-Academic open-source project using a large language model + LaTeX translation plugin", "使用latexdiff生成论文转化前后对比": "Use latexdiff to generate before and after comparison of paper transformation", "正在编译PDF文档": "Compiling PDF document", "读取config.py文件中关于AZURE OPENAI API的信息": "Read the information about AZURE OPENAI API from the config.py file", "配置教程&视频教程": "Configuration tutorial & video tutorial", "临时地启动代理网络": "Temporarily start proxy network", "临时地激活代理网络": "Temporarily activate proxy network", "功能尚不稳定": "Functionality is unstable", "默认为Chinese": "<PERSON><PERSON><PERSON> is Chinese", "请查收结果": "Please check the results", "将 chatglm 直接对齐到 chatglm2": "Align chatglm directly to chatglm2", "中读取数据构建知识库": "Build a knowledge base by reading data in", "用于给一小段代码上代理": "Used to proxy a small piece of code", "分析结果": "Analysis results", "依赖不足": "Insufficient dependencies", "Markdown翻译": "Markdown translation", "除非您是论文的原作者": "Unless you are the original author of the paper", "test_LangchainKnowledgeBase读取": "test_LangchainKnowledgeBase read", "将多文件tex工程融合为一个巨型tex": "Merge multiple tex projects into one giant tex", "吸收iffalse注释": "Absorb <PERSON><PERSON><PERSON><PERSON> comments", "您接下来不能再使用其他插件了": "You can no longer use other plugins next", "正在构建知识库": "Building knowledge base", "需Latex": "Requires Latex", "即找不到": "That is not found", "保证括号正确": "Ensure parentheses are correct", "= 2 通过一些Latex模板中常见": "= 2 through some common Latex templates", "请立即终止程序": "Please terminate the program immediately", "解压失败! 需要安装pip install rarfile来解压rar文件": "Decompression failed! Install 'pip install rarfile' to decompress rar files", "请在此处给出自定义翻译命令": "Please provide custom translation command here", "解压失败! 需要安装pip install py7zr来解压7z文件": "Decompression failed! Install 'pip install py7zr' to decompress 7z files", "执行错误": "Execution error", "目前仅支持GPT3.5/GPT4": "Currently only supports GPT3.5/GPT4", "P.S. 顺便把Latex的注释去除": "P.S. Also remove comments from Latex", "写出文件": "Write out the file", "当前报错的latex代码处于第": "The current error in the LaTeX code is on line", "主程序即将开始": "Main program is about to start", "详情信息见requirements.txt": "See details in requirements.txt", "释放线程锁": "Release thread lock", "由于最为关键的转化PDF编译失败": "Due to the critical failure of PDF conversion and compilation", "即将退出": "Exiting soon", "尝试下载": "Attempting to download", "删除整行的空注释": "Remove empty comments from the entire line", "也找不到": "Not found either", "从一批文件": "From a batch of files", "编译结束": "Compilation finished", "调用缓存": "Calling cache", "只有GenerateImage和生成图像相关": "Only GenerateImage and image generation related", "待处理的word文档路径": "Path of the word document to be processed", "是否在提交时自动清空输入框": "Whether to automatically clear the input box upon submission", "检查结果": "Check the result", "生成时间戳": "Generate a timestamp", "编译原始PDF": "Compile the original PDF", "填入ENGINE": "Fill in ENGINE", "填入api版本": "Fill in the API version", "中文Bing版": "Chinese Bing version", "当前支持的格式包括": "Currently supported formats include", "交互功能模板函数": "InteractiveFunctionTemplateFunction", "交互功能函数模板": "InteractiveFunctionFunctionTemplate", "语音助手": "VoiceAssistant", "微调数据集生成": "FineTuneDatasetGeneration", "chatglm微调工具": "ChatGLMFineTuningTool", "启动微调": "StartFineTuning", "请讲话": "Please speak", "正在听您讲话": "Listening to you", "对这个人外貌、身处的环境、内心世界、过去经历进行描写": "Describe the appearance, environment, inner world, and past experiences of this person", "请向下翻": "Please scroll down", "实时音频采集": "Real-time audio collection", "找不到": "Not found", "在一个异步线程中采集音频": "Collect audio in an asynchronous thread", "azure和api2d请求源": "Azure and API2D request source", "等待ChatGLMFT响应中": "Waiting for ChatGLMFT response", "如果使用ChatGLM2微调模型": "If using ChatGLM2 fine-tuning model", "把文件复制过去": "Copy the file over", "可选": "Optional", "ChatGLMFT响应异常": "ChatGLMFT response exception", "上传本地文件/压缩包供函数插件调用": "Upload local files/compressed packages for function plugin calls", "例如 f37f30e0f9934c34a992f6f64f7eba4f": "For example, f37f30e0f9934c34a992f6f64f7eba4f", "正在等您说完问题": "Waiting for you to finish the question", "解除插件状态": "Release plugin status", "详情见https": "See details at https", "避免线程阻塞": "Avoid thread blocking", "先上传数据集": "Upload dataset first", "请直接提交即可": "Submit directly", "Call ChatGLMFT fail 不能正常加载ChatGLMFT的参数": "Call ChatGLMFT fail, cannot load ChatGLMFT parameters", "插件可读取“输入区”文本/路径作为参数": "The plugin can read text/path in the input area as parameters", "给出指令": "Give instructions", "暂不提交": "Do not submit for now", "如 绿帽子*深蓝色衬衫*黑色运动裤": "E.g. green hat * dark blue shirt * black sports pants", "阿里云实时语音识别 配置难度较高 仅建议高手用户使用 参考 https": "Aliyun real-time speech recognition has high configuration difficulty and is only recommended for advanced users. Refer to https", "ChatGLMFT尚未加载": "ChatGLMFT has not been loaded yet", "输入 clear 以清空对话历史": "Enter 'clear' to clear the conversation history", "可以将自身的状态存储到cookie中": "You can store your own status in cookies", "填入你亲手写的部署名": "Fill in the deployment name you wrote by yourself", "该选项即将被弃用": "This option will be deprecated soon", "代理网络配置": "Proxy network configuration", "每秒采样数量": "Number of samples per second", "使用时": "When using", "想象一个穿着者": "Imagine a wearer", "如果已经存在": "If it already exists", "例如您可以将以下命令复制到下方": "For example, you can copy the following command below", "正在锁定插件": "Locking plugin", "使用": "Use", "读 docs\\use_azure.md": "Read docs\\use_azure.md", "开始最终总结": "Start final summary", "openai的官方KEY需要伴随组织编码": "Openai's official KEY needs to be accompanied by organizational code", "将子线程的gpt结果写入chatbot": "Write the GPT result of the sub-thread into the chatbot", "ArXiv论文精细翻译": "Fine translation of ArXiv paper", "开始接收chatglmft的回复": "Start receiving replies from chatglmft", "请先将.doc文档转换为.docx文档": "Please convert .doc documents to .docx documents first", "避免多用户干扰": "Avoid multiple user interference", "清空label": "Clear label", "解除插件锁定": "Unlock plugin", "请以以下方式load模型！！！": "Please load the model in the following way!!!", "没给定指令": "No instruction given", "100字以内": "Within 100 words", "获取关键词": "Get keywords", "欢迎使用 MOSS 人工智能助手！": "Welcome to use MOSS AI assistant!", "音频助手": "Audio assistant", "上传Latex项目": "Upload Latex project", "对话助手函数插件": "Chat assistant function plugin", "如果一句话小于7个字": "If a sentence is less than 7 words", "640个字节为一组": "640 bytes per group", "右下角更换模型菜单中可切换openai": "OpenAI can be switched in the model menu in the lower right corner", "双手离开鼠标键盘吧": "Take your hands off the mouse and keyboard", "先删除": "Delete first", "如果要使用ChatGLMFT": "If you want to use ChatGLMFT", "例如 RoPlZrM88DnAFkZK": "For example, RoPlZrM88DnAFkZK", "提取总结": "Extract summary", "ChatGLMFT消耗大量的内存": "ChatGLMFT consumes a lot of memory", "格式如org-123456789abcdefghijklmno的": "In the format of org-123456789abcdefghijklmno", "在执行完成之后": "After execution is complete", "此处填API密钥": "Fill in the API key here", "chatglmft 没有 sys_prompt 接口": "ChatGLMFT does not have a sys_prompt interface", "用第二人称": "Use the second person", "Chuanhu-Small-and-Beautiful主题": "<PERSON><PERSON><PERSON>-Small-and-Beautiful theme", "请检查ALIYUN_TOKEN和ALIYUN_APPKEY是否过期": "Please check if ALIYUN_TOKEN and ALIYUN_APPKEY have expired", "还需要填写组织": "You also need to fill in the organization", "会直接转到该函数": "Will directly jump to the function", "初始化插件状态": "Initializing plugin status", "插件锁定中": "Plugin is locked", "如果这里报错": "If there is an error here", "本地Latex论文精细翻译": "Local Latex paper fine translation", "极少数情况下": "In very few cases", "首先你在中文语境下通读整篇论文": "First, read the entire paper in a Chinese context", "点击“停止”键可终止程序": "Click the 'Stop' button to terminate the program", "建议排查": "Suggested troubleshooting", "没有阿里云语音识别APPKEY和TOKEN": "No Aliyun voice recognition APPKEY and TOKEN", "避免遗忘导致死锁": "Avoid forgetting to cause deadlock", "第一次调用": "First call", "解决插件锁定时的界面显示问题": "Solve the interface display problem when the plugin is locked", "初始化音频采集线程": "Initialize audio capture thread", "找不到微调模型检查点": "Cannot find fine-tuning model checkpoint", "色彩主体": "Color theme", "上传文件自动修正路径": "Automatically correct the path when uploading files", "将文件添加到chatbot cookie中": "Add files to chatbot cookie", "正常状态": "Normal state", "建议使用英文单词": "Suggest using English words", "Aliyun音频服务异常": "Aliyun audio service exception", "格式如org-xxxxxxxxxxxxxxxxxxxxxxxx": "Format like org-xxxxxxxxxxxxxxxxxxxxxxxx", "GPT 学术优化": "GPT academic optimization", "要求": "Requirement", "赋予插件状态": "Assign plugin status", "等待GPT响应": "Waiting for GPT response", "MOSS can understand and communicate fluently in the language chosen by the user such as English and 中文. MOSS can perform any language-based tasks.": "MOSS can understand and communicate fluently in the language chosen by the user such as English and Chinese. MOSS can perform any language-based tasks.", "我将为您查找相关壁纸": "I will search for related wallpapers for you", "当下一次用户提交时": "When the next user submits", "赋予插件锁定 锁定插件回调路径": "Assign plugin lock, lock plugin callback path", "处理个别特殊插件的锁定状态": "Handle the lock status of individual special plugins", "add gpt task 创建子线程请求gpt": "Add GPT task, create sub-thread to request GPT", "等待用户的再次调用": "Waiting for the user to call again", "只读": "Read-only", "用于灵活调整复杂功能的各种参数": "Various parameters used to flexibly adjust complex functions", "输入 stop 以终止对话": "Enter stop to terminate the conversation", "缺少ChatGLMFT的依赖": "Missing dependency of ChatGLMFT", "找 API_ORG 设置项": "Find API_ORG setting item", "检查config中的AVAIL_LLM_MODELS选项": "Check the AVAIL_LLM_MODELS option in config", "对这个人外貌、身处的环境、内心世界、人设进行描写": "Describe the appearance, environment, inner world, and character of this person.", "请输入关键词": "Please enter a keyword.", "！！！如果需要运行量化版本": "!!! If you need to run the quantitative version.", "为每一位访问的用户赋予一个独一无二的uuid编码": "Assign a unique uuid code to each visiting user.", "由于提问含不合规内容被Azure过滤": "Due to Azure filtering out questions containing non-compliant content.", "欢迎使用 MOSS 人工智能助手！输入内容即可进行对话": "Welcome to use MOSS AI assistant! Enter the content to start the conversation.", "记住当前的label": "Remember the current label.", "不能正常加载ChatGLMFT的参数！": "Cannot load ChatGLMFT parameters normally!", "建议直接在API_KEY处填写": "It is recommended to fill in directly at API_KEY.", "创建request": "Create request", "默认 secondary": "Default secondary", "会被加在你的输入之前": "Will be added before your input", "缺少": "Missing", "前者是API2D的结束条件": "The former is the termination condition of API2D", "无需填写": "No need to fill in", "后缀": "Suffix", "扭转的范围": "Range of twisting", "是否在触发时清除历史": "Whether to clear history when triggered", "⭐多线程方法": "⭐Multi-threaded method", "消耗大量的内存": "Consumes a large amount of memory", "重组": "Reorganize", "高危设置! 常规情况下不要修改! 通过修改此设置": "High-risk setting! Do not modify under normal circumstances! Modify this setting", "检查USE_PROXY": "Check USE_PROXY", "标注节点的行数范围": "Range of line numbers for annotated nodes", "即不处理之前的对话历史": "That is, do not process previous conversation history", "即将编译PDF": "Compiling PDF", "没有设置ANTHROPIC_API_KEY选项": "ANTHROPIC_API_KEY option is not set", "非Openai官方接口返回了错误": "Non-Openai official interface returned an error", "您的 API_KEY 不满足任何一种已知的密钥格式": "Your API_KEY does not meet any known key format", "格式": "Format", "不能正常加载": "Cannot load properly", "🏃‍♂️🏃‍♂️🏃‍♂️ 子进程执行": "🏃‍♂️🏃‍♂️🏃‍♂️ Subprocess execution", "前缀": "Prefix", "创建AcsClient实例": "Create AcsClient instance", "⭐主进程执行": "⭐Main process execution", "增强稳健性": "Enhance robustness", "用来描述你的要求": "Used to describe your requirements", "举例": "For example", "⭐单线程方法": "⭐Single-threaded method", "后者是OPENAI的结束条件": "The latter is the termination condition of OPENAI", "防止proxies单独起作用": "Prevent proxies from working alone", "将两个PDF拼接": "Concatenate two PDFs", "最后一步处理": "The last step processing", "正在从github下载资源": "Downloading resources from github", "失败时": "When failed", "尚未加载": "Not loaded yet", "配合前缀可以把你的输入内容用引号圈起来": "With the prefix, you can enclose your input content in quotation marks", "我好！": "I'm good!", "默认 False": "<PERSON><PERSON><PERSON>", "的依赖": "Dependencies of", "并设置参数": "and set parameters", "会被加在你的输入之后": "Will be added after your input", "安装": "Installation", "一个单实例装饰器": "Single instance decorator", "自定义API KEY格式": "Customize API KEY format", "的参数": "Parameters of", "api2d等请求源": "api2d and other request sources", "逆转出错的段落": "Reverse the wrong paragraph", "没有设置ANTHROPIC_API_KEY": "ANTHROPIC_API_KEY is not set", "默认 True": "De<PERSON><PERSON>", "本项目现已支持OpenAI和Azure的api-key": "This project now supports OpenAI and Azure's api-key", "即可见": "Visible immediately", "请问什么是质子": "What is a proton?", "按钮是否可见": "Is the button visible?", "调用": "Call", "如果要使用": "If you want to use", "的参数！": "parameters!", "例如翻译、解释代码、润色等等": "such as translation, code interpretation, polishing, etc.", "响应异常": "Response exception", "响应中": "Responding", "请尝试英文Prompt": "Try English Prompt", "在运行过程中动态地修改多个配置": "Dynamically modify multiple configurations during runtime", "无法调用相关功能": "Unable to invoke related functions", "接驳虚空终端": "Connect to Void Terminal", "虚空终端插件的功能": "Functionality of Void Terminal plugin", "执行任意插件的命令": "Execute commands of any plugin", "修改调用函数": "Modify calling function", "获取简单聊天的默认参数": "Get default parameters for simple chat", "根据自然语言的描述": "Based on natural language description", "获取插件的句柄": "Get handle of plugin", "第四部分": "Part Four", "在运行过程中动态地修改配置": "Dynamically modify configurations during runtime", "请先把模型切换至gpt-*或者api2d-*": "Please switch the model to gpt-* or api2d-* first", "获取简单聊天的句柄": "Get handle of simple chat", "获取插件的默认参数": "Get default parameters of plugin", "GROBID服务不可用": "GROBID service is unavailable", "请问": "May I ask", "如果等待时间过长": "If the waiting time is too long", "编程": "programming", "5. 现在": "5. Now", "您不必读这个else分支": "You don't have to read this else branch", "用插件实现": "Implement with plugins", "插件分类默认选项": "Default options for plugin classification", "填写多个可以均衡负载": "Filling in multiple can balance the load", "色彩主题": "Color theme", "可能附带额外依赖 -=-=-=-=-=-=-": "May come with additional dependencies -=-=-=-=-=-=-", "讯飞星火认知大模型": "<PERSON><PERSON><PERSON><PERSON> cognitive model", "ParsingLuaProject的所有源文件 | 输入参数为路径": "All source files of ParsingLuaProject | Input parameter is path", "复制以下空间https": "Copy the following space https", "如果意图明确": "If the intention is clear", "如系统是Linux": "If the system is Linux", "├── 语音功能": "├── Voice function", "见Github wiki": "See Github wiki", "⭐ ⭐ ⭐ 立即应用配置": "⭐ ⭐ ⭐ Apply configuration immediately", "现在您只需要再次重复一次您的指令即可": "Now you just need to repeat your command again", "没辙了": "No way", "解析Jupyter Notebook文件 | 输入参数为路径": "Parse Jupyter Notebook file | Input parameter is path", "⭐ ⭐ ⭐ 确认插件参数": "⭐ ⭐ ⭐ Confirm plugin parameters", "找不到合适插件执行该任务": "Cannot find a suitable plugin to perform this task", "接驳VoidTerminal": "Connect to VoidTerminal", "**很好": "**Very good", "对话|编程": "Conversation&ImageGenerating|Programming", "对话|编程|学术": "Conversation|Programming|Academic", "4. 建议使用 GPT3.5 或更强的模型": "4. It is recommended to use GPT3.5 or a stronger model", "「请调用插件翻译PDF论文": "Please call the plugin to translate the PDF paper", "3. 如果您使用「调用插件xxx」、「修改配置xxx」、「请问」等关键词": "3. If you use keywords such as 'call plugin xxx', 'modify configuration xxx', 'please', etc.", "以下是一篇学术论文的基本信息": "The following is the basic information of an academic paper", "GROBID服务器地址": "GROBID server address", "修改配置": "Modify configuration", "理解PDF文档的内容并进行回答 | 输入参数为路径": "Understand the content of the PDF document and answer | Input parameter is path", "对于需要高级参数的插件": "For plugins that require advanced parameters", "🏃‍♂️🏃‍♂️🏃‍♂️ 主进程执行": "Main process execution 🏃‍♂️🏃‍♂️🏃‍♂️", "没有填写 HUGGINGFACE_ACCESS_TOKEN": "HUGGINGFACE_ACCESS_TOKEN not filled in", "调度插件": "Scheduling plugin", "语言模型": "Language model", "├── ADD_WAIFU 加一个live2d装饰": "├── ADD_WAIFU Add a live2d decoration", "初始化": "Initialization", "选择了不存在的插件": "Selected a non-existent plugin", "修改本项目的配置": "Modify the configuration of this project", "如果输入的文件路径是正确的": "If the input file path is correct", "2. 您可以打开插件下拉菜单以了解本项目的各种能力": "2. You can open the plugin dropdown menu to learn about various capabilities of this project", "VoidTerminal插件说明": "VoidTerminal plugin description", "无法理解您的需求": "Unable to understand your requirements", "默认 AdvancedArgs = False": "Default AdvancedArgs = False", "「请问Transformer网络的结构是怎样的": "What is the structure of the Transformer network?", "比如1812.10695": "For example, 1812.10695", "翻译README或MD": "Translate README or MD", "读取新配置中": "Reading new configuration", "假如偏离了您的要求": "If it deviates from your requirements", "├── THEME 色彩主题": "├── THEME color theme", "如果还找不到": "If still not found", "问": "Ask", "请检查系统字体": "Please check system fonts", "如果错误": "If there is an error", "作为替代": "As an alternative", "ParseJavaProject的所有源文件 | 输入参数为路径": "All source files of ParseJavaProject | Input parameter is path", "比对相同参数时生成的url与自己代码生成的url是否一致": "Check if the generated URL matches the one generated by your code when comparing the same parameters", "清除本地缓存数据": "Clear local cache data", "使用谷歌学术检索助手搜索指定URL的结果 | 输入参数为谷歌学术搜索页的URL": "Use Google Scholar search assistant to search for results of a specific URL | Input parameter is the URL of Google Scholar search page", "运行方法": "Running method", "您已经上传了文件**": "You have uploaded the file **", "「给爷翻译Arxiv论文": "Translate Arxiv papers for me", "请修改config中的GROBID_URL": "Please modify GROBID_URL in the config", "处理特殊情况": "Handling special cases", "不要自己瞎搞！」": "Don't mess around by yourself!", "LoadConversationHistoryArchive | 输入参数为路径": "LoadConversationHistoryArchive | Input parameter is a path", "| 输入参数是一个问题": "| Input parameter is a question", "├── CHATBOT_HEIGHT 对话窗的高度": "├── CHATBOT_HEIGHT Height of the chat window", "对C": "To C", "默认关闭": "De<PERSON>ult closed", "当前进度": "Current progress", "HUGGINGFACE的TOKEN": "HUGGINGFACE's TOKEN", "查找可用插件中": "Searching for available plugins", "下载LLAMA时起作用 https": "Works when downloading LLAMA https", "使用 AK": "Using AK", "正在执行任务": "Executing task", "保存当前的对话 | 不需要输入参数": "Save current conversation | No input parameters required", "对话": "Conversation", "图中鲜花怒放": "Flowers blooming in the picture", "批量将Markdown文件中文翻译为英文 | 输入参数为路径或上传压缩包": "Batch translate Chinese to English in Markdown files | Input parameter is a path or upload a compressed package", "ParsingCSharpProject的所有源文件 | 输入参数为路径": "ParsingCSharpProject's all source files | Input parameter is a path", "为我翻译PDF论文": "Translate PDF papers for me", "聊天对话": "Chat conversation", "拼接鉴权参数": "Concatenate authentication parameters", "请检查config中的GROBID_URL": "Please check the GROBID_URL in the config", "拼接字符串": "Concatenate strings", "您的意图可以被识别的更准确": "Your intent can be recognized more accurately", "该模型有七个 bin 文件": "The model has seven bin files", "但思路相同": "But the idea is the same", "你需要翻译": "You need to translate", "或者描述文件所在的路径": "Or the path of the description file", "请您上传文件": "Please upload the file", "不常用": "Not commonly used", "尚未充分测试的实验性插件 & 需要额外依赖的插件 -=--=-": "Experimental plugins that have not been fully tested & plugins that require additional dependencies -=--=-", "⭐ ⭐ ⭐ 选择插件": "⭐ ⭐ ⭐ Select plugin", "当前配置不允许被修改！如需激活本功能": "The current configuration does not allow modification! To activate this feature", "正在连接GROBID服务": "Connecting to GROBID service", "用户图形界面布局依赖关系示意图": "Diagram of user interface layout dependencies", "是否允许通过自然语言描述修改本页的配置": "Allow modifying the configuration of this page through natural language description", "self.chatbot被序列化": "self.chatbot is serialized", "本地Latex论文精细翻译 | 输入参数是路径": "Locally translate Latex papers with fine-grained translation | Input parameter is the path", "抱歉": "Sorry", "以下这部分是最早加入的最稳定的模型 -=-=-=-=-=-=-": "The following section is the earliest and most stable model added", "「用插件翻译README": "Translate README with plugins", "如果不正确": "If incorrect", "⭐ ⭐ ⭐ 读取可配置项目条目": "⭐ ⭐ ⭐ Read configurable project entries", "开始语言对话 | 没有输入参数": "Start language conversation | No input parameters", "谨慎操作 | 不需要输入参数": "Handle with caution | No input parameters required", "对英文Latex项目全文进行纠错处理 | 输入参数为路径或上传压缩包": "Correct the entire English Latex project | Input parameter is the path or upload compressed package", "如果需要处理文件": "If file processing is required", "提供图像的内容": "Provide the content of the image", "查看历史上的今天事件 | 不需要输入参数": "View historical events of today | No input parameters required", "这个稍微啰嗦一点": "This is a bit verbose", "多线程解析并翻译此项目的源码 | 不需要输入参数": "Parse and translate the source code of this project in multi-threading | No input parameters required", "此处打印出建立连接时候的url": "Print the URL when establishing the connection here", "精准翻译PDF论文为中文 | 输入参数为路径": "Translate PDF papers accurately into Chinese | Input parameter is the path", "检测到操作错误！当您上传文档之后": "Operation error detected! After you upload the document", "在线大模型配置关联关系示意图": "Online large model configuration relationship diagram", "你的填写的空间名如grobid": "Your filled space name such as grobid", "获取方法": "Get method", "| 输入参数为路径": "| Input parameter is the path", "⭐ ⭐ ⭐ 执行插件": "⭐ ⭐ ⭐ Execute plugin", "├── ALLOW_RESET_CONFIG 是否允许通过自然语言描述修改本页的配置": "├── ALLOW_RESET_CONFIG Whether to allow modifying the configuration of this page through natural language description", "重新页面即可生效": "Refresh the page to take effect", "设为public": "Set as public", "并在此处指定模型路径": "And specify the model path here", "分析用户意图中": "Analyzing user intent", "刷新下拉列表": "Refresh the drop-down list", "失败 当前语言模型": "Failed current language model", "1. 请用**自然语言**描述您需要做什么": "1. Please describe what you need to do in **natural language**", "对Latex项目全文进行中译英处理 | 输入参数为路径或上传压缩包": "Translate the full text of Latex projects from Chinese to English | Input parameter is the path or upload a compressed package", "没有配置BAIDU_CLOUD_API_KEY": "No configuration for BAIDU_CLOUD_API_KEY", "设置默认值": "Set default value", "如果太多了会导致gpt无法理解": "If there are too many, it will cause GPT to be unable to understand", "绿草如茵": "Green grass", "├── LAYOUT 窗口布局": "├── LAYOUT window layout", "用户意图理解": "User intent understanding", "生成RFC1123格式的时间戳": "Generate RFC1123 formatted timestamp", "欢迎您前往Github反馈问题": "Welcome to go to Github to provide feedback", "排除已经是按钮的插件": "Exclude plugins that are already buttons", "亦在下拉菜单中显示": "Also displayed in the dropdown menu", "导致无法反序列化": "Causing deserialization failure", "意图=": "Intent =", "章节": "Chapter", "调用插件": "Invoke plugin", "ParseRustProject的所有源文件 | 输入参数为路径": "All source files of ParseRustProject | Input parameter is path", "需要点击“函数插件区”按钮进行处理": "Need to click the 'Function Plugin Area' button for processing", "默认 AsButton = True": "De<PERSON><PERSON>on = True", "收到websocket错误的处理": "Handling websocket errors", "用插件": "Use Plugin", "没有选择任何插件组": "No plugin group selected", "答": "Answer", "可修改成本地GROBID服务": "Can modify to local GROBID service", "用户意图": "User intent", "对英文Latex项目全文进行润色处理 | 输入参数为路径或上传压缩包": "Polish the full text of English Latex projects | Input parameters are paths or uploaded compressed packages", "「我不喜欢当前的界面颜色": "I don't like the current interface color", "「请调用插件": "Please call the plugin", "VoidTerminal状态": "VoidTerminal status", "新配置": "New configuration", "支持Github链接": "Support Github links", "没有配置BAIDU_CLOUD_SECRET_KEY": "No BAIDU_CLOUD_SECRET_KEY configured", "获取当前VoidTerminal状态": "Get the current VoidTerminal status", "刷新按钮": "Refresh button", "为了防止pickle.dumps": "To prevent pickle.dumps", "放弃治疗": "Give up treatment", "可指定不同的生成长度、top_p等相关超参": "Can specify different generation lengths, top_p and other related hyperparameters", "请将题目和摘要翻译为": "Translate the title and abstract", "通过appid和用户的提问来生成请参数": "Generate request parameters through appid and user's question", "ImageGeneration | 输入参数字符串": "ImageGeneration | Input parameter string", "将文件拖动到文件上传区": "Drag and drop the file to the file upload area", "如果意图模糊": "If the intent is ambiguous", "星火认知大模型": "Spark Cognitive Big Model", "默认 Color = secondary": "Default Color = secondary", "此处也不需要修改": "No modification is needed here", "⭐ ⭐ ⭐ 分析用户意图": "⭐ ⭐ ⭐ Analyze user intent", "再试一次": "Try again", "请写bash命令实现以下功能": "Please write a bash command to implement the following function", "批量SummarizingWordDocuments | 输入参数为路径": "Batch SummarizingWordDocuments | Input parameter is the path", "/Users/<USER>/Desktop/旧文件/gpt/chatgpt_academic/crazy_functions/latex_fns中的python文件进行解析": "Parse the python file in /Users/<USER>/Desktop/旧文件/gpt/chatgpt_academic/crazy_functions/latex_fns", "当我要求你写bash命令时": "When I ask you to write a bash command", "├── AUTO_CLEAR_TXT 是否在提交时自动清空输入框": "├── AUTO_CLEAR_TXT Whether to automatically clear the input box when submitting", "按停止键终止": "Press the stop key to terminate", "文心一言": "Original text", "不能理解您的意图": "Cannot understand your intention", "用简单的关键词检测用户意图": "Detect user intention with simple keywords", "中文": "Chinese", "解析一个C++项目的所有源文件": "Parse all source files of a C++ project", "请求的Prompt为": "Requested prompt is", "参考本demo的时候可取消上方打印的注释": "You can remove the comments above when referring to this demo", "开始接收回复": "Start receiving replies", "接入讯飞星火大模型 https": "Access to Xunfei <PERSON>o large model https", "用该压缩包进行反馈": "Use this compressed package for feedback", "翻译Markdown或README": "Translate Markdown or README", "SK 生成鉴权签名": "SK generates authentication signature", "插件参数": "Plugin parameters", "需要访问中文Bing": "Need to access Chinese Bing", "ParseFrontendProject的所有源文件": "Parse all source files of ParseFrontendProject", "现在将执行效果稍差的旧版代码": "Now execute the older version code with slightly worse performance", "您需要明确说明并在指令中提到它": "You need to specify and mention it in the command", "请在config.py中设置ALLOW_RESET_CONFIG=True后重启软件": "Please set ALLOW_RESET_CONFIG=True in config.py and restart the software", "按照自然语言描述生成一个动画 | 输入参数是一段话": "Generate an animation based on natural language description | Input parameter is a sentence", "你的hf用户名如qingxu98": "Your hf username is qingxu98", "ArXiv论文精细翻译 | 输入参数arxiv论文的ID": "Fine translation of ArXiv paper | Input parameter is the ID of arxiv paper", "无法获取 abstract": "Unable to retrieve abstract", "尽可能地仅用一行命令解决我的要求": "Try to solve my request using only one command", "提取插件参数": "Extract plugin parameters", "配置修改完成": "Configuration modification completed", "正在修改配置中": "Modifying configuration", "ParsePythonProject的所有源文件": "All source files of ParsePythonProject", "请求错误": "Request error", "精准翻译PDF论文": "Accurate translation of PDF paper", "无法获取 authors": "Unable to retrieve authors", "该插件诞生时间不长": "This plugin has not been around for long", "返回项目根路径": "Return project root path", "BatchSummarizePDFDocuments的内容 | 输入参数为路径": "Content of BatchSummarizePDFDocuments | Input parameter is a path", "百度千帆": "<PERSON><PERSON>", "解析一个C++项目的所有头文件": "Parse all header files of a C++ project", "现在请您描述您的需求": "Now please describe your requirements", "该功能具有一定的危险性": "This feature has a certain level of danger", "收到websocket关闭的处理": "Processing when receiving websocket closure", "读取Tex论文并写摘要 | 输入参数为路径": "Read Tex paper and write abstract | Input parameter is the path", "地址为https": "The address is https", "限制最多前10个配置项": "Limit up to 10 configuration items", "6. 如果不需要上传文件": "6. If file upload is not needed", "默认 Group = 对话": "Default Group = Conversation", "五秒后即将重启！若出现报错请无视即可": "Restarting in five seconds! Please ignore if there is an error", "收到websocket连接建立的处理": "Processing when receiving websocket connection establishment", "批量生成函数的注释 | 输入参数为路径": "Batch generate function comments | Input parameter is the path", "聊天": "Cha<PERSON>", "但您可以尝试再试一次": "But you can try again", "千帆大模型平台": "Qianfan Big Model Platform", "直接运行 python tests/test_plugins.py": "Run python tests/test_plugins.py directly", "或是None": "Or None", "进行hmac-sha256进行加密": "Perform encryption using hmac-sha256", "批量总结音频或视频 | 输入参数为路径": "Batch summarize audio or video | Input parameter is path", "插件在线服务配置依赖关系示意图": "Plugin online service configuration dependency diagram", "开始初始化模型": "Start initializing model", "弱模型可能无法理解您的想法": "Weak model may not understand your ideas", "解除大小写限制": "Remove case sensitivity restriction", "跳过提示环节": "Skip prompt section", "接入一些逆向工程https": "Access some reverse engineering https", "执行完成": "Execution completed", "如果需要配置": "If configuration is needed", "此处不修改；如果使用本地或无地域限制的大模型时": "Do not modify here; if using local or region-unrestricted large models", "你是一个Linux大师级用户": "You are a Linux master-level user", "arxiv论文的ID是1812.10695": "The ID of the arxiv paper is 1812.10695", "而不是点击“提交”按钮": "Instead of clicking the 'Submit' button", "解析一个Go项目的所有源文件 | 输入参数为路径": "Parse all source files of a Go project | Input parameter is path", "对中文Latex项目全文进行润色处理 | 输入参数为路径或上传压缩包": "Polish the entire text of a Chinese Latex project | Input parameter is path or upload compressed package", "「生成一张图片": "Generate an image", "将Markdown或README翻译为中文 | 输入参数为路径或URL": "Translate Markdown or README to Chinese | Input parameters are path or URL", "训练时间": "Training time", "将请求的鉴权参数组合为字典": "Combine the requested authentication parameters into a dictionary", "对Latex项目全文进行英译中处理 | 输入参数为路径或上传压缩包": "Translate the entire text of Latex project from English to Chinese | Input parameters are path or uploaded compressed package", "内容如下": "The content is as follows", "用于高质量地读取PDF文档": "Used for high-quality reading of PDF documents", "上下文太长导致 token 溢出": "The context is too long, causing token overflow", "├── DARK_MODE 暗色模式 / 亮色模式": "├── DARK_MODE Dark mode / Light mode", "语言模型回复为": "The language model replies as", "from crazy_functions.chatglm微调工具 import 微调数据集生成": "from crazy_functions.chatglm fine-tuning tool import fine-tuning dataset generation", "为您选择了插件": "Selected plugin for you", "无法获取 title": "Unable to get title", "收到websocket消息的处理": "Processing of received websocket messages", "2023年": "2023", "清除所有缓存文件": "Clear all cache files", "├── PDF文档精准解析": "├── Accurate parsing of PDF documents", "论文我刚刚放到上传区了": "I just put the paper in the upload area", "生成url": "Generate URL", "以下部分是新加入的模型": "The following section is the newly added model", "学术": "Academic", "├── DEFAULT_FN_GROUPS 插件分类默认选项": "├── DEFAULT_FN_GROUPS Plugin classification default options", "不推荐使用": "Not recommended for use", "正在同时咨询": "Consulting simultaneously", "将Markdown翻译为中文 | 输入参数为路径或URL": "Translate Markdown to Chinese | Input parameters are path or URL", "Github网址是https": "The Github URL is https", "试着加上.tex后缀试试": "Try adding the .tex suffix", "对项目中的各个插件进行测试": "Test each plugin in the project", "插件说明": "Plugin description", "├── CODE_HIGHLIGHT 代码高亮": "├── CODE_HIGHLIGHT Code highlighting", "记得用插件": "Remember to use the plugin", "谨慎操作": "Handle with caution", "private_upload里面的文件名在解压zip后容易出现乱码": "The file name inside private_upload is prone to garbled characters after unzipping", "直接返回报错": "Direct return error", "临时的上传文件夹位置": "Temporary upload folder location", "使用latex格式 测试3 写出麦克斯韦方程组": "Write <PERSON>'s equations using latex format for test 3", "这是一张图片": "This is an image", "没有发现任何近期上传的文件": "No recent uploaded files found", "如url未成功匹配返回None": "Return None if the URL does not match successfully", "如果有Latex环境": "If there is a Latex environment", "第一次运行时": "When running for the first time", "创建工作路径": "Create a working directory", "向": "To", "执行中. 删除数据": "Executing. Deleting data", "CodeInterpreter开源版": "CodeInterpreter open source version", "建议选择更稳定的接口": "It is recommended to choose a more stable interface", "现在您点击任意函数插件时": "Now when you click on any function plugin", "请使用“LatexEnglishCorrection+高亮”插件": "Please use the 'LatexEnglishCorrection+Highlight' plugin", "安装完成": "Installation completed", "记得用插件！」": "Remember to use the plugin!", "结论": "Conclusion", "无法下载资源": "Unable to download resources", "首先排除一个one-api没有done数据包的第三方Bug情形": "First exclude a third-party bug where one-api does not have a done data package", "知识库中添加文件": "Add files to the knowledge base", "处理重名的章节": "Handling duplicate chapter names", "先上传文件素材": "Upload file materials first", "无法从google获取信息！": "Unable to retrieve information from Google!", "展示如下": "Display as follows", "「把Arxiv论文翻译成中文PDF": "Translate Arxiv papers into Chinese PDF", "论文我刚刚放到上传区了」": "I just put the paper in the upload area", "正在下载Gradio主题": "Downloading Gradio themes", "再运行此插件": "Run this plugin again", "记录近期文件": "Record recent files", "粗心检查": "Careful check", "更多主题": "More themes", "//huggingface.co/spaces/gradio/theme-gallery 可选": "//huggingface.co/spaces/gradio/theme-gallery optional", "由 test_on_result_chg": "By test_on_result_chg", "所有问询记录将自动保存在本地目录./": "All inquiry records will be automatically saved in the local directory ./", "正在解析论文": "Analyzing the paper", "逐个文件转移到目标路径": "Move each file to the target path", "最多重试5次": "Retry up to 5 times", "日志文件夹的位置": "Location of the log folder", "我们暂时无法解析此PDF文档": "We are temporarily unable to parse this PDF document", "文件检索": "File retrieval", "/**/chatGPT对话历史*.html": "/**/chatGPT conversation history*.html", "非OpenAI官方接口返回了错误": "Non-OpenAI official interface returned an error", "如果在Arxiv上匹配失败": "If the match fails on Arxiv", "文件进入知识库后可长期保存": "Files can be saved for a long time after entering the knowledge base", "您可以再次重试": "You can try again", "整理文件集合": "Organize file collection", "检测到有缺陷的非OpenAI官方接口": "Detected defective non-OpenAI official interface", "此插件不调用Latex": "This plugin does not call Latex", "移除过时的旧文件从而节省空间&保护隐私": "Remove outdated old files to save space & protect privacy", "代码我刚刚打包拖到上传区了」": "I just packed the code and dragged it to the upload area", "将图像转为灰度图像": "Convert the image to grayscale", "待排除": "To be excluded", "请勿修改": "Please do not modify", "crazy_functions/代码重写为全英文_多线程.py": "crazy_functions/code rewritten to all English_multi-threading.py", "开发中": "Under development", "请查阅Gradio主题商店": "Please refer to the Gradio theme store", "输出消息": "Output message", "其他情况": "Other situations", "获取文献失败": "Failed to retrieve literature", "可以通过再次调用本插件的方式": "You can use this plugin again by calling it", "保留下半部分": "Keep the lower half", "排除问题": "Exclude the problem", "知识库": "Knowledge base", "ParsePDF失败": "ParsePDF failed", "向知识库追加更多文档": "Append more documents to the knowledge base", "此处待注入的知识库名称id": "The knowledge base name ID to be injected here", "您需要构建知识库后再运行此插件": "You need to build the knowledge base before running this plugin", "判定是否为公式 | 测试1 写出洛伦兹定律": "Determine whether it is a formula | Test 1 write out the Lorentz law", "构建知识库后": "After building the knowledge base", "找不到本地项目或无法处理": "Unable to find local project or unable to process", "再做一个小修改": "Make another small modification", "解析整个Matlab项目": "Parse the entire Matlab project", "需要用GPT提取参数": "Need to extract parameters using GPT", "文件路径": "File path", "正在排队": "In queue", "-=-=-=-=-=-=-=-= 写出第1个文件": "-=-=-=-=-=-=-=-= Write the first file", "仅翻译后的文本 -=-=-=-=-=-=-=-=": "Translated text only -=-=-=-=-=-=-=-=", "对话通道": "Conversation channel", "找不到任何": "Unable to find any", "正在启动": "Starting", "开始创建新进程并执行代码! 时间限制": "Start creating a new process and executing the code! Time limit", "解析Matlab项目": "Parse Matlab project", "更换UI主题": "Change UI theme", "⭐ 开始啦 ！": "⭐ Let's start!", "先提取当前英文标题": "First extract the current English title", "睡一会防止触发google反爬虫": "Sleep for a while to prevent triggering Google anti-crawler", "测试": "Test", "-=-=-=-=-=-=-=-= 写出Markdown文件 -=-=-=-=-=-=-=-=": "-=-=-=-=-=-=-=-= Write out Markdown file", "如果index是1的话": "If the index is 1", "VoidTerminal已经实现了类似的代码": "VoidTerminal has already implemented similar code", "等待线程锁": "Waiting for thread lock", "那么我们默认代理生效": "Then we default to proxy", "结果是一个有效文件": "The result is a valid file", "⭐ 检查模块": "⭐ Check module", "备份一份History作为记录": "Backup a copy of History as a record", "作者Binary-Husky": "Author <PERSON><PERSON><PERSON><PERSON><PERSON>", "将csv文件转excel表格": "Convert CSV file to Excel table", "获取文章摘要": "Get article summary", "次代码生成尝试": "Attempt to generate code", "如果参数是空的": "If the parameter is empty", "请配置讯飞星火大模型的XFYUN_APPID": "Please configure XFYUN_APPID for the Xunfei Starfire model", "-=-=-=-=-=-=-=-= 写出第2个文件": "Write the second file", "代码生成阶段结束": "Code generation phase completed", "则进行提醒": "Then remind", "处理异常": "Handle exception", "可能触发了google反爬虫机制": "May have triggered Google anti-crawler mechanism", "AnalyzeAMatlabProject的所有源文件": "All source files of AnalyzeAMatlabProject", "写入": "Write", "我们5秒后再试一次...": "Let's try again in 5 seconds...", "判断一下用户是否错误地通过对话通道进入": "Check if the user entered through the dialogue channel by mistake", "结果": "Result", "2. 如果没有文件": "2. If there is no file", "由 test_on_sentence_end": "By test_on_sentence_end", "则直接使用first section name": "Then directly use the first section name", "太懒了": "Too lazy", "记录当前的大章节标题": "Record the current chapter title", "然后再次点击该插件! 至于您的文件": "Then click the plugin again! As for your file", "此次我们的错误追踪是": "This time our error tracking is", "首先在arxiv上搜索": "First search on arxiv", "被新插件取代": "Replaced by a new plugin", "正在处理文件": "Processing file", "除了连接OpenAI之外": "In addition to connecting OpenAI", "我们检查一下": "Let's check", "进度": "Progress", "处理少数情况下的特殊插件的锁定状态": "Handle the locked state of special plugins in a few cases", "⭐ 开始执行": "⭐ Start execution", "正常情况": "Normal situation", "下个句子中已经说完的部分": "The part that has already been said in the next sentence", "首次运行需要花费较长时间下载NOUGAT参数": "The first run takes a long time to download NOUGAT parameters", "使用tex格式公式 测试2 给出柯西不等式": "Use the tex format formula to test 2 and give the <PERSON><PERSON><PERSON> inequality", "无法从bing获取信息！": "Unable to retrieve information from Bing!", "秒. 请等待任务完成": "Wait for the task to complete", "开始干正事": "Start doing real work", "需要花费较长时间下载NOUGAT参数": "It takes a long time to download NOUGAT parameters", "然后再次点击该插件": "Then click the plugin again", "受到bing限制": "Restricted by <PERSON>", "检索文章的历史版本的题目": "Retrieve the titles of historical versions of the article", "收尾": "Wrap up", "给定了task": "Given a task", "某段话的整个句子": "The whole sentence of a paragraph", "-=-=-=-=-=-=-=-= 写出HTML文件 -=-=-=-=-=-=-=-=": "-=-=-=-=-=-=-=-= Write out HTML file -=-=-=-=-=-=-=-=", "当前文件": "Current file", "请在输入框内填写需求": "Please fill in the requirements in the input box", "结果是一个字符串": "The result is a string", "用插件实现」": "Implemented with a plugin", "⭐ 到最后一步了": "⭐ Reached the final step", "重新修改当前part的标题": "Modify the title of the current part again", "请勿点击“提交”按钮或者“基础功能区”按钮": "Do not click the 'Submit' button or the 'Basic Function Area' button", "正在执行命令": "Executing command", "检测到**滞留的缓存文档**": "Detected **stuck cache document**", "第三步": "Step three", "失败了~ 别担心": "Failed~ Don't worry", "动态代码解释器": "Dynamic code interpreter", "开始执行": "Start executing", "不给定task": "No task given", "正在加载NOUGAT...": "Loading NOUGAT...", "精准翻译PDF文档": "Accurate translation of PDF documents", "时间限制TIME_LIMIT": "Time limit TIME_LIMIT", "翻译前后混合 -=-=-=-=-=-=-=-=": "Mixed translation before and after -=-=-=-=-=-=-=-=", "搞定代码生成": "Code generation is done", "插件通道": "Plugin channel", "智能体": "Intelligent agent", "切换界面明暗 ☀": "Switch interface brightness ☀", "交换图像的蓝色通道和红色通道": "Swap blue channel and red channel of the image", "作为函数参数": "As a function parameter", "先挑选偶数序列号": "First select even serial numbers", "仅供测试": "For testing only", "执行成功了": "Execution succeeded", "开始逐个文件进行处理": "Start processing files one by one", "当前文件处理列表": "Current file processing list", "执行失败了": "Execution failed", "请及时处理": "Please handle it in time", "源文件": "Source file", "裁剪图像": "Crop image", "插件动态生成插件": "Dynamic generation of plugins", "正在验证上述代码的有效性": "Validating the above code", "⭐ = 关键步骤": "⭐ = Key step", "!= 0 代表“提交”键对话通道": "!= 0 represents the 'Submit' key dialogue channel", "解析python源代码项目": "Parsing Python source code project", "请检查PDF是否损坏": "Please check if the PDF is damaged", "插件动态生成": "Dynamic generation of plugins", "⭐ 分离代码块": "⭐ Separating code blocks", "已经被记忆": "Already memorized", "默认用英文的": "Default to English", "错误追踪": "Error tracking", "对话&编程|编程|学术|智能体": "Conversation&ImageGenerating|Programming|Academic|Intelligent agent", "请检查": "Please check", "检测到被滞留的缓存文档": "Detected cached documents being left behind", "还有哪些场合允许使用代理": "What other occasions allow the use of proxies", "1. 如果有文件": "1. If there is a file", "执行开始": "Execution starts", "代码生成结束": "Code generation ends", "请及时点击“**保存当前对话**”获取所有滞留文档": "Please click '**Save Current Dialogue**' in time to obtain all cached documents", "需点击“**函数插件区**”按钮进行处理": "Click the '**Function Plugin Area**' button for processing", "此函数已经弃用": "This function has been deprecated", "以后再写": "Write it later", "返回给定的url解析出的arxiv_id": "Return the arxiv_id parsed from the given URL", "⭐ 文件上传区是否有东西": "⭐ Is there anything in the file upload area", "Nougat解析论文失败": "Nougat failed to parse the paper", "本源代码中": "In this source code", "或者基础功能通道": "Or the basic function channel", "使用zip压缩格式": "Using zip compression format", "受到google限制": "Restricted by Google", "如果是": "If it is", "不用担心": "don't worry", "显示/隐藏自定义菜单": "Show/Hide Custom Menu", "1. 输入文本": "1. Enter Text", "微软AutoGen": "Microsoft AutoGen", "在没有声音之后": "After No Sound", "⭐ 主进程 Docker 外挂文件夹监控": "⭐ Main Process Docker External Folder Monitoring", "请求任务": "Request Task", "推荐上传压缩文件": "Recommend Uploading Compressed File", "我准备好处理下一个问题了": "I'm ready to handle the next question", "输入要反馈的内容": "Enter the content to be feedbacked", "当已经存在一个正在运行的MultiAgentTerminal时": "When there is already a running MultiAgentTerminal", "也根据时间间隔": "Also according to the time interval", "自定义功能": "Custom Function", "上传文件后会自动把输入区修改为相应路径": "After uploading the file, the input area will be automatically modified to the corresponding path", "缺少docker运行环境！": "Missing docker runtime environment!", "暂不支持中转": "Transit is not supported temporarily", "一些第三方接口的出现这样的错误": "Some third-party interfaces encounter such errors", "项目Wiki": "Project Wiki", "但是我们把上一帧同样加上": "But we also add the previous frame", "AutoGen 执行失败": "AutoGen execution failed", "程序抵达用户反馈节点": "The program reaches the user feedback node", "预制功能": "Prefabricated Function", "输入新按钮名称": "Enter the new button name", "| 不需要输入参数": "| No input parameters required", "如果有新文件出现": "If there is a new file", "Bug反馈": "<PERSON><PERSON>", "指定翻译成何种语言": "Specify the language to translate into", "点击保存当前的对话按钮": "Click the save current conversation button", "如果您需要补充些什么": "If you need to add something", "HTTPS 秘钥和证书": "HTTPS Key and Certificate", "输入exit": "Enter exit", "输入新提示后缀": "Enter a new prompt suffix", "如果是文本文件": "If it is a text file", "支持动态切换主题": "Support dynamic theme switching", "并与self.previous_work_dir_files中所记录的文件进行对比": "And compare with the files recorded in self.previous_work_dir_files", "作者 Microsoft & Binary-Husky": "Author Microsoft & Binary-<PERSON><PERSON>", "请在自定义菜单中定义提示词前缀": "Please define the prefix of the prompt word in the custom menu", "一般情况下您不需要说什么": "In general, you don't need to say anything", "「暗色主题已启用": "Dark theme enabled", "继续向服务器发送n次音频数据": "Continue to send audio data to the server n times", "获取fp的拓展名": "Get the extension name of fp", "指令安装内置Gradio及其他依赖": "Command to install built-in Gradio and other dependencies", "查看自动更新": "Check for automatic updates", "则更新self.previous_work_dir_files中": "Then update in self.previous_work_dir_files", "看门狗耐心": "Watchdog patience", "检测到新生图像": "Detected new image", "等待AutoGen执行结果": "Waiting for AutoGen execution result", "自定义菜单": "Custom menu", "保持链接激活": "Keep the link active", "已经被新插件取代": "Has been replaced by a new plugin", "检查当前的模型是否符合要求": "Check if the current model meets the requirements", "交互功能模板Demo函数": "Interactive function template Demo function", "上一帧没有人声": "No human voice in the previous frame", "用于判断异常": "Used to judge exceptions", "请阅读Wiki": "Please read the Wiki", "查找wallhaven.cc的壁纸": "Search for wallpapers on wallhaven.cc", "2. 点击任意基础功能区按钮": "2. Click any button in the basic function area", "一些垃圾第三方接口的出现这样的错误": "Some errors caused by garbage third-party interfaces", "再次点击VoidTerminal": "Click VoidTerminal again", "结束信号已明确": "The end signal is clear", "获取代理失败 无代理状态下很可能无法访问OpenAI家族的模型及谷歌学术 建议": "Failed to get proxy. It is very likely that you will not be able to access OpenAI family models and Google Scholar without a proxy. It is recommended", "界面外观": "Interface appearance", "如果您想终止程序": "If you want to terminate the program", "2. 点击任意函数插件区按钮": "Click any function plugin area button", "绕过openai访问频率限制": "Bypass openai access frequency limit", "配置暗色主题或亮色主题": "Configure dark theme or light theme", "自定义按钮的最大数量限制": "Maximum number limit for custom buttons", "函数插件区使用说明": "Instructions for function plugin area", "如何语音对话": "How to have a voice conversation", "清空输入区": "Clear input area", "文档清单如下": "The document list is as follows", "由 audio_convertion_thread": "By audio_convertion_thread", "音频的可视化表现": "Visual representation of audio", "然后直接点击“提交”以继续": "Then click 'Submit' to continue", "运行MultiAgentTerminal": "Run MultiAgentTerminal", "自定义按钮1": "Custom button 1", "查看历史上的今天事件": "View events from history", "如遇到Bug请前往": "If you encounter a bug, please go to", "当前插件只支持": "The current plugin only supports", "而不是再次启动一个新的MultiAgentTerminal": "Instead of starting a new MultiAgentTerminal again", "用户代理或助理代理未定义": "User agent or assistant agent is not defined", "运行阶段-": "Running phase-", "随机选择": "Random selection", "直接点击“提交”以继续": "Click 'Submit' to continue", "使用项目内置Gradio获取最优体验! 请运行": "Use the built-in Gradio for the best experience! Please run", "直接点击“提交”以终止AutoGen并解锁": "Click 'Submit' to terminate AutoGen and unlock", "Github源代码开源和更新": "Github source code is open source and updated", "直接将用户输入传递给它": "Pass user input directly to it", "这是一个面向开发者的插件Demo": "This is a plugin demo for developers", "帮助": "Help", "普通对话使用说明": "Instructions for normal conversation", "自定义按钮": "Custom button", "即使没有声音": "Even without sound", "⭐ 主进程": "⭐ Main process", "基础功能区使用说明": "Basic Function Area Usage Instructions", "提前读取一些信息": "Read some information in advance", "当用户点击了“等待反馈”按钮时": "When the user clicks the 'Wait for Feedback' button", "选择一个需要自定义基础功能区按钮": "Select a button in the Basic Function Area that needs to be customized", "VoidTerminal使用说明": "VoidTerminal Usage Instructions", "兼容一下吧": "Let's make it compatible", "⭐⭐ 子进程执行": "⭐⭐ Subprocess execution", "首次": "For the first time", "则直接显示文本内容": "Then display the text content directly", "更新状态": "Update status", "2. 点击提交": "2. <PERSON><PERSON> Submit", "⭐⭐ 子进程": "⭐⭐ Subprocess", "输入新提示前缀": "Enter a new prompt prefix", "等待用户输入超时": "Wait for user input timeout", "把新文件和发生变化的文件的路径记录到 change_list 中": "Record the paths of new files and files that have changed in change_list", "或者上传文件": "Or upload a file", "或者文件的修改时间发生变化": "Or the modification time of the file has changed", "1. 输入路径/问题": "1. Enter path/question", "尝试直接连接": "Try to connect directly", "未来将删除": "Will be deleted in the future", "请在自定义菜单中定义提示词后缀": "Please define the suffix of the prompt word in the custom menu", "将executor存储到cookie中": "Store the executor in the cookie", "1. 输入问题": "1. Enter question", "发送一些音频片段给服务器": "Send some audio clips to the server", "点击VoidTerminal": "Click VoidTerminal", "扫描路径下的所有文件": "Scan all files under the path", "检测到新生文档": "Detect new documents", "预热tiktoken模块": "Preheat the tiktoken module", "等待您的进一步指令": "Waiting for your further instructions", "实时语音对话": "Real-time voice conversation", "确认并保存": "Confirm and save", "「亮色主题已启用": "Light theme enabled", "终止AutoGen程序": "Terminate AutoGen program", "然后根据提示输入指令": "Then enter the command as prompted", "请上传本地文件/压缩包供“函数插件区”功能调用": "Please upload local files/zip packages for 'Function Plugin Area' function call", "上传文件": "Upload file", "上一帧是否有人说话": "Was there anyone speaking in the previous frame", "这是一个时刻聆听着的语音对话助手 | 没有输入参数": "This is a voice conversation assistant that is always listening | No input parameters", "常见问题请查阅": "Please refer to the FAQ for common questions", "更换模型 & Prompt": "Change model & Prompt", "如何保存对话": "How to save the conversation", "处理任务": "Process task", "加载已保存": "Load saved", "打开浏览器页面": "Open browser page", "解锁插件": "Unlock plugin", "如果话筒激活 / 如果处于回声收尾阶段": "If the microphone is active / If it is in the echo tail stage", "分辨率": "Resolution", "分析行业动态": "Analyze industry trends", "在项目实施过程中提供支持": "Provide support during project implementation", "azure 对齐支持 -=-=-=-=-=-=-": "Azure alignment support -=-=-=-=-=-=-", "默认的系统提示词": "Default system prompts", "为您解释复杂的技术概念": "Explain complex technical concepts to you", "提供项目管理和协作建议": "Provide project management and collaboration advice", "请从AVAIL_LLM_MODELS中选择": "Please select from AVAIL_LLM_MODELS", "提高编程能力": "Improve programming skills", "请注意Newbing组件已不再维护": "Please note that the Newbing component is no longer maintained", "用于定义和切换多个azure模型 --": "Used to define and switch between multiple Azure models --", "支持 256x256": "Supports 256x256", "定义界面上“询问多个GPT模型”插件应该使用哪些模型": "Define which models the 'Ask multiple GPT models' plugin should use on the interface", "必须是.png格式": "Must be in .png format", "tokenizer只用于粗估token数量": "The tokenizer is only used to estimate the number of tokens", "协助您进行文案策划和内容创作": "Assist you in copywriting and content creation", "帮助您巩固编程基础": "Help you consolidate your programming foundation", "修改需求": "Modify requirements", "确保项目顺利进行": "Ensure the smooth progress of the project", "帮助您了解市场发展和竞争态势": "Help you understand market development and competitive situation", "不需要动态切换": "No need for dynamic switching", "解答您在学习过程中遇到的问题": "Answer the questions you encounter during the learning process", "Endpoint不正确": "Endpoint is incorrect", "提供编程思路和建议": "Provide programming ideas and suggestions", "先上传图片": "Upload the image first", "提供计算机科学、数据科学、人工智能等相关领域的学习资源和建议": "Provide learning resources and advice in computer science, data science, artificial intelligence, and other related fields", "提供写作建议和技巧": "Provide writing advice and tips", "间隔": "Interval", "此后不需要在此处添加api2d的接口了": "No need to add the api2d interface here anymore", "4. 学习辅导": "4. Learning guidance", "智谱AI大模型": "Zhipu AI large model", "3. 项目支持": "3. Project support", "但这是意料之中的": "But this is expected", "检查endpoint是否可用": "Check if the endpoint is available", "接入智谱大模型": "Access the intelligent spectrum model", "如果您有任何问题或需要解答的议题": "If you have any questions or topics that need answers", "api2d 对齐支持 -=-=-=-=-=-=-": "api2d alignment support -=-=-=-=-=-=-", "支持多线程": "Support multi-threading", "再输入修改需求": "Enter modification requirements again", "Endpoint不满足要求": "Endpoint does not meet the requirements", "检查endpoint是否合法": "Check if the endpoint is valid", "为您制定技术战略提供参考和建议": "Provide reference and advice for developing your technical strategy", "支持 1024x1024": "Support 1024x1024", "因为下面的代码会自动添加": "Because the following code will be automatically added", "尝试加载模型": "Try to load the model", "使用DALLE3生成图片 | 输入参数字符串": "Use DALLE3 to generate images | Input parameter string", "当前论文无需解析": "The current paper does not need to be parsed", "单个azure模型部署": "Deploy a single Azure model", "512x512 或 1024x1024": "512x512 or 1024x1024", "至少是8k上下文的模型": "A model with at least 8k context", "自动忽略重复的输入": "Automatically ignore duplicate inputs", "让您更好地掌握知识": "Help you better grasp knowledge", "文件列表": "File list", "并在不同模型之间用": "And use it between different models", "插件调用出错": "Plugin call error", "帮助您撰写文章、报告、散文、故事等": "Help you write articles, reports, essays, stories, etc.", "*实验性功能*": "*Experimental feature*", "2. 编程": "2. Programming", "让您更容易理解": "Make it easier for you to understand", "的最大上下文长度太短": "The maximum context length is too short", "方法二": "Method 2", "多个azure模型部署+动态切换": "Deploy multiple Azure models + dynamic switching", "详情请见额外文档 docs\\use_azure.md": "For details, please refer to the additional document docs\\use_azure.md", "包括但不限于 Python、Java、C++ 等": "Including but not limited to Python, Java, C++, etc.", "为您提供业界最新的新闻和技术趋势": "Providing you with the latest industry news and technology trends", "自动检测并屏蔽失效的KEY": "Automatically detect and block invalid keys", "请勿使用": "Please do not use", "最后输入分辨率": "Enter the resolution at last", "图片": "Image", "请检查AZURE_ENDPOINT的配置! 当前的Endpoint为": "Please check the configuration of AZURE_ENDPOINT! The current Endpoint is", "图片修改": "Image modification", "已经收集到所有信息": "All information has been collected", "加载API_KEY": "Loading API_KEY", "协助您编写代码": "Assist you in writing code", "我可以为您提供以下服务": "I can provide you with the following services", "排队中请稍候 ...": "Please wait in line ...", "建议您使用英文提示词": "It is recommended to use English prompts", "不能支撑AutoGen运行": "Cannot support AutoGen operation", "帮助您解决编程问题": "Help you solve programming problems", "上次用户反馈输入为": "Last user feedback input is", "请随时告诉我您的需求": "Please feel free to tell me your needs", "有 sys_prompt 接口": "There is a sys_prompt interface", "可能会覆盖之前的配置": "May overwrite previous configuration", "5. 行业动态和趋势分析": "5. Industry dynamics and trend analysis", "正在等待线程锁": "Waiting for thread lock", "请输入分辨率": "Please enter the resolution", "接驳void-terminal": "Connecting to void-terminal", "启动DALLE2图像修改向导程序": "Launching DALLE2 image modification wizard program", "加载模型失败": "Failed to load the model", "是否使用Docker容器运行代码": "Whether to run the code using Docker container", "请输入修改需求": "Please enter modification requirements", "作为您的写作和编程助手": "As your writing and programming assistant", "然后再次点击本插件": "Then click this plugin again", "需要动态切换": "Dynamic switching is required", "文心大模型4.0": "Wenxin Large Model 4.0", "找不到任何.pdf拓展名的文件": "Cannot find any file with .pdf extension", "在使用AutoGen插件时": "When using the AutoGen plugin", "协助您规划项目进度和任务分配": "Assist you in planning project schedules and task assignments", "1. 写作": "1. Writing", "你亲手写的api名称": "The API name you wrote yourself", "使用DALLE2生成图片 | 输入参数字符串": "Generate images using DALLE2 | Input parameter string", "方法一": "Method 1", "我会尽力提供帮助": "I will do my best to provide assistance", "多个azure模型": "Multiple Azure models", "准备就绪": "Ready", "请随时提问": "Please feel free to ask", "如果需要使用AZURE": "If you need to use AZURE", "如果不是本地模型": "If it is not a local model", "AZURE_CFG_ARRAY中配置的模型必须以azure开头": "The models configured in AZURE_CFG_ARRAY must start with 'azure'", "API key has been deactivated. OpenAI以账户失效为由": "API key has been deactivated. OpenAI considers it as an account failure", "请先上传图像": "Please upload the image first", "高优先级": "High priority", "请配置ZHIPUAI_API_KEY": "Please configure ZHIPUAI_API_KEY", "单个azure模型": "Single Azure model", "预留参数 context 未实现": "Reserved parameter 'context' not implemented", "在输入区输入临时API_KEY后提交": "Submit after entering temporary API_KEY in the input area", "鸟": "<PERSON>", "图片中需要修改的位置用橡皮擦擦除为纯白色": "Erase the areas in the image that need to be modified with an eraser to pure white", "└── PDF文档精准解析": "└── Accurate parsing of PDF documents", "└── ALLOW_RESET_CONFIG 是否允许通过自然语言描述修改本页的配置": "└── ALLOW_RESET_CONFIG Whether to allow modifying the configuration of this page through natural language description", "等待指令": "Waiting for instructions", "不存在": "Does not exist", "选择游戏": "Select game", "本地大模型示意图": "Local large model diagram", "无视此消息即可": "You can ignore this message", "即RGB=255": "That is, RGB=255", "如需追问": "If you have further questions", "也可以是具体的模型路径": "It can also be a specific model path", "才会起作用": "Will take effect", "下载失败": "Download failed", "网页刷新后失效": "Invalid after webpage refresh", "crazy_functions.互动小游戏-": "crazy_functions.Interactive mini game-", "右对齐": "Right alignment", "您可以调用下拉菜单中的“LoadConversationHistoryArchive”还原当下的对话": "You can use the 'LoadConversationHistoryArchive' in the drop-down menu to restore the current conversation", "左对齐": "Left alignment", "使用默认的 FP16": "Use default FP16", "一小时": "One hour", "从而方便内存的释放": "Thus facilitating memory release", "如何临时更换API_KEY": "How to temporarily change API_KEY", "请输入 1024x1024-HD": "Please enter 1024x1024-HD", "使用 INT8 量化": "Use INT8 quantization", "3. 输入修改需求": "3. Enter modification requirements", "刷新界面 由于请求gpt需要一段时间": "Refreshing the interface takes some time due to the request for gpt", "随机小游戏": "Random mini game", "那么请在下面的QWEN_MODEL_SELECTION中指定具体的模型": "So please specify the specific model in QWEN_MODEL_SELECTION below", "表值": "Table value", "我画你猜": "I draw, you guess", "狗": "Dog", "2. 输入分辨率": "2. Enter resolution", "鱼": "Fish", "尚未完成": "Not yet completed", "表头": "Table header", "填localhost或者127.0.0.1": "Fill in localhost or 127.0.0.1", "请上传jpg格式的图片": "Please upload images in jpg format", "API_URL_REDIRECT填写格式是错误的": "The format of API_URL_REDIRECT is incorrect", "├──  RWKV的支持见Wiki": "Support for RWKV is available in the Wiki", "如果中文Prompt效果不理想": "If the Chinese prompt is not effective", "/SEAFILE_LOCAL/50503047/我的资料库/学位/paperlatex/aaai/Fu_8368_with_appendix": "/SEAFILE_LOCAL/50503047/My Library/Degree/paperlatex/aaai/Fu_8368_with_appendix", "只有当AVAIL_LLM_MODELS包含了对应本地模型时": "Only when AVAIL_LLM_MODELS contains the corresponding local model", "选择本地模型变体": "Choose the local model variant", "如果您确信自己没填错": "If you are sure you haven't made a mistake", "PyPDF2这个库有严重的内存泄露问题": "PyPDF2 library has serious memory leak issues", "整理文件集合 输出消息": "Organize file collection and output message", "没有检测到任何近期上传的图像文件": "No recently uploaded image files detected", "游戏结束": "Game over", "调用结束": "Call ended", "猫": "Cat", "请及时切换模型": "Please switch models in time", "次中": "In the meantime", "如需生成高清图像": "If you need to generate high-definition images", "CPU 模式": "CPU mode", "项目目录": "Project directory", "动物": "Animal", "居中对齐": "Center alignment", "请注意拓展名需要小写": "Please note that the extension name needs to be lowercase", "重试第": "Retry", "实验性功能": "Experimental feature", "猜错了": "Wrong guess", "打开你的代理软件查看代理协议": "Open your proxy software to view the proxy agreement", "您不需要再重复强调该文件的路径了": "You don't need to emphasize the file path again", "请阅读": "Please read", "请直接输入您的问题": "Please enter your question directly", "API_URL_REDIRECT填错了": "API_URL_REDIRECT is filled incorrectly", "谜底是": "The answer is", "第一个模型": "The first model", "你猜对了！": "You guessed it right!", "已经接收到您上传的文件": "The file you uploaded has been received", "您正在调用“图像生成”插件": "You are calling the 'Image Generation' plugin", "刷新界面 界面更新": "Refresh the interface, interface update", "如果之前已经初始化了游戏实例": "If the game instance has been initialized before", "文件": "File", "老鼠": "Mouse", "列2": "Column 2", "等待图片": "Waiting for image", "使用 INT4 量化": "Use INT4 quantization", "from crazy_functions.互动小游戏 import 随机小游戏": "TranslatedText", "游戏主体": "TranslatedText", "该模型不具备上下文对话能力": "TranslatedText", "列3": "TranslatedText", "清理": "TranslatedText", "检查量化配置": "TranslatedText", "如果游戏结束": "TranslatedText", "蛇": "TranslatedText", "则继续该实例；否则重新初始化": "TranslatedText", "e.g. cat and 猫 are the same thing": "TranslatedText", "第三个模型": "TranslatedText", "如果你选择Qwen系列的模型": "TranslatedText", "列4": "TranslatedText", "输入“exit”获取答案": "TranslatedText", "把它放到子进程中运行": "TranslatedText", "列1": "TranslatedText", "使用该模型需要额外依赖": "TranslatedText", "再试试": "TranslatedText", "1. 上传图片": "TranslatedText", "保存状态": "TranslatedText", "GPT-Academic对话存档": "TranslatedText", "Arxiv论文精细翻译": "TranslatedText", "from crazy_functions.AdvancedFunctionTemplate import 测试图表渲染": "from crazy_functions.AdvancedFunctionTemplate import test_chart_rendering", "测试图表渲染": "test_chart_rendering", "请使用「LatexEnglishCorrection+高亮修正位置": "Please use 'LatexEnglishCorrection+highlight corrected positions", "输出代码片段中！": "Output code snippet!", "使用多种方式尝试切分文本": "Attempt to split the text in various ways", "你是一个作家": "You are a writer", "如果无法从中得到答案": "If unable to get an answer from it", "无法读取以下数据": "Unable to read the following data", "不允许直接报错": "Direct error reporting is not allowed", "您也可以使用插件参数指定绘制的图表类型": "You can also specify the type of chart to be drawn using plugin parameters", "不要包含太多情节": "Do not include too many plots", "翻译为中文后重新编译为PDF": "Recompile into PDF after translating into Chinese", "采样温度": "Sampling temperature", "直接修改config.py": "Directly modify config.py", "处理文件": "Handle file", "判断返回是否正确": "Determine if the return is correct", "gemini 不允许对话轮次为偶数": "Gemini does not allow the number of dialogue rounds to be even", "8 象限提示图": "8-quadrant prompt diagram", "基于上下文的prompt模版": "Context-based prompt template", "^开始": "^Start", "输出文本的最大tokens限制": "Maximum tokens limit for output text", "在这个例子中": "In this example", "以及处理PDF文件的示例代码": "And example code for handling PDF files", "更新cookie": "Update cookie", "获取公共缩进": "Get public indentation", "请你给出围绕“{subject}”的序列图": "Please provide a sequence diagram around '{subject}'", "请确保使用小写的模型名称": "Please ensure the use of lowercase model names", "出现人物时": "When characters appear", "azure模型对齐支持 -=-=-=-=-=-=-": "Azure model alignment support -=-=-=-=-=-=-", "请一分钟后重试": "Please try again in one minute", "解析GEMINI消息出错": "Error parsing GEMINI message", "选择提示词": "Select prompt words", "取值范围是": "The value range is", "它会在": "It will be", "加载文件": "Load file", "是预定义按钮": "Is a predefined button", "消息": "Message", "默认搜索5条结果": "Default search for 5 results", "第 2 部分": "Part 2", "我们采样一个特殊的手段": "We sample a special method", "后端开发": "Backend development", "接下来提取md中的一级/二级标题作为摘要": "Next, extract the first/second-level headings in md as summaries", "一个年轻人穿过天安门广场向纪念堂走去": "A young person walks through Tiananmen Square towards the Memorial Hall", "将会使用这些摘要绘制图表": "Will use these summaries to draw charts", "8-象限提示图": "8-quadrant prompt diagram", "首先": "First", "设计了此接口": "Designed this interface", "本地模型": "Local model", "所有图像仅在最后一个问题中提供": "All images are provided only in the last question", "如连续3次判断失败将会使用流程图进行绘制": "If there are 3 consecutive failures, a flowchart will be used to draw", "为了更灵活地接入one-api多模型管理界面": "To access the one-api multi-model management interface more flexibly", "UI设计": "UI design", "不允许在答案中添加编造成分": "Fabrication is not allowed in the answer", "尽可能地": "As much as possible", "先在前端快速清除chatbot&status": "First, quickly clear chatbot & status in the frontend", "You exceeded your current quota. Cohere以账户额度不足为由": "You exceeded your current quota. Cohere due to insufficient account quota", "合并所有的标题": "Merge all headings", "跳过下载": "Skip download", "中生产图表": "Production Chart", "如输入区内容不是文件则直接返回输入区内容": "Return the content of the input area directly if it is not a file", "用温度取样的另一种方法": "Another method of temperature sampling", "不需要解释原因": "No need to explain the reason", "一场延续了两万年的星际战争已接近尾声": "An interstellar war that has lasted for 20,000 years is drawing to a close", "依次处理文件": "Process files in order", "第一幕的字数少于300字": "The first act has fewer than 300 characters", "已成功加载": "Successfully loaded", "还是web渲染": "Web rendering", "解析分辨率": "Resolution parsing", "如果剩余文本的token数大于限制": "If the number of remaining text tokens exceeds the limit", "你可以修改整个句子的顺序以确保翻译后的段落符合中文的语言习惯": "You can change the order of the whole sentence to ensure that the translated paragraph is in line with Chinese language habits", "并同时充分考虑中文的语法、清晰、简洁和整体可读性": "And at the same time, fully consider Chinese grammar, clarity, conciseness, and overall readability", "否则返回": "Otherwise return", "一个特殊标记": "A special mark", "4. 后续剧情发展4": "4. Plot development", "恢复默认": "Restore default", "转义点号": "Escape period", "检查DASHSCOPE_API_KEY": "Check DASHSCOPE_API_KEY", "阿里灵积云API_KEY": "Aliyun API_KEY", "文件是否存在": "Check if the file exists", "您的选择是": "Your choice is", "处理用户对话": "Handle user dialogue", "即": "That is", "将会由对话模型首先判断适合的图表类型": "The dialogue model will first determine the appropriate chart type", "以查看所有的配置信息": "To view all configuration information", "用于初始化包的属性和导入模块": "For initializing package properties and importing modules", "to_markdown_tabs 文件list 转换为 md tab": "to_markdown_tabs Convert file list to MD tab", "更换模型": "Replace Model", "从以下文本中提取摘要": "Extract Summary from the Following Text", "表示捕获任意长度的文本": "Indicates Capturing Text of Arbitrary Length", "可能是一个模块的初始化文件": "May Be an Initialization File for a Module", "处理提问与输出": "Handle Questions and Outputs", "需要的再做些简单调整即可": "Some Simple Adjustments Needed", "所以这个没有用": "So This Is Not Useful", "请配置 DASHSCOPE_API_KEY": "Please Configure DASHSCOPE_API_KEY", "不是预定义按钮": "Not a Predefined Button", "让读者能够感受到你的故事世界": "Let Readers Feel Your Story World", "开始整理headers与message": "Start Organizing Headers and Messages", "兼容最新的智谱Ai": "Compatible with the Latest ZhiPu AI", "对于某些PDF会有第一个段落就以小写字母开头": "For Some PDFs, the First Paragraph May Start with a Lowercase Letter", "问题是": "The Issue Is", "也就是说它会匹配尽可能少的字符": "That Is, It Will Match the Least Amount of Characters Possible", "未能成功加载": "Failed to Load Successfully", "接入通义千问在线大模型 https": "Access TongYi QianWen Online Large Model HTTPS", "用不太优雅的方式处理一个core_functional.py中出现的mermaid渲染特例": "Handle a Mermaid Rendering Special Case in core_functional.py in an Ugly Way", "您也可以选择给出其他故事走向": "You Can Also Choose to Provide Alternative Storylines", "改善非markdown输入的显示效果": "Improve Display Effects for Non-Markdown Input", "在二十二世纪编年史中": "In the Chronicle of the 22nd Century", "docs 为Document列表": "docs Are a List of Documents", "互动写故事": "Interactive Story Writing", "4 饼图": "Pie Chart", "正在生成插图中": "Generating Illustration", "路径不存在": "Path Does Not Exist", "PDF翻译中文": "PDF Translation to Chinese", "进行简短的环境描写": "Conduct a Brief Environmental Description", "学术英中互译": "Academic English-Chinese Translation", "且少于2个段落": "And less than 2 paragraphs", "html_view_blank 超链接": "HTML View Blank Hyperlink", "处理 history": "Handle History", "非Cohere官方接口返回了错误": "Non-Cohere Official Interface Returned an Error", "缺失 MATHPIX_APPID 和 MATHPIX_APPKEY": "Missing MATHPIX_APPID and MATHPIX_APPKEY", "搜索知识库内容条数": "Search Knowledge Base Content Count", "返回数据": "Return Data", "没有相关文件": "No Relevant Files", "知识库路径": "Knowledge Base Path", "质量与风格默认值": "Quality and Style Defaults", "包含了用于文本切分的函数": "Contains Functions for Text Segmentation", "请你给出围绕“{subject}”的逻辑关系图": "Please Provide a Logic Diagram Surrounding '{subject}'", "官方Pro服务器🧪": "Official Pro Server", "不支持同时处理多个pdf文件": "Does Not Support Processing Multiple PDF Files Simultaneously", "查询5天历史事件": "Query 5-Day Historical Events", "你是经验丰富的翻译": "You Are an Experienced Translator", "html输入": "HTML Input", "输入文件不存在": "Input File Does Not Exist", "很多人生来就会莫名其妙地迷上一样东西": "Many People Are Born with an Unexplained Attraction to Something", "默认值为 0.7": "Default Value is 0.7", "值越大": "The Larger the Value", "以下文件未能成功加载": "The Following Files Failed to Load", "在线模型": "Online Model", "切割输入": "Cut Input", "修改docker-compose.yml等价于修改容器内部的环境变量": "Modifying docker-compose.yml is Equivalent to Modifying the Internal Environment Variables of the Container", "以换行符分割": "Split by Line Break", "修复中文乱码的问题": "Fix Chinese Character Encoding Issues", "zhipuai 是glm-4的别名": "<PERSON><PERSON><PERSON><PERSON> is an alias for glm-4", "保证其在允许范围内": "Ensure it is within the permissible range", "段尾如果有多余的\\n就去掉它": "Remove any extra \\n at the end of the paragraph", "是否流式输出": "Whether to stream output", "1-流程图": "1-Flowchart", "学术语料润色": "Academic text polishing", "已经超过了模型的最大上下文或是模型格式错误": "Has exceeded the model's maximum context or there is a model format error", "英文省略号": "English ellipsis", "登录成功": "Login successful", "随便切一下吧": "Just cut it randomly", "PDF转换为tex项目失败": "PDF conversion to TeX project failed", "的 max_token 配置不是整数": "The max_token configuration is not an integer", "根据当前聊天历史或指定的路径文件": "According to the current chat history or specified path file", "你必须利用以下文档中包含的信息回答这个问题": "You must use the information contained in the following document to answer this question", "对话、日志记录": "Dialogue, logging", "内容至知识库": "Content to knowledge base", "在银河系的中心": "At the center of the Milky Way", "检查PDF是否被重复上传": "Check if the PDF has been uploaded multiple times", "取最后 max_prompt_tokens 个 token 输入模型": "Take the last max_prompt_tokens tokens as input to the model", "请输入图类型对应的数字": "Please enter the corresponding number for the graph type", "插件主程序3  =-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=": "Plugin main program 3 -=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=", "正在tex项目将翻译为中文": "The TeX project is being translated into Chinese", "适配润色区域": "Adapter polishing area", "首先你从历史记录中提取摘要": "First, you extract an abstract from the history", "讯飞星火认知大模型 -=-=-=-=-=-=-": "iFLYTEK Spark Cognitive Model -=-=-=-=-=-=-=-=-=-", "包含了用于构建和管理向量数据库的函数和类包含了用于构建和管理向量数据库的函数和类包含了用于构建和管理向量数据库的函数和类": "Contains functions and classes for building and managing vector databases", "另外": "Additionally", "内部调优参数": "Internal tuning parameters", "输出格式例如": "Example of Output Format", "当回复图像时": "When Responding with an Image", "越权访问!": "Unauthorized Access!", "如果给出的 prompt 的 token 长度超过此限制": "If the Given Prompt's Token Length Exceeds This Limit", "因此你每次写的故事段落应少于300字": "Therefore, Each Story Paragraph You Write Should Be Less Than 300 Words", "尽量短": "As Concise as Possible", "中文提示词就不显示了": "Chinese Keywords Will Not Be Displayed", "请在前文的基础上": "Please Based on the Previous Text", "20张": "20 Sheets", "文件内容优先": "File Content Takes Priority", "状态图": "State Diagram", "开始查找合适切分点的偏移": "Start Looking for the Offset of an Appropriate Split Point", "已知信息": "Known Information", "文心一言大模型": "<PERSON><PERSON> Large Model", "传递进来一些奇怪的东西": "Passing in Some Weird Things", "很多规则中会考虑分号": "Many Rules Consider the Semicolon", "请配置YUNQUE_SECRET_KEY": "Please Configure YUNQUE_SECRET_KEY", "6-状态图": "6-State Diagram", "输出文本的最小tokens限制": "Minimum Tokens Limit for Output Text", "服务节点": "Service Node", "云雀大模型": "Lark Large Model", "请配置 GEMINI_API_KEY": "Please Configure GEMINI_API_KEY", "可以让软件运行在 http": "Can Run the Software Over HTTP", "基于当前对话或文件GenerateMultipleMermaidCharts": "Generate Multiple Mermaid Charts Based on the Current Conversation or File", "剧情收尾": "Plot Conclusion", "请开始提问": "Please Begin Your Question", "第一页内容/摘要": "First Page Content/Summary", "无法判断则返回image/jpeg": "Return image/jpeg If Unable to Determine", "仅需要输出单个不带任何标点符号的数字": "Single digit without any punctuation", "以下是每类图表的PROMPT": "Here are the PROMPTS for each type of chart", "状态码": "Status code", "TopP值越大输出的tokens类型越丰富": "The larger the TopP value, the richer the types of output tokens", "files_filter_handler 根据type过滤文件": "files_filter_handler filters files by type", "比较每一页的内容是否相同": "Compare whether each page's content is the same", "前往": "Go to", "请输入剧情走向": "Please enter the plot direction", "故事收尾": "Story ending", "必须说明正在回复哪张图像": "Must specify which image is being replied to", "历史文件继续上传": "Continue uploading historical files", "因此禁用": "Therefore disabled", "使用lru缓存": "Use LRU caching", "该装饰器是大多数功能调用的入口": "This decorator is the entry point for most function calls", "如果需要开启": "If needed to enable", "使用 json 解析库进行处理": "Process using JSON parsing library", "将PDF转换为Latex项目": "Convert PDF to LaTeX project", "7-实体关系图": "7-Entity relationship diagram", "根据用户的提示": "According to the user's prompt", "当前用户的请求信息": "Current user's request information", "配置关联关系说明": "Configuration relationship description", "这段代码是使用Python编程语言中的re模块": "This code uses the re module in the Python programming language", "link_mtime_to_md 文件增加本地时间参数": "link_mtime_to_md adds local time parameter to the file", "从当前对话或路径": "From the current conversation or path", "一起写故事": "Write a story together", "前端开发": "Front-end development", "开区间": "Open interval", "如插件参数不正确则使用对话模型判断": "If the plugin parameters are incorrect, use the dialogue model for judgment", "对字符串进行处理": "Process the string", "简洁和专业的来回答用户的问题": "Answer user questions concisely and professionally", "如输入区不是文件则将输入区内容加入历史记录": "If the input area is not a file, add the content of the input area to the history", "编写一个小说的第一幕": "Write the first act of a novel", "更具创造性；": "More creative;", "用于解析和翻译PDF文件的功能和相关辅助函数用于解析和翻译PDF文件的功能和相关辅助函数用于解析和翻译PDF文件的功能和相关辅助函数": "Functions and related auxiliary functions for parsing and translating PDF files", "月之暗面 -=-=-=-=-=-=-": "The Dark Side of the Moon -=-=-=-=-=-=-", "2. 后续剧情发展2": "2. Subsequent plot development 2", "请先提供文本的更正版本": "Please provide the corrected version of the text first", "修改环境变量": "Modify environment variables", "读取之前的自定义按钮": "Read previous custom buttons", "如果为0": "If it is 0", "函数用于去除多行字符串的缩进": "Function to remove indentation from multiline strings", "请绘制有关“": "Please draw something about \"", "给出4种不同的后续剧情发展方向": "Provide 4 different directions for subsequent plot development", "新调优版本GPT-4🔥": "Newly tuned version GPT-4🔥", "已弃用": "Deprecated", "参考 https": "Refer to https", "发现重复上传": "Duplicate upload detected", "本项目的所有配置都集中在config.py中": "All configurations for this project are centralized in config.py", "默认值为 0.95": "Default value is 0.95", "请查阅": "Please refer to", "此选项已废弃": "This option is deprecated", "找到了.doc文件": ".doc file found", "他们的目的地是南极": "Their destination is Antarctica", "lang_reference这段文字是": "The lang_reference text is", "正在尝试生成对比PDF": "Attempting to generate a comparative PDF", "input_encode_handler 提取input中的文件": "input_encode_handler Extracts files from input", "使用中文": "Use Chinese", "一些垃圾第三方接口会出现这样的错误": "Some crappy third-party interfaces may produce such errors", "例如将空格转换为&nbsp": "For example, converting spaces to &nbsp", "请你给出围绕“{subject}”的类图": "Please provide a class diagram around '{subject}'", "是插件的内部参数": "Is an internal parameter of the plugin", "网络波动时可选其他": "Alternative options when network fluctuates", "非Cohere官方接口的出现这样的报错": "Such errors occur in non-Cohere official interfaces", "是前缀": "Is a prefix", "默认 None": "Default None", "如果几天后能顺利到达那里": "If we can smoothly arrive there in a few days", "输出1": "Output 1", "3-类图": "3-Class Diagram", "如需绘制思维导图请使用参数调用": "Please use parameters to call if you need to draw a mind map", "正在将PDF转换为tex项目": "Converting PDF to TeX project", "列出10个经典名著": "List 10 classic masterpieces", "? 在这里用作非贪婪匹配": "? Used here as a non-greedy match", "左上角更换模型菜单中可切换openai": "Switch to OpenAI in the model change menu in the top left corner", "原样返回": "Return as is", "请配置 MATHPIX_APPID 和 MATHPIX_APPKEY": "Please configure MATHPIX_APPID and MATHPIX_APPKEY", "概括上述段落的内容以及内在逻辑关系": "Summarize the content of the above paragraph and its inherent logical relationship", "cookie相关工具函数": "Cookie-related utility functions", "请你给出围绕“{subject}”的饼图": "Please provide a pie chart around '{subject}'", "原型设计": "Prototype design", "必须为正数": "Must be a positive number", "又一阵剧痛从肝部袭来": "Another wave of severe pain strikes from the liver", "智谱AI": "Zhipu AI", "基础功能区按钮的附加功能": "Additional functions of the basic functional area buttons", "one-api 对齐支持 -=-=-=-=-=-=-": "one-api alignment support -=-=-=-=-=-=-", "5 甘特图": "5 Gantt chart", "用于初始化包的属性和导入模块是一个包的初始化文件": "The file used for initializing package properties and importing modules is an initialization file for the package", "创建并修改config_private.py": "Create and modify config_private.py", "会使输出更随机": "Would make the output more random", "已添加": "Added", "估计一个切分点": "Estimate a split point", "\\n\\n1. 临时解决方案": "\\n\\n1. Temporary solution", "没有回答": "No answer", "尝试重新翻译PDF": "Try to retranslate the PDF", "被这个解码给耍了": "Fooled by this decoding", "再在后端清除history": "Clear history on the backend again", "根据情况选择flowchart LR": "Choose flowchart LR based on the situation", "幻方-深度求索大模型 -=-=-=-=-=-=-": "Deep Seek Large Model -=-=-=-=-=-=-", "即使它们在历史记录中被提及": "Even if they are mentioned in the history", "此处需要进一步优化逻辑": "Further logic optimization is needed here", "借鉴自同目录下的bridge_ChatGPT.py": "Derived from the bridge_ChatGPT.py in the same directory", "正是这样": "That's exactly right", "您也可以给出您心中的其他故事走向": "You can also provide other story directions in your mind", "文本预处理": "Text preprocessing", "请登录": "Please log in", "请修改docker-compose": "Please modify docker-compose", "运行一些异步任务": "Run some asynchronous tasks", "5-甘特图": "5-Gantt chart", "3 类图": "3-Class diagram", "因为你接下来将会与用户互动续写下面的情节": "Because you will interact with the user to continue writing the plot below", "避免把同一个文件添加多次": "Avoid adding the same file multiple times", "可挑选精度": "Selectable precision", "调皮一下": "Play a joke", "并解析": "And parse", "您可以在输入框中输入一些关键词": "You can enter some keywords in the input box", "文件加载失败": "File loading failed", "请你给出围绕“{subject}”的甘特图": "Please provide a Gantt chart around \"{subject}\"", "上传PDF": "Upload PDF", "请判断适合使用的流程图类型": "Please determine the suitable flowchart type", "错误码": "Error code", "非markdown输入": "Non-markdown input", "所以只能通过提示词对第几张图片进行定位": "So can only locate the image by the prompt", "避免下载到缓存文件": "Avoid downloading cached files", "没有思维导图!!!测试发现模型始终会优先选择思维导图": "No mind map!!! Testing found that the model always prioritizes mind maps", "请登录Cohere查看详情 https": "Please log in to Cohere for details https", "检查历史上传的文件是否与新上传的文件相同": "Check if the previously uploaded file is the same as the newly uploaded file", "加载主题相关的工具函数": "Load theme-related utility functions", "图表类型由模型判断": "Chart type is determined by the model", "⭐ 多线程方法": "Multi-threading method", "获取 max_token 的值": "Get the value of max_token", "空白的输入栏": "Blank input field", "根据整理的摘要选择图表类型": "Select chart type based on the organized summary", "返回 True": "Return True", "这里为了区分中英文情景搞复杂了一点": "Here it's a bit complicated to distinguish between Chinese and English contexts", "ZHIPUAI_MODEL 配置项选项已经弃用": "ZHIPUAI_MODEL configuration option is deprecated", "但是这里我把它忽略不计": "But here I ignore it", "非必要": "Not necessary", "思维导图": "Mind map", "插件」": "Plugin", "重复文件路径": "Duplicate file path", "之间不要存在空格": "No spaces between fields", "破折号、英文双引号等同样忽略": "Ignore dashes, English quotes, etc.", "填写 VOLC_ACCESSKEY": "Enter VOLC_ACCESSKEY", "称为核取样": "Called nuclear sampling", "Incorrect API key. 请确保API key有效": "Incorrect API key. Please ensure the API key is valid", "如输入区内容为文件则清空历史记录": "If the input area content is a file, clear the history", "并处理精度问题": "And handle precision issues", "并给出修改的理由": "And provide reasons for the changes", "至此已经超出了正常接口应该进入的范围": "This has exceeded the scope that a normal interface should enter", "并已加载知识库": "And the knowledge base has been loaded", "file_manifest_filter_html 根据type过滤文件": "file_manifest_filter_html filters files by type", "participant B as 系统": "participant B as System", "要留出足够的互动空间": "Leave enough interaction space", "请你给出围绕“{subject}”的实体关系图": "Please provide an entity relationship diagram around '{subject}'", "答案请使用中文": "Please answer in Chinese", "输出会更加稳定或确定": "The output will be more stable or certain", "是一个包的初始化文件": "Is an initialization file for a package", "用于加载和分割文件中的文本的通用文件加载器用于加载和分割文件中的文本的通用文件加载器用于加载和分割文件中的文本的通用文件加载器": "A universal file loader for loading and splitting text in files", "围绕我选定的剧情情节": "Around the plot I have chosen", "Mathpix 拥有执行PDF的OCR功能": "Mathpix has OCR functionality for PDFs", "是否允许暴力切分": "Whether to allow violent segmentation", "清空 txt_tmp 对应的位置方便下次搜索": "Clear the location corresponding to txt_tmp for easier next search", "编写小说的最后一幕": "Write the last scene of the novel", "可能是一个模块的初始化文件根据位置和名称": "May be an initialization file for a module based on position and name", "更新新的自定义按钮": "Update new custom button", "把分句符\\n放到双引号后": "Put the sentence separator \\n after the double quotes", "序列图": "Sequence diagram", "兼容非markdown输入": "Compatible with non-markdown input", "那么就切": "Then cut", "4-饼图": "4-Pie chart", "结束剧情": "End of the plot", "字数要求": "Word count requirement", "以下是对以上文本的总结": "Below is a summary of the above text", "但不要同时调整两个参数": "But do not adjust two parameters at the same time", "📌省略": "Omit", "请查看message": "Please check the message", "如果所有页的内容都相同": "If all pages have the same content", "我将在这4个选择中": "I will choose from these 4 options", "请设置为True": "Please set to True", "当 remain_txt_to_cut": "When remain_txt_to_cut", "后续输出被截断": "Subsequent output is truncated", "检查API_KEY": "Check API_KEY", "阿里云实时语音识别 配置难度较高": "Alibaba Cloud real-time speech recognition has a higher configuration difficulty", "图像生成提示为空白": "Image generation prompt is blank", "由于实体关系图用到了{}符号": "Because the entity relationship diagram uses the {} symbol", "系统繁忙": "System busy", "月之暗面 API KEY": "Dark side of the moon API KEY", "编写小说的下一幕": "Write the next scene of the novel", "选择一种": "Choose one", "或者flowchart TD": "Or flowchart TD", "请把以下学术文章段落翻译成中文": "Please translate the following academic article paragraph into Chinese", "7 实体关系图": "7 Entity relationship diagram", "处理游戏的主体逻辑": "Handle the main logic of the game", "请以“{headstart}”为开头": "Please start with \"{headstart}\"", "匹配后单段上下文长度": "Length of single segment context after matching", "先行者知道": "The pioneer knows", "以及处理PDF文件的示例代码包含了用于文本切分的函数": "Example code for processing PDF files includes functions for text segmentation", "未发现重复上传": "No duplicate uploads found", "那么就不用切了": "Then there's no need to split", "目前来说": "Currently", "请在LLM_MODEL中配置": "Please configure in LLM_MODEL", "是否启用上下文关联": "Whether to enable context association", "为了加速计算": "To speed up calculations", "登录请求": "Login request", "这里解释一下正则表达式中的几个特殊字符": "Explanation of some special characters in regular expressions", "其中数字对应关系为": "The corresponding relationship of the numbers is", "修改配置有三种方法": "There are three ways to modify the configuration", "请前往arxiv打开此论文下载页面": "Please go to arXiv and open the paper download page", "然后download source手动下载latex源码包": "Then manually download the LaTeX source package by downloading the source", "功能单元": "Functional unit", "你需要翻译的文本如下": "The text you need to translate is as follows", "以便于后续快速的匹配和查找操作": "To facilitate rapid matching and search operations later", "文本内容": "Text content", "自动更新、打开浏览器页面、预热tiktoken模块": "Auto-update, open browser page, warm up tiktoken module", "原样传递": "Pass through as is", "但是该文件格式不被支持": "But the file format is not supported", "他现在是全宇宙中唯一的一个人了": "He is now the only person in the entire universe", "取值范围0~1": "Value range 0~1", "搜索匹配score阈值": "Search match score threshold", "当字符串中有掩码tag时": "When there is a mask tag in the string", "错误的不纳入对话": "Errors are not included in the conversation", "英语": "English", "象限提示图": "Quadrant prompt diagram", "由于不管提供文本是什么": "Because regardless of what the provided text is", "确定后续剧情的发展": "Determine the development of the subsequent plot", "处理空输入导致报错的问题 https": "Handle the error caused by empty input", "第 3 部分": "Part 3", "不能等于 0 或 1": "Cannot be equal to 0 or 1", "同时过大的图表可能需要复制到在线编辑器中进行渲染": "Large charts may need to be copied to an online editor for rendering", "装饰器函数ArgsGeneralWrapper": "Decorator function ArgsGeneralWrapper", "写个函数移除所有的换行符": "Write a function to remove all line breaks", "默认为False": "Default is False", "实例化BaiduSpider": "Instantiate <PERSON>", "9-思维导图": "Mind Map 9", "是否开启跨域": "Whether to enable cross-domain", "随机InteractiveMiniGame": "Random InteractiveMiniGame", "用于构建HTML报告的类和方法用于构建HTML报告的类和方法用于构建HTML报告的类和方法": "Classes and methods for building HTML reports", "这里填一个提示词字符串就行了": "Just fill in a prompt string here", "文本切分": "Text segmentation", "用于在生成mermaid图表时隐藏代码块": "Used to hide code blocks when generating mermaid charts", "如果剩余文本的token数小于限制": "If the number of tokens in the remaining text is less than the limit", "未能在规定时间内完成任务": "Failed to complete the task within the specified time", "API key has been deactivated. Cohere以账户失效为由": "API key has been deactivated. Cohere cited account expiration as the reason", "正在使用讯飞图片理解API": "Using the Xunfei Image Understanding API", "如果您使用docker-compose部署": "If you deploy using docker-compose", "最大输入 token 数": "Maximum input token count", "遇到了控制请求速率限制": "Encountered control request rate limit", "数值范围约为0-1100": "The numerical range is approximately 0-1100", "几乎使他晕厥过去": "Almost made him faint", "识图模型GPT-4V": "Image recognition model GPT-4V", "零一万物模型 -=-=-=-=-=-=-": "Zero-One Universe Model", "所有对话记录将自动保存在本地目录": "All conversation records will be saved automatically in the local directory", "饼图": "Pie Chart", "添加Live2D": "Add Live2D", "⭐ 单线程方法": "Single-threaded Method", "配图": "Illustration", "根据上述已知信息": "Based on the Above Known Information", "1. 后续剧情发展1": "1. Subsequent Plot Development 1", "2-序列图": "Sequence Diagram", "流程图": "Flowchart", "需求分析": "Requirement Analysis", "我认为更合理的是": "I Think a More Reasonable Approach Is", "claude家族": "<PERSON>", "”的逻辑关系图": "Logic Relationship Diagram", "给出人物的名字": "Provide the Names of Characters", "无法自动下载该论文的Latex源码": "Unable to Automatically Download the LaTeX Source Code of the Paper", "需要用户手动处理的信息": "Information That Requires Manual Processing by Users", "点击展开“文件下载区”": "Click to Expand 'File Download Area'", "生成长度过长": "Excessive Length Generated", "\\n\\n2. 长效解决方案": "2. Long-term Solution", "=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-= 插件主程序2 =-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=": "=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-= Plugin Main Program 2 =-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=", "title 项目开发流程": "Title Project Development Process", "如果您希望剧情立即收尾": "If You Want the Plot to End Immediately", "空格转换为&nbsp": "Space Converted to &nbsp;", "图片数量超过api上限": "Number of Images Exceeds API Limit", "他知道": "He Knows", "在这里输入自定义参数「分辨率-质量": "Enter Custom Parameters Here 'Resolution-Quality", "例如ChatGLM&gpt-3.5-turbo&gpt-4": "For example ChatGLM, gpt-3.5-turbo, and gpt-4", "账户管理": "Account Management", "正在将翻译好的项目tex项目编译为PDF": "Compiling the Translated Project .tex Project into PDF", "我们把 _max 后的文字转存至 remain_txt_to_cut_storage": "We save the text after _max to the remain_txt_to_cut_storage", "标签之前停止匹配": "Stop matching before the label", "例子": "Example", "遍历检查是否有额外参数": "Iterate to check for extra parameters", "文本分句长度": "Length of text segmentation", "请你给出围绕“{subject}”的状态图": "Please provide a state diagram surrounding \"{subject}\"", "用stream的方法避免中途网线被掐": "Use the stream method to avoid the cable being disconnected midway", "然后在markdown表格中列出修改的内容": "Then list the changes in a Markdown table", "以上是从文章中提取的摘要": "The above is an abstract extracted from the article", "但是无法找到相关文件": "But unable to find the relevant file", "上海AI-LAB书生大模型 -=-=-=-=-=-=-": "Shanghai AI-LAB <PERSON> Model -=-=-=-=-=-=-", "遇到第一个": "Meet the first", "存储在名为const_extract_exp的变量中": "Stored in a variable named const_extract_exp", "括号在正则表达式中表示捕获组": "Parentheses represent capture groups in regular expressions", "那里的太空中渐渐隐现出一个方形区域": "A square area gradually appears in the space there", "智谱GLM4超级模型🔥": "Zhipu GLM4 Super Model🔥", "故事开头": "Beginning of the story", "请检查文件格式是否正确": "Please check if the file format is correct", "这个模式被编译成一个正则表达式对象": "This pattern is compiled into a regular expression object", "单字符断句符": "Single character sentence break", "看后续支持吧": "Let's see the follow-up support", "markdown输入": "Markdown input", "系统": "System", "80字以内": "Within 80 characters", "一个测试mermaid绘制图表的功能": "A function to test the Mermaid chart drawing", "输入部分": "Input section", "移除右侧逗号": "Remove the comma on the right", "因此思维导图仅能通过参数调用": "Therefore, the mind map can only be invoked through parameters", "6 状态图": "State Diagram", "类图": "Class Diagram", "不要重复前文": "Do not repeat the previous text", "但内部": "But internally", "小说的下一幕字数少于300字": "The next scene of the novel has fewer than 300 words", "每个发展方向都精明扼要地用一句话说明": "Each development direction is concisely described in one sentence", "充分考虑其之间的逻辑": "Fully consider the logic between them", "兼顾前端状态的功能": "Take into account the functionality of the frontend state", "1 流程图": "Flowchart", "用户QQ群*********": "User QQ Group *********", "通义-本地模型 -=-=-=-=-=-=-": "Tongyi - Local Model", "取值范围0-1000": "Value range 0-1000", "但不是^*.开始": "But not ^*. Start", "他们将钻出地壳去看诗云": "They will emerge from the crust to see the poetry cloud", "我们正在互相讨论": "We are discussing with each other", "值越小": "The smaller the value", "请在以下几种故事走向中": "Please choose from the following story directions", "请先把模型切换至gpt-*": "Please switch the model to gpt-* first", "不再需要填写": "No longer needs to be filled out", "深夜": "Late at night", "小说的前文回顾": "Review of the previous text of the novel", "项目文件树": "Project file tree", "如果双引号前有终止符": "If there is a terminator before the double quotes", "participant A as 用户": "Participant A as User", "处理游戏初始化等特殊情况": "Handle special cases like game initialization", "然后使用mermaid+llm绘制图表": "Then use mermaid+llm to draw charts", "0表示不生效": "0 means not effective", "在以下的剧情发展中": "In the following plot development", "模型考虑具有 top_p 概率质量 tokens 的结果": "Model considering results with top_p probability quality tokens", "根据字符串要给谁看": "Depending on who is intended to view the string", "没有设置YIMODEL_API_KEY选项": "YIMODEL_API_KEY option is not set", "换行符转换为": "Convert line breaks to", "-风格": "-style", "默认情况下并发量极低": "Default to a very low level of concurrency", "为字符串加上上面定义的前缀和后缀": "Add the defined prefix and suffix to the string", "先切换模型到gpt-*": "Switch the model to gpt-* first", "它确保我们匹配的任意文本是尽可能短的": "It ensures that any text we match is as short as possible", "积极地运用环境描写、人物描写等手法": "Actively use techniques such as environmental and character descriptions", "零一万物": "Zero One Universe", "html_local_file 本地文件取相对路径": "html_local_file takes the relative path of the local file", "伊依一行三人乘坐一艘游艇在南太平洋上做吟诗航行": "<PERSON> and three others set sail on a yacht to recite poetry in the South Pacific", "移除左边通配符": "<PERSON><PERSON><PERSON> left wildcard characters", "随后绘制图表": "Draw a chart subsequently", "输入2": "Input 2", "所以用最没有意义的一个点代替": "Therefore, replace it with the most meaningless point", "等": "etc.", "是本地文件": "Is a local file", "正在文本切分": "Text segmentation in progress", "等价于修改容器内部的环境变量": "Equivalent to modifying the environment variables inside the container", "cohere等请求源": "Cohere and other request sources", "我们再把 remain_txt_to_cut_storage 中的部分文字取出": "Then we extract part of the text from remain_txt_to_cut_storage", "生成带掩码tag的字符串": "Generate a string with masked tags", "智谱 -=-=-=-=-=-=-": "ZhiPu -=-=-=-=-=-=-", "前缀字符串": "Prefix string", "Temperature值越大随机性越大": "The larger the Temperature value, the greater the randomness", "借用PDF切割中的函数对文本进行切割": "Use functions from PDF cutting to segment the text", "挑选一种剧情发展": "Choose a plot development", "将换行符转换为": "Convert line breaks to", "0.1 意味着模型解码器只考虑从前 10% 的概率的候选集中取 tokens": "0.1 means the model decoder only considers taking tokens from the top 10% probability candidates", "确定故事的下一步": "Determine the next step of the story", "个文件的显示": "Display of a file", "用于控制输出tokens的多样性": "Used to control the diversity of output tokens", "导入BaiduSpider": "I<PERSON><PERSON>", "不输入则为模型自行判断": "If not entered, the model will judge on its own", "准备下一次迭代": "Prepare for the next iteration", "包含一些用于文本处理和模型微调的函数和装饰器包含一些用于文本处理和模型微调的函数和装饰器包含一些用于文本处理和模型微调的函数和装饰器": "Contains functions and decorators for text processing and model fine-tuning", "由于没有单独的参数保存包含图片的历史": "Since there is no separate parameter to save the history with images", "section 开发": "section development", "注意这里没有掩码tag": "Note that there is no mask tag here", "section 设计": "section design", "对话|编程|学术|智能体": "Dialogue | Programming | Academic | Intelligent Agent", "您只需要选择其中一种即可": "You only need to choose one of them", "添加Live2D形象": "Add Live2D image", "请用以下命令安装": "Please install with the following command", "触发了Google的安全访问策略": "Triggered Google's safe access policy", "参数示例「1024x1024-hd-vivid」 || 分辨率支持 「1024x1024」": "Parameter example '1024x1024-hd-vivid' || Resolution support '1024x1024'", "结局除外": "Excluding the ending", "subgraph 函数调用": "subgraph function call", "项目示意图": "Project diagram", "实体关系图": "Entity relationship diagram", "计算机把他的代号定为M102": "The computer named his code M102", "首先尝试用双空行": "Try using double empty lines first", "接下来将判断适合的图表类型": "Next, determine the appropriate chart type", "注意前面的几句都小心保留了双引号": "Note that the previous sentences have carefully preserved double quotes", "您正在调用插件": "You are calling a plugin", "从上到下": "From top to bottom", "请配置HUOSHAN_API_KEY": "Please configure HUOSHAN_API_KEY", "知识检索内容相关度 Score": "Knowledge retrieval content relevance score", "所以不会被处理": "So it will not be processed", "设置10秒即可": "Set to 10 seconds", "以空格分割": "Separated by space", "根据位置和名称": "According to position and name", "一些垃圾第三方接口出现这样的错误": "Some crappy third-party interfaces have this error", "////////////////////// 输入清除键 ///////////////////////////": "////////////////////// Input Clear Key ///////////////////////////", "并解析为html or md 文本": "And parse as HTML or MD text", "匹配单段内容的连接上下文长度": "Matching single section content connection context length", "控制输出的随机性": "Control the randomness of output", "是模型名": "Is model name", "请检查配置文件": "Please check the configuration file", "如何使用one-api快速接入": "How to quickly access using one-api", "请求失败": "Request failed", "追加列表": "Append list", "////////////////////// 函数插件区 ///////////////////////////": "////////////////////// Function Plugin Area ///////////////////////////", "你是WPSAi": "You are WPSAi", "第五部分 一些文件处理方法": "Part Five Some file processing methods", "圆圆迷上了肥皂泡": "<PERSON> is fascinated by soap bubbles", "可选参数": "Optional parameters", "one-api模型": "one-api model", "port/gpt_academic/ 下": "Under port/gpt_academic/", "下一段故事": "Next part of the story", "* 表示前一个字符可以出现0次或多次": "* means the previous character can appear 0 or more times", "向后兼容配置": "Backward compatible configuration", "输出部分": "Output section", "稍后": "Later", "比如比喻、拟人、排比、对偶、夸张等等": "For example, similes, personification, parallelism, antithesis, hyperbole, etc.", "是自定义按钮": "Is a custom button", "你需要根据用户给出的小说段落": "You need to based on the novel paragraph given by the user", "以mermaid flowchart的形式展示": "Display in the form of a mermaid flowchart", "最后一幕的字数少于1000字": "The last scene has fewer than 1000 words", "如没出错则保持为空": "Keep it empty if there are no errors", "建议您根据应用场景调整 top_p 或 temperature 参数": "It is recommended to adjust the top_p or temperature parameters according to the application scenario", "仿佛他的出生就是要和这东西约会似的": "As if his birth was meant to date this thing", "处理特殊的渲染问题": "Handle special rendering issues", "我认为最合理的故事结局是": "I think the most reasonable ending for the story is", "请给出上方内容的思维导图": "Please provide a mind map of the content above", "点other Formats": "Click on other Formats", "文件加载完毕": "File loaded", "Your account is not active. Cohere以账户失效为由": "Your account is not active. <PERSON><PERSON> cites the account's inactivation as the reason", "找不到任何.pdf文件": "Cannot find any .pdf files", "请根据判断结果绘制相应的图表": "Please draw the corresponding chart based on the judgment result", "积极地运用修辞手法": "Actively use rhetorical devices", "工具函数 =-=-=-=-=-=-=-=-=-=-=-=-=-=-=-": "Utility function -=-=-=-=-=-=-=-=-=-=-=-=-=-=-=", "=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-= 插件主程序1 =-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=": "=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-= Plugin Main Program 1 =-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=", "在": "In", "即正则表达式库": "That is, the regular expression library", "////////////////////// 基础功能区 ///////////////////////////": "////////////////////// Basic Function Area ///////////////////////////", "并重新编译PDF | 输入参数为路径": "And recompile PDF | Input parameter is the path", "甘特图": "Gantt Chart", "但是需要注册账号": "But registration is required", "获取完整的从Cohere返回的报错": "Get the complete error message returned from Cohere", "合并摘要": "<PERSON><PERSON>", "这最后一课要提前讲了": "The last lesson will be taught ahead of schedule", "大模型": "Large Model", "查找输入区内容中的文件": "Find files in the input area content", "预处理参数": "Preprocessing Parameters", "这段代码定义了一个名为ProxyNetworkActivate的空上下文管理器": "This code defines an empty context manager named ProxyNetworkActivate", "对话错误": "Dialogue Error", "确定故事的结局": "Determine the ending of the story", "第 1 部分": "Part 1", "直到遇到括号外部最近的限定符": "Until the nearest qualifier outside the parentheses is encountered", "负责向用户前端展示对话": "Responsible for displaying dialogue to the user frontend", "查询内容": "Query Content", "匹配结果更精准": "More accurate matching results", "根据选择的图表类型绘制图表": "Draw a chart based on the selected chart type", "空格、换行、空字符串都会报错": "Spaces, line breaks, and empty strings will all result in errors", "请尝试削减单次输入的文本量": "Please try to reduce the amount of text in a single input", "上传到路径": "Upload to path", "中": "In", "后缀字符串": "Suffix string", "您还可以在接入one-api时": "You can also when accessing one-api", "请说 “根据已知信息无法回答该问题” 或 “没有提供足够的相关信息”": "Please say 'Cannot answer the question based on available information' or 'Not enough relevant information is provided'", "Cohere和API2D不会走这里": "Cohere and API2D will not go here", "节点名字使用引号包裹": "Node names should be enclosed in quotes", "这次的故事开头是": "The beginning of this story is", "你是一个想象力丰富的杰出作家": "You are a brilliant writer with a rich imagination", "正在与你的朋友互动": "Interacting with your friends", "/「-hd」 || 风格支持 「-vivid」": "/ '-hd' || Style supports '-vivid'", "如输入区无内容则直接解析历史记录": "If the input area is empty, parse the history directly", "根据以上的情节": "Based on the above plot", "将图表类型参数赋值为插件参数": "Set the chart type parameter to the plugin parameter", "根据图片类型返回image/jpeg": "Return image/jpeg based on image type", "如果lang_reference是英文": "If lang_reference is English", "示意图": "Schematic diagram", "完整参数列表": "Complete parameter list", "仿佛灿烂的群星的背景被剪出一个方口": "As if the brilliant background of stars has been cut out into a square", "如果没有找到合适的切分点": "If no suitable splitting point is found", "获取数据": "Get data", "内嵌的javascript代码": "Embedded JavaScript code", "绘制多种mermaid图表": "Draw various mermaid charts", "无效": "Invalid", "查找pdf/md/word并获取文本内容并返回状态以及文本": "Search for pdf/md/word, retrieve text content, and return status and text", "总结绘制脑图": "Summarize mind mapping", "禁止杜撰不符合我选择的剧情": "Prohibit making up plots that do not match my choice", "正在生成向量库": "Generating vector library", "是LLM的内部调优参数": "Is an internal tuning parameter of LLM", "请你选择一个合适的图表类型": "Please choose an appropriate chart type", "请在“输入区”输入图像生成提示": "Please enter image generation prompts in the 'input area'", "经测试设置为小于500时": "After testing, set it to less than 500", "当然": "Certainly", "必要": "Necessary", "从左到右": "From left to right", "接下来调用本地Latex翻译插件即可": "Next, call the local Latex translation plugin", "如果相同则返回": "If the same, return", "根据语言": "According to the language", "使用mermaid语法": "Use mermaid syntax", "这是游戏的第一步": "This is the first step of the game", "构建后续剧情引导": "Building subsequent plot guidance", "以满足 token 限制": "To meet the token limit", "也就是说": "That is to say", "mermaid语法举例": "Mermaid syntax example", "发送": "Send", "那么就只显示英文提示词": "Then only display English prompts", "正在检查": "Checking", "返回处理后的字符串": "Return the processed string", "2 序列图": "Sequence diagram 2", "yi-34b-chat-0205只有4k上下文": "yi-34b-chat-0205 has only 4k context", "请检查配置": "Please check the configuration", "请你给出围绕“{subject}”的象限图": "Please provide a quadrant diagram around '{subject}'", "故事该结束了": "The story should end", "修复缩进": "Fix indentation", "请描述给出的图片": "Please describe the given image", "启用插件热加载": "Enable plugin hot reload", "通义-在线模型 -=-=-=-=-=-=-": "Tongyi - Online Model", "比较页数是否相同": "Compare if the number of pages is the same", "正式开始服务": "Officially start the service", "使用mermaid flowchart对以上文本进行总结": "Summarize the above text using a mermaid flowchart", "不是vision 才处理history": "Not only vision but also handle history", "来定义了一个正则表达式模式": "Defined a regular expression pattern", "IP地址等": "IP addresses, etc.", "那么双引号才是句子的终点": "Then the double quotes mark the end of the sentence", "输入1": "Input 1", "/「1792x1024」/「1024x1792」 || 质量支持 「-standard」": "/'1792x1024'/ '1024x1792' || Quality support '-standard'", "为了避免索引错误将其更改为大写": "To avoid indexing errors, change it to uppercase", "搜索网页": "Search the web", "用于控制生成文本的随机性和创造性": "Used to control the randomness and creativity of generated text", "不能等于 0": "<PERSON>not equal 0", "在距地球五万光年的远方": "At a distance of fifty thousand light-years from Earth", ". 表示任意单一字符": ". represents any single character", "选择预测值最大的k个token进行采样": "Select the k tokens with the largest predicted values for sampling", "输出2": "Output 2", "函数示意图": "Function Diagram", "You are associated with a deactivated account. Cohere以账户失效为由": "You are associated with a deactivated account. Cohere due to account deactivation", "3. 后续剧情发展3": "3. Subsequent Plot Development", "并以“剧情收尾”四个字提示程序": "And use the four characters 'Plot Conclusion' as a prompt for the program", "中文省略号": "Chinese Ellipsis", "则不生效": "Will not take effect", "目前是两位小数": "Currently is two decimal places", "Incorrect API key. Cohere以提供了不正确的API_KEY为由": "Incorrect API key. Cohere reports an incorrect API_KEY.", "应当慎之又慎！": "Should be extremely cautious!", "、后端setter": "backend setter", "对于 Run1 的数据": "data for Run1", "另一种更简单的setter方法": "another simpler setter method", "完成解析": "complete parsing", "自动同步": "automatic synchronization", "**表8**": "**Table 8**", "安装方法见": "Installation method see", "通过更严格的 PID 选择对π介子和 K 介子进行过滤以减少主要鉴别为 π 介子的 K 介子等峰背景的污染": "Filtering π mesons and K mesons with a stricter PID to reduce contamination of K mesons mainly identified as π mesons", "并且占据高质量边带的候选体会被拒绝": "And candidates occupying high-quality sidebands are rejected", "GPT-SOVITS 文本转语音服务的运行地址": "Operating address of GPT-SOVITS text-to-speech service", "PDF文件路径": "PDF file path", "注意图片大约占用1": "Note that the image takes up about 1", "以便可以研究BDT输入": "So that BDT inputs can be studied", "是否自动打开浏览器页面": "Whether to automatically open the browser page", "中此模型的APIKEY的名字": "The name of the APIKEY for this model", "{0.8} $ 和 $ \\operatorname{ProbNNk}\\left": "{0.8} $ and $ \\operatorname{ProbNNk}\\left", "请检测终端输出": "Please check the terminal output", "注册账号并获取API KEY": "Register an account and get an API KEY", "-=-=-=-=-=-=-= 👇 以下是多模型路由切换函数 -=-=-=-=-=-=-=": "-=-=-=-=-=-=-= 👇 The following is a multi-model route switching function -=-=-=-=-=-=-=", "如不设置": "If not set", "如果只询问“一个”大语言模型": "If only asking about 'one' large language model", "并非为了计算权重而专门施加了附加选择": "Not specifically applying additional selection for weight calculation", "DOC2X的PDF解析服务": "PDF parsing service of DOC2X", "两兄弟": "Two brothers", "相同的切割也用于Run2和Run1数据": "The same segmentation is also used for Run2 and Run1 data", "返回的数据流第一次为空": "The returned data stream is empty for the first time", "对于光子 PID": "For photon PID", "例如chatglm&gpt-3.5-turbo&gpt-4": "For example chatglm&gpt-3.5-turbo&gpt-4", "第二种方法": "The second method", "BDT 模型的系统性误差使用通过拟合通过和未通过所选 BDT 截断值的 $ B $ 候选体质量分布的异构同位旋对称模式进行评估": "The systematic error of the BDT model is evaluated using the heterogeneous isospin symmetry mode of the candidate body mass distribution of $ B $ selected by fitting through and not through the selected BDT truncation value", "通过比较模拟和真实的 $ {B}^{ + } \\rightarrow  J/\\psi {K}^{* + } $ 衰变样本来计算权重": "Calculate weights by comparing simulated and real $ {B}^{ + } \\rightarrow  J/\\psi {K}^{* + } $ decay samples", "上下文长度超过glm-4v上限2000tokens": "The context length exceeds the upper limit of 2000 tokens for glm-4v", "通过为每个模拟信号候选分配权重来校正模拟和碰撞数据之间的一些差异": "Correct some differences between simulated and collision data by assigning weights to each simulated signal candidate", "2016 年上磁场数据集中通过松散选择": "Loose selection in the 2016 upper magnetic field data set", "定义history的一个孪生的前端存储区": "Define a twin front-end storage area for history", "为默认值；": "For the default value;", "一个带二级菜单的插件": "A plugin with a secondary menu", "用于": "Used for", "每次请求的最大token数量": "Maximum token count for each request", "输入Arxiv的ID或者网址": "Enter the Arxiv ID or URL", "采用哪种方法执行转换": "Which method to use for transformation", "定义history_cache-": "Define history_cache-", "再点击该插件": "Click the plugin again", "隐藏": "<PERSON>de", "第三个参数": "The third parameter", "声明这是一个文本框": "Declare this as a text box", "其准则为拒绝已知 $ {B}^{ + } $ 质量内 $  \\pm  {50}\\mathrm{{MeV}}/{c}^{2} $ 范围内的候选体": "Its criterion is to reject candidates within $  \\pm  {50}\\mathrm{{MeV}}/{c}^{2} $ of the known $ {B}^{ + } $ mass", "第一种方法": "The first method", "正在尝试GROBID": "Trying GROBID", "定义新一代插件的高级参数区": "Define the advanced parameter area for the new generation of plugins", "047个tokens": "47 tokens", "PDF解析方法": "PDF parsing method", "缺失 DOC2X_API_KEY": "Missing DOC2X_API_KEY", "第二个参数": "The second parameter", "将只取第一张图片进行处理": "Only the first image will be processed", "请检查配置文件的": "Please check the configuration file", "此函数已经弃用！！新函数位于": "This function has been deprecated!! The new function is located at", "同样地": "Similarly", "的 $ J/\\psi {K}^{ + }{\\pi }^{0} $ 和 $ J/\\psi {K}^{ + } $ 质量的分布": "The distribution of the masses of $ J/\\psi {K}^{ + }{\\pi }^{0} $ and $ J/\\psi {K}^{ + } $", "取消": "Cancel", "3.8 对 BDT 系统误差的严格 PID 选择": "Strict PID selection for BDT system errors at 3.8", "发送至DOC2X解析": "Send to DOC2X for parsing", "在触发这个按钮时": "When triggering this button", "例如对于01万物的yi-34b-chat-200k": "For example, for 010,000 items yi-34b-chat-200k", "继续等待": "Continue waiting", "留空则使用时间作为文件名": "Leave blank to use time as the file name", "获得以下报错信息": "Get the following error message", "ollama模型": "Ollama model", "要求如下": "Requirements are as follows", "不包括思维导图": "Excluding mind maps", "则用指定模型覆盖全局模型": "Then override the global model with the specified model", "DOC2X服务不可用": "DOC2X service is not available", "则抛出异常": "Then throw an exception", "幻方-深度求索大模型在线API -=-=-=-=-=-=-": "Magic Square - Deep Quest Large Model Online API -=-=-=-=-=-=-", "详见 themes/common.js": "See themes/common.js", "如果尝试加载未授权的类": "If trying to load unauthorized class", "因此真实样本包含一定比例的背景": "Therefore, real samples contain a certain proportion of background", "热更新Prompt & ModelOverride": "Hot update Prompt & ModelOverride", "可能的原因是": "Possible reasons are", "因此仅BDT进入相应的选择": "So only BDT enters the corresponding selection", "⚠️请不要与模型的最大token数量相混淆": "⚠️ Do not confuse with the maximum token number of the model", "为openai格式的API生成响应函数": "Generate response function for OpenAI format API", "API异常": "API exception", "调用Markdown插件": "Call Markdown plugin", "报告已经添加到右侧“文件下载区”": "The report has been added to the right 'File Download Area'", "把PDF文件拖入对话": "Drag the PDF file into the dialogue", "根据基础功能区 ModelOverride 参数调整模型类型": "Adjust the model type according to the ModelOverride parameter in the basic function area", "vllm 对齐支持 -=-=-=-=-=-=-": "VLLM alignment support -=-=-=-=-=-=-", "强制点击此基础功能按钮时": "When forcing to click this basic function button", "请上传文件后": "Please upload the file first", "解析错误": "Parsing error", "APIKEY为空": "APIKEY is empty", "效果最好": "Best effect", "未来5天": "Next 5 days", "会先执行js代码更新history_cache": "Will first execute js code to update history_cache", "下拉菜单的选项为": "The options in the dropdown menu are", "额外的翻译提示词": "Additional translation prompts", "这三个切割也用于选择 $ {B}^{ + } \\rightarrow  J/\\psi {K}^{* + } $ 衰变": "These three cuts are also used to select $ {B}^{ + } \\rightarrow  J/\\psi {K}^{* + } $ decay", "借鉴自同目录下的bridge_chatgpt.py": "Inspired by bridge_chatgpt.py in the same directory", "其中质量从 DTF 四维向量重新计算以改善测量的线形": "Recalculate the mass from the DTF four-vector to improve the linearity of the measurement", "移除任何不安全的元素": "Remove any unsafe elements", "默认返回原参数": "Return the original parameters by default", "三兄弟": "Three brothers", "为下拉菜单默认值；": "As the default value for the dropdown menu;", "翻译后的带图文档.zip": "Translated document with images.zip", "是否使用代理": "Whether to use a proxy", "新一代插件的高级参数区确认按钮": "Confirmation button for the advanced parameter area of the new generation plugin", "声明这是一个下拉菜单": "Declare that this is a dropdown menu", "ffmpeg未安装": "FFmpeg not installed", "围绕 $ {K}^{* + } $ 的质量窗口从 $  \\pm  {100} $ 缩小至 $  \\pm  {75}\\mathrm{{MeV}}/{c}^{2} $": "Narrow the mass window around $ {K}^{* + } $ from $  \\pm  {100} $ to $  \\pm  {75}\\mathrm{{MeV}}/{c}^{2} $", "保存文件名": "Save file name", "第三种方法": "The third method", "$ 缩减到 $ \\left\\lbrack  {{75}": "$ Reduced to $ \\left\\lbrack  {{75}", "清理提取路径": "Clean up the extraction path", "history的更新方法": "Method to update the history", "定义history的后端state": "Define the backend state of the history", "生成包含图片的压缩包": "Generate a compressed package containing images", "执行插件": "Execute the plugin", "使用指定的模型": "Use the specified model", "只允许特定的类进行反序列化": "Only allow specific classes to be deserialized", "是否允许从缓存中调取结果": "Whether to allow fetching results from the cache", "效果不理想": "The effect is not ideal", "这计算是在不需要BDT要求的情况下进行的": "This calculation is done without the need for BDT requirements", "生成在线预览": "Generate online preview", "主输入": "Primary input", "定义允许的安全类": "Define allowed security classes", "其最大请求数为4096": "Its maximum request number is 4096", "在线预览翻译": "Online preview translation", "其中传入参数": "Among the incoming parameters", "下载Gradio主题时出现异常": "An exception occurred when downloading the Gradio theme", "修正一些公式问题": "Correcting some formula issues", "对专有名词、翻译语气等方面的要求": "Requirements for proper nouns, translation tone, etc.", "替换成$$": "Replace with $$", "主要用途": "Main purpose", "允许 $ {\\pi }^{0} $ 候选体的质量范围从 $ \\left\\lbrack  {0": "Allow the mass range of the $ {\\pi }^{0} $ candidate from $ \\left\\lbrack  {0", "$ {B}^{ + } $ 衰变到 $ J/\\psi {K}^{ + } $": "$ {B}^{ + } $ decays to $ J/\\psi {K}^{ + } $", "未指定路径": "Path not specified", "True为不使用": "True means not in use", "尝试获取完整的错误信息": "Attempt to get the complete error message", "仅今天": "Only today", "图 12": "Figure 12", "效果次优": "Effect is suboptimal", "绘制的Mermaid图表类型": "Types of Mermaid charts drawn", "vllm模型": "VLLM model", "文本框上方显示": "Displayed above the text box", "未来3天": "Next 3 days", "在这里添加其他安全的类": "Add other secure classes here", "额外提示词": "Additional prompt words", "由于在等离子体共轭模式中没有光子": "Due to no photons in the plasma conjugate mode", "将公式中的\\": "Escape the backslash in the formula", "插件功能": "Plugin function", "设置5秒不准咬人": "Di<PERSON>low biting for 5 seconds", "定义cookies的后端state": "Define the backend state of cookies", "选择其他类型时将直接绘制指定的图表类型": "Directly draw the specified chart type when selecting another type", "替换成$": "Replace with $", "自动从输入框同步": "Automatically sync from the input box", "第一个参数": "The first parameter", "注意需要使用双引号将内容括起来": "Note that you need to enclose the content in double quotes", "下拉菜单上方显示": "Display above the dropdown menu", "把history转存history_cache备用": "Transfer history to history_cache for backup", "从头执行": "Execute from the beginning", "选择插件参数": "Select plugin parameters", "您还可以在接入one-api/vllm/ollama时": "You can also access one-api/vllm/ollama", "输入对话存档文件名": "Enter the dialogue archive file name", "但是需要DOC2X服务": "But DOC2X service is required", "相反": "On the contrary", "你好👋": "Hello👋", "生成在线预览html": "Generate online preview HTML", "为简化拟合模型": "To simplify the fitting model", "、前端": "Front end", "定义插件的二级选项菜单": "Define the secondary option menu of the plugin", "未选定任何插件": "No plugin selected", "以上三种方法都试一遍": "Try all three methods above once", "一个非常简单的插件": "A very simple plugin", "为了更灵活地接入ollama多模型管理界面": "In order to more flexibly access the ollama multi-model management interface", "文本框内部显示": "Text box internal display", "☝️ 以上是模型路由 -=-=-=-=-=-=-=-=-=": "☝️ The above is the model route -=-=-=-=-=-=-=-=-=", "则使用当前全局模型；如设置": "Then use the current global model; if set", "由LLM决定": "Decided by LLM", "4 对模拟的修正": "4 corrections to the simulation", "glm-4v只支持一张图片": "glm-4v only supports one image", "这个并发量稍微大一点": "This concurrency is slightly larger", "无法处理EdgeTTS音频": "Unable to handle EdgeTTS audio", "早期代码": "Early code", "您可以调用下拉菜单中的“LoadChatHistoryArchive”还原当下的对话": "You can use the 'LoadChatHistoryArchive' in the drop-down menu to restore the current conversation", "因此您在定义和使用类变量时": "So when you define and use class variables", "这将通过sPlot方法进行减除": "This will be subtracted through the sPlot method", "然后再执行python代码更新history": "Then execute python code to update history", "新一代插件需要注册Class": "The new generation plugin needs to register Class", "请选择": "Please select", "旧插件的高级参数区确认按钮": "Confirm button in the advanced parameter area of the old plugin", "多数情况": "In most cases", "ollama 对齐支持 -=-=-=-=-=-=-": "ollama alignment support -=-=-=-=-=-=-", "用该压缩包+Conversation_To_File进行反馈": "Use this compressed package + Conversation_To_File for feedback", "名称": "Name", "错误处理部分": "Error handling section", "False为使用": "False for use", "详细方法见第4节": "See Section 4 for detailed methods", "在应用元组裁剪后": "After applying tuple clipping", "深度求索": "Deep Search", "绘制脑图的Demo": "Demo for Drawing Mind Maps", "需要在表格前加上一个emoji": "Need to add an emoji in front of the table", "批量Markdown翻译": "<PERSON><PERSON>", "将语言模型的生成文本朗读出来": "Read aloud the generated text of the language model", "Function旧接口仅会在“VoidTerminal”中起作用": "The old interface of Function only works in 'VoidTerminal'", "请配置 DOC2X_API_KEY": "Please configure DOC2X_API_KEY", "如果同时询问“多个”大语言模型": "If inquiring about 'multiple' large language models at the same time", "3.7 用于MC校正的宽松选择": "3.7 Loose selection for MC correction", "咬的也不是人": "Not biting humans either", "定义 后端state": "Define backend state", "这个隐藏textbox负责装入当前弹出插件的属性": "This hidden textbox is responsible for loading the properties of the current pop-up plugin", "会执行在不同的线程中": "Will be executed in different threads", "定义cookies的一个孪生的前端存储区": "Define a twin front-end storage area for cookies", "模型选择": "Model selection", "应用于信号、标准化和等离子体共轭模式的最终切割": "Final cutting applied to signal, normalization, and plasma conjugate modes", "确认参数并执行": "Confirm parameters and execute", "请先上传文件": "Please upload the file first", "以便公式渲染": "For formula rendering", "加载PDF文件": "Load PDF file", "LoadChatHistoryArchive | 输入参数为路径": "Load Chat History Archive | Input parameter is the path", "日期选择": "Date selection", "除 $ {B}^{ + } \\rightarrow  J/\\psi {K}^{ + } $ 否决": "Veto except for $ {B}^{ + } \\rightarrow  J/\\psi {K}^{ + } $", "使用 0.2 的截断值会获得类似的效率": "Using a truncation value of 0.2 will achieve similar efficiency", "请输入": "Please enter", "当注册Class后": "After registering the Class", "Markdown中使用不标准的表格": "Using non-standard tables in Markdown", "采用非常宽松的截断值": "Using very loose truncation values", "为了更灵活地接入vllm多模型管理界面": "To more flexibly access the vllm multi-model management interface", "读取解析": "Read and parse", "允许缓存": "Allow caching", "Run2 中对 Kaon 鉴别的要求被收紧为 $ \\operatorname{ProbNNk}\\left": "The requirement for Kaon discrimination in Run2 has been tightened to $ \\operatorname{ProbNNk}\\left", "当前使用人数太多": "Current user count is too high", "提取historyBox信息": "Extract historyBox information", "📚Arxiv论文精细翻译": "Fine translation of 📚Arxiv papers", "检索中": "Searching", "受到限制": "Restricted", "3. 历史输入包含图像": "3. Historical input contains images", "待通过互联网检索的问题": "Questions to be retrieved via the internet", "使用 findall 方法查找所有匹配的 Base64 字符串": "Use findall method to find all matching Base64 strings", "建立文件树": "Build file tree", "经过clip": "Through clip", "增强": "<PERSON><PERSON>ce", "对话存档": "Conversation archive", "网页": "Webpage", "怎么下载相关论文": "How to download related papers", "当前对话是关于 Nginx 的介绍和使用等": "The current conversation is about the introduction and use of Nginx, etc.", "从而提高学术论文检索的精度": "To improve the accuracy of academic paper retrieval", "使用自然语言实现您的想法": "Implement your ideas using natural language", "这样可以保证后续问答能读取到有效的历史记录": "This ensures that subsequent questions and answers can read valid historical records", "生成对比html": "Generate comparison html", "Doc2x API 页数受限": "Doc2x API page count limited", "inputs 本次请求": "Inputs for this request", "有其原问题": "Has its original question", "在线搜索失败": "Online search failed", "选择搜索引擎": "Choose a search engine", "同步已知模型的其他信息": "Synchronize other information of known models", "在线搜索服务": "Online search service", "常规对话": "Regular conversation", "使用正则表达式匹配模式": "Use regular expressions to match patterns", "从而提高网页检索的精度": "To improve the accuracy of webpage retrieval", "GPT-Academic输出文档": "GPT-Academic output document", "/* 小按钮 */": "/* Small button */", "历史记录": "History record", "上传一系列python源文件": "Upload a series of python source files", "仅DALLE3生效": "Only DALLE3 takes effect", "判断给定的单个字符是否是全角字符": "Determine if the given single character is a full-width character", "依次放入每组第一": "Put each group's first one by one", "这部分代码会逐渐移动到common.js中": "This part of the code will gradually move to common.js", "列出机器学习的三种应用": "List three applications of machine learning", "更新主输入区的参数": "Update the parameters of the main input area", "从以上搜索结果中抽取与问题": "Extract from the above search results related to the question", "* 如果解码失败": "* If decoding fails", "如果是已知模型": "If it is a known model", "一": "One", "模型切换时的回调": "Callback when switching models", "加入历史": "Add to history", "压缩结果": "Compress the result", "使用 DALLE2/DALLE3 生成图片 | 输入参数字符串": "Use DALLE2/DALLE3 to generate images | Input parameter string", "搜索分类": "Search category", "获得空的回复": "Get an empty reply", "多模态模型": "Multimodal model", "移除注释": "<PERSON>mo<PERSON> comments", "对话背景": "Conversation background", "获取需要执行的插件名称": "Get the name of the plugin to be executed", "是否启动语音输入功能": "Whether to enable voice input function", "更新高级参数输入区的参数": "Update the parameters of the advanced parameter input area", "启用多模态能力": "Enable multimodal capabilities", "请根据以上搜索结果回答问题": "Please answer the question based on the above search results", "生成的问题要求指向对象清晰明确": "The generated question requires clear and specific references to the object", "Arxiv论文翻译": "Translation of Arxiv paper", "找不到该模型": "Model not found", "提取匹配的数字部分并转换为整数": "Extract matching numeric parts and convert to integers", "尝试进行搜索优化": "Try to optimize the search", "重新梳理输入参数": "Reorganize input parameters", "存储翻译好的arxiv论文的路径": "Path to store translated arxiv papers", "尽量使用英文": "Use English as much as possible", "插件二级菜单的实现": "Implementation of plugin submenus", "* 增强优化": "Enhanced optimization", "但属于其他用户": "But belongs to another user", "不得有多余字符": "No extra characters allowed", "怎么解决": "How to solve", "根据综合回答问题": "Answer questions comprehensively", "降低温度再试一次": "Lower the temperature and try again", "作为一个网页搜索助手": "As a web search assistant", "支持将文件直接粘贴到输入区": "Support pasting files directly into the input area", "打开新对话": "Open a new conversation", "但位置非法": "But the position is illegal", "会自动读取输入框内容": "Will automatically read the input box content", "移除模块的文档字符串": "Remove the module's docstrings", "from crazy_functions.联网的ChatGPT_bing版 import 连接bing搜索回答问题": "from crazy_functions.online.ChatGPT_bing import connect_bing_search_to_answer_questions", "关闭": "Close", "学术论文": "Academic paper", "多模态能力": "Multimodal capabilities", "无渲染": "No rendering", "弃用功能": "Deprecated feature", "输入Searxng的地址": "Enter the address of Searxng", "风格": "Style", "介绍下第2点": "Introduce the second point", "你的任务是结合历史记录": "Your task is to combine historical records", "前端": "Frontend", "采取措施丢弃一部分文本": "Take measures to discard some text", "2. 输入包含图像": "2. Input contains images", "输入问题": "Input question", "可能原因": "Possible reasons", "2. Java 是一种面向对象的编程语言": "Java is an object-oriented programming language", "不支持的检索类型": "Unsupported retrieval type", "第四步": "Step four", "2. 机器学习在自然语言处理中的应用": "Applications of machine learning in natural language processing", "浮动菜单定义": "Definition of floating menu", "鿿": "Undefined", "history 历史上下文": "History context", "1. Java 是一种编译型语言": "Java is a compiled language", "请根据给定的若干条搜索结果回答问题": "Answer the question based on the given search results", "当输入文本 + 历史文本超出最大限制时": "When the input text + historical text exceeds the maximum limit", "限DALLE3": "Limited to DALLE3", "原问题": "Original question", "日志文件": "Log file", "输入图片描述": "Input image description", "示例使用": "Example usage", "后续参数": "Subsequent parameters", "请用一句话对下面的程序文件做一个整体概述": "Please give a brief overview of the program file below in one sentence", "当前对话是关于深度学习的介绍和应用等": "The current conversation is about the introduction and applications of deep learning", "点击这里输入「关键词」搜索插件": "Click here to enter 'keywords' search plugin", "按用户划分": "Divided by user", "将结果写回源文件": "Write the results back to the source file", "使用前切换到GPT系列模型": "Switch to GPT series model before using", "正在读取下一段代码片段": "Reading the next code snippet", "第二个搜索结果": "Second search result", "作为一个学术论文搜索助手": "As an academic paper search assistant", "搜索": "Search", "无法从searxng获取信息！请尝试更换搜索引擎": "Unable to retrieve information from searxng! Please try changing the search engine", "* 清洗搜索结果": "Cleaning search results", "或者压缩包": "Or compressed file", "模型": "Model", "切换布局": "Switch layout", "生成当前浏览器窗口的uuid": "Generate the uuid of the current browser window", "左上角工具栏定义": "Definition of the top-left toolbar", "from crazy_functions.联网的ChatGPT import ConnectToNetworkToAnswerQuestions": "from crazy_functions.ConnectToNetworkToAnswerQuestions import ChatGPT", "对最相关的三个搜索结果进行总结": "Summarize the top three most relevant search results", "刷新失效": "Refresh invalid", "将处理后的 AST 转换回源代码": "Convert the processed AST back to source code", "/* 插件下拉菜单 */": "/* Plugin dropdown menu */", "移除类的文档字符串": "Remove the documentation strings of a class", "请尽量不要修改": "Please try not to modify", "并更换新的 API 秘钥": "And replace with a new API key", "输入文件的路径": "Input file path", "发现异常嵌套公式": "Identify nested formula exceptions", "修复不标准的dollar公式符号的问题": "Fix the issue of non-standard dollar formula symbols", "Searxng互联网检索服务": "Searxng Internet search service", "联网检索中": "In network retrieval", "并与“原问题语言相同”": "And in the same language as the original question", "存在": "Exists", "列出Java的三种特点": "List three characteristics of Java", "3. Java 是一种跨平台的编程语言": "3. Java is a cross-platform programming language", "所有源文件均已处理完毕": "All source files have been processed", "限DALLE2": "Limited to DALLE2", "紫东太初大模型 https": "Zidong Taichu Large Model https", "🎨图片生成": "🎨 Image generation", "1. 模型本身是多模态模型": "1. The model itself is multimodal", "相关的信息": "Related information", "* 或者使用搜索优化器": "* Or use a search optimizer", "搜索查询": "Search query", "当前对话是关于 Nginx 的介绍和在Ubuntu上的使用等": "The current conversation is about the introduction of Nginx and its use on Ubuntu, etc.", "必须以json形式给出": "Must be provided in JSON format", "开启": "Turn on", "1. 机器学习在图像识别中的应用": "1. The application of machine learning in image recognition", "处理代码片段": "Processing code snippet", "则尝试获取其信息": "Then try to get its information", "已完成的文件": "Completed file", "注意这可能会消耗较多token": "Note that this may consume more tokens", "多模型对话": "Multi-model conversation", "现在有历史记录": "Now there is a history record", "你知道 Python 么": "Do you know Python?", "Base64编码": "Base64 encoding", "Gradio的inbrowser触发不太稳定": "Gradio's in-browser trigger is not very stable", "CJK标点符号": "CJK punctuation marks", "请联系 Doc2x 方面": "Please contact Doc2x for details", "耗尽generator避免报错": "Exhaust the generator to avoid errors", "📚本地Latex论文精细翻译": "📚 Local Latex paper finely translated", "* 尝试解码优化后的搜索结果": "* Try to decode the optimized search results", "为这些代码添加docstring | 输入参数为路径": "Add docstring for these codes | Input parameter is path", "读取插件参数": "Read plugin parameters", "如果剩余的行数非常少": "If the remaining lines are very few", "输出格式为JSON": "Output format is JSON", "提取QaBox信息": "Extract QaBox information", "不使用多模态能力": "Not using multimodal capabilities", "解析源代码为 AST": "Parse source code into AST", "使用前请切换模型到GPT系列": "Switch the model to GPT series before using", "中文字符": "Chinese characters", "用户的上传目录": "User's upload directory", "请将文件上传后再执行该任务": "Please upload the file before executing this task", "移除函数的文档字符串": "Remove the function's docstring", "新版-更流畅": "New version - smoother", "检索词": "Search term", "获取插件参数": "Get plugin parameters", "获取插件执行函数": "Get plugin execution function", "为“原问题”生成个不同版本的“检索词”": "Generate different versions of 'search terms' for the 'original question'", "并清洗重复的搜索结果": "Clean and remove duplicate search results", "直接返回原始问题": "Directly return the original question", "从不同角度": "From different perspectives", "展示已经完成的部分": "Display completed parts", "搜索优化": "Search optimization", "解决缩进问题": "Resolve indentation issues", "直接给出最多{num}个检索词": "Directly provide up to {num} search terms", "对话数据": "Conversation data", "定义一个正则表达式来匹配 Base64 字符串": "Define a regular expression to match Base64 strings", "转化为kwargs字典": "Convert to kwargs dictionary", "原始数据": "Original data", "当以下条件满足时": "When the following conditions are met", "主题修改": "Topic modification", "Searxng服务地址": "Searxng service address", "3. 机器学习在推荐系统中的应用": "3. Application of machine learning in recommendation systems", "全角符号": "Full-width symbols", "发送到大模型进行分析": "Send for analysis to a large model", "一个常用的测试目录": "A commonly used test directory", "在线搜索失败！": "Online search failed!", "搜索语言": "Search language", "万事俱备": "All is ready", "指定了后续参数的名称": "Specified the names of subsequent parameters", "是否使用搜索增强": "Whether to use search enhancement", "你知道 GAN 么": "Do you know about GAN?", "├── 互联网检索": "├── Internet retrieval", "公式之中出现了异常": "An anomaly occurred in the formula", "当前对话是关于深度学习的介绍和在图像识别中的应用等": "The current conversation is about the introduction of deep learning and its applications in image recognition, etc.", "返回反转后的 Base64 字符串列表": "Return a list of Base64 strings reversed", "一鼓作气处理掉": "Deal with it in one go", "剩余源文件数量": "Remaining source file count", "查互联网后回答": "Answer after checking the internet", "需要生成图像的文本描述": "Text description for generating images", "* 如果再次失败": "If failed again", "质量": "Quality", "请配置 TAICHU_API_KEY": "Please configure TAICHU_API_KEY", "most_recent_uploaded 是一个放置最新上传图像的路径": "most_recent_uploaded is a path to place the latest uploaded images", "真正的参数": "Actual parameters", "生成带注释文件": "Generate files with annotations", "源自": "From", "怎么下载": "How to download", "请稍后": "Please wait", "会尝试结合历史记录进行搜索优化": "Will try to optimize the search by combining historical records", "max_token_limit 最大token限制": "max_token_limit maximum token limit", "所有这些顾虑": "All these concerns", "宽倒披针状线形或近倒卵形；花序圆锥状或近伞房状": "Broadly lanceolate or nearly oval; inflorescence conical or nearly umbellate", "使工人的整个生活地位越来越没有保障；单个工人和单个资产者之间的冲突越来越具有两个阶级的冲突的性质": "The entire living status of workers is becoming increasingly insecure; the conflict between individual workers and individual capitalists increasingly has the nature of a conflict between two classes", "而且": "Moreover", "资产阶级无意中造成而又无力抵抗的工业进步": "The industrial progress that the bourgeoisie inadvertently causes and is powerless to resist", "力图使工人阶级厌弃一切革命运动": "Striving to make the working class disdain all revolutionary movements", "它第一个证明了": "It was the first to prove", "只不过是僧侣用来使贵族的怨愤神圣的圣水罢了": "It was merely the holy water used by monks to sanctify the grievances of the nobility", "他们再一次被可恨的暴发户打败了": "They were once again defeated by the detestable upstarts", "你们责备我们": "You blame us", "解析多个联系人的身份信息": "Analyze the identity information of multiple contacts", "I人助手": "I assistant", "就直接反对共产主义的“野蛮破坏的”倾向": "Directly opposing the 'savage destruction' tendency of communism", "然后是某一工厂的工人": "Then there are the workers of a certain factory", "无产者只有废除自己的现存的占有方式": "The proletariat can only abolish their existing mode of possession", "德国的社会主义是这种批判的可怜的回声": "German socialism is a pathetic echo of this critique", "我的朋友": "My friend", "诅咒代议制国家": "Cursing representative states", "对于中世纪被奴役的市民来说": "For the enslaved citizens of the Middle Ages", "好一个劳动得来的、自己挣得的、自己赚来的财产！你们说的是资产阶级财产出现以前的那种小资产阶级、小农的财产吗": "What a property earned through labor, earned by oneself! Are you referring to the kind of petty-bourgeois and small peasant property that existed before the emergence of bourgeois property?", "他们就不是维护他们目前的利益": "They are not defending their current interests", "新的工业的建立已经成为一切文明民族的生命攸关的问题；这些工业所加工的": "The establishment of new industries has become a matter of vital importance for all civilized nations; these industries process", "由于交通的极其便利": "Due to the extreme convenience of transportation", "资产阶级社会早就应该因懒惰而灭亡了": "Bourgeois society should have long since perished due to laziness", "精神的生产也是如此": "The production of spirit is the same", "民族之间的敌对关系就会随之消失": "The antagonistic relations between nations will then disappear", "他们以为": "They thought", "一切固定的僵化的关系以及与之相适应的素被尊崇的观念和见解都被消除了": "All fixed, rigid relationships and the corresponding revered concepts and views have been eliminated", "谈到古代所有制的时候你们所能理解的": "What you can understand when discussing ancient property", "共产党人到处都支持一切反对现存的社会制度和政治制度的革命运动": "Communists everywhere support all revolutionary movements against the existing social and political systems", "把社会主义的要求同政治运动对立起来": "Opposing the demands of socialism to political movements", "无论在英国或法国": "Whether in England or France", "都联合起来了": "All have united", "资产阶级赖以形成的生产资料和交换手段": "The means of production and exchange upon which the bourgeoisie is formed", "一切活动就会停止": "All activities will come to a halt", "较耐寒": "More cold-resistant", "把信贷集中在国家手里": "Concentrate credit in the hands of the state", "拥有发展得多的无产阶级去实现这个变革": "The proletariat, which has developed much more, will realize this transformation", "正如僧侣总是同封建主携手同行一样": "Just as monks always walk hand in hand with feudal lords", "他们的目的只有用暴力推翻全部现存的社会制度才能达到": "Their goal can only be achieved by violently overthrowing all existing social systems", "因为他们力图使历史的车轮倒转": "Because they strive to turn back the wheels of history", "他们想也没有想到": "They never even thought", "因为我是私生子": "Because I am a bastard", "这些社会主义和共产主义的著作也含有批判的成分": "These works of socialism and communism also contain critical elements", "他在守夜人军团中与我并肩作战": "He fought alongside me in the Night Watch Legion", "凯特琳·史塔克": "<PERSON><PERSON><PERSON>", "正是要消灭资产者的个性、独立性和自由": "It is precisely to eliminate the individuality, independence, and freedom of the capitalists", "在无产阶级还很不发展、因而对本身的地位的认识还基于幻想的时候": "When the proletariat is still underdeveloped, and thus their understanding of their own position is still based on illusions", "资产阶级生存和统治的根本条件": "The fundamental conditions for the survival and rule of the bourgeoisie", "这样的个性确实应当被消灭": "Such individuality should indeed be eliminated", "萼片呈宽三角形": "The sepals are broad and triangular", "铁路的通行": "The passage of railways", "不言而喻": "It goes without saying", "隐藏在这些偏见后面的全都是资产阶级利益": "All that is hidden behind these prejudices are the interests of the bourgeoisie", "工人有时也得到胜利": "Workers sometimes also achieve victory", "如果不炸毁构成官方社会的整个上层": "If the entire upper layer constituting the official society is not blown up", "诅咒资产阶级的竞争、资产阶级的新闻出版自由、资产阶级的法、资产阶级的自由和平等": "Cursing the competition of the bourgeoisie, the freedom of the bourgeois press, the law of the bourgeoisie, the freedom and equality of the bourgeoisie", "一个幽灵": "A specter", "或者毋宁说": "Or rather", "它越来越感觉到自己的力量": "It increasingly feels its own power", "已清空": "Has been emptied", "是现存制度的真实的社会基础": "Is the real social foundation of the existing system", "他们只是表明了一个事实": "They merely indicate a fact", "用诅咒异端邪说的传统办法诅咒自由主义": "Cursing liberalism in the traditional way of cursing heretical doctrines", "但颜色会稍有不同": "But the colors may be slightly different", "这种在法国人的论述下面塞进自己哲学词句的做法": "This practice of inserting one's philosophical phrases into the discourse of the French", "一、资产者和无产者": "1. The bourgeoisie and the proletariat", "使之变成完全相反的东西": "Transforming it into something completely opposite", "物质的生产是如此": "Material production is such", "私有制一消灭": "The abolition of private property", "从这种关系中产生的公妻制": "The communal wife system arising from this relationship", "基督教不是也激烈反对私有财产": "Isn't Christianity also fiercely opposed to private property?", "甚至使得统治阶级中的一小部分人脱离统治阶级而归附于革命的阶级": "Even causing a small portion of the ruling class to detach from the ruling class and join the revolutionary class", "在德国": "In Germany", "最先进的国家几乎都可以采取下面的措施": "The most advanced countries can almost adopt the following measures", "使生产资料集中起来": "Concentrating the means of production", "并不是共产主义所独具的特征": "Is not a characteristic unique to communism", "他们总是不加区别地向整个社会呼吁": "They always indiscriminately appeal to the entire society", "输入“清空向量数据库”可以清空RAG向量数据库": "Entering 'clear vector database' can clear the RAG vector database", "从而废除全部现存的占有方式": "Thus abolishing all existing forms of possession", "不过是使防止危机的手段越来越少的办法": "Merely a way to reduce the means of preventing crises", "而仅仅是物质生活条件即经济关系的改变": "But merely a change in material living conditions, that is, economic relations", "他们是产业军的普通士兵": "They are the ordinary soldiers of the industrial army", "这种组织总是重新产生": "Such organizations always re-emerge", "小资产者曾经在封建专制制度的束缚下挣扎到资产者的地位": "Petty bourgeoisie once struggled under the constraints of feudal despotism to attain the status of bourgeoisie", "共产党人同全体无产者的关系是怎样的呢": "What is the relationship between communists and all proletarians?", "即工人为维持其工人的生活所必需的生活资料的数额": "That is, the amount of living materials necessary for workers to maintain their livelihood", "由于开拓了世界市场": "Due to the expansion of the world market", "大工业建立了由美洲的发现所准备好的世界市场": "Large-scale industry established a world market prepared by the discoveries of America", "开垦荒地和改良土壤": "Reclaiming wasteland and improving soil", "它要求无产阶级实现它的体系": "It requires the proletariat to realize its system", "我们是要这样做的": "We are going to do this", "美洲的发现、绕过非洲的航行": "The discovery of America, the navigation around Africa", "有人责备我们共产党人": "Some blame us communists", "毫不奇怪": "Not surprising at all", "它发展到最后": "It develops to the end", "从而劳动的价格": "Thus the price of labor", "它必须到处落户": "It must settle everywhere", "共产主义并不剥夺任何人占有社会产品的权力": "Communism does not deprive anyone of the right to own social products", "取消民族": "Abolish nations", "他们在法国的原著下面写上自己的哲学胡说": "They write their philosophical nonsense under the original works in France", "谈到封建所有制的时候你们所能理解的": "What you can understand when talking about feudal ownership", "这种超乎阶级斗争的幻想": "This fantasy beyond class struggle", "还要大": "It must be larger", "使工人的工资越来越不稳定；机器的日益迅速的和继续不断的改良": "Making workers' wages increasingly unstable; the rapid and continuous improvement of machines", "建立在私人发财上面的": "Built on private wealth", "它要废除宗教、道德": "It aims to abolish religion and morality", "在无产者不同的民族的斗争中": "In the struggles of the proletariat of different nations", "是景天科景天属的多肉植物": "It is a succulent plant of the Crassulaceae family", "整个整个大陆的开垦": "The cultivation of the entire continent", "无产者不是同自己的敌人作斗争": "The proletariat does not fight against its own enemies", "现世界多地均有栽培": "Cultivated in many places around the world today", "“真正的”社会主义像瘟疫一样流行起来了": "\"True\" socialism has spread like a plague", "这种社会主义是这些政府用来镇压德国工人起义的毒辣的皮鞭和枪弹的甜蜜的补充": "This socialism is the sweet complement of the vicious whip and bullets used by these governments to suppress the German workers' uprising", "资产阶级挖掉了工业脚下的民族基础": "The bourgeoisie has undermined the national foundation of industry", "工人革命的第一步就是使无产阶级上升为统治阶级": "The first step of the workers' revolution is to elevate the proletariat to the ruling class", "不过表明竞争在信仰领域里占统治地位罢了": "It only indicates that competition dominates the realm of belief", "绝对不是只有通过革命的途径才能实现的资产阶级生产关系的废除": "The abolition of bourgeois production relations is not achievable solely through revolutionary means", "当阶级差别在发展进程中已经消失而全部生产集中在联合起来的个人的手里的时候": "When class differences have disappeared in the process of development and all production is concentrated in the hands of united individuals", "Rag智能召回": "Rag intelligent recall", "4、没收一切流亡分子和叛乱分子的财产": "4. Confiscate all property of exiles and rebels", "雇佣工人靠自己的劳动所占有的东西": "What hired workers possess through their own labor", "机器的采用": "The adoption of machines", "也就是采取这样一些措施": "That is to take such measures", "共产主义的特征并不是要废除一般的所有制": "The characteristic of communism is not to abolish general ownership", "都伴随着相应的政治上的进展": "All accompanied by corresponding political progress", "当厂主对工人的剥削告一段落": "When the exploitation of workers by factory owners comes to an end", "它利用资产阶级内部的分裂": "It exploits the divisions within the bourgeoisie", "狂热地迷信自己那一套社会科学的奇功异效": "Fanatically superstitious about the miraculous effects of their own social sciences", "民族的片面性和局限性日益成为不可能": "The one-sidedness and limitations of nations are increasingly becoming impossible", "贫困比人口和财富增长得还要快": "Poverty is growing faster than population and wealth", "无产阶级反对资产阶级的斗争首先是一国范围内的斗争": "The struggle of the proletariat against the bourgeoisie is primarily a struggle within one country", "向量化完成": "Vectorization completed", "德国的社会主义者给自己的那几条干瘪的“永恒真理”披上一件用思辨的蛛丝织成的、绣满华丽辞藻的花朵和浸透甜情蜜意的甘露的外衣": "German socialists drape their few dry 'eternal truths' in a garment woven from the threads of speculative thought, adorned with flowery language and soaked in sweet sentiment", "花朵玲珑小巧": "The flowers are exquisite and delicate", "共产党人支持激进派": "Communists support the radicals", "可盆栽放置于电视、电脑旁": "Can be potted and placed next to the TV or computer", "或者是企图重新把现代的生产资料和交换手段硬塞到已被它们突破而且必然被突破的旧的所有制关系的框子里去": "Or attempting to forcibly shove modern means of production and exchange back into the outdated ownership relations that have already been transcended and will inevitably be transcended", "从而对生产关系": "Thus affecting the production relations", "他们控告资产阶级的主要罪状正是在于": "Their main accusation against the bourgeoisie is that", "把自身组织成为民族": "They organize themselves into a nation", "以便在推翻德国的反动阶级之后立即开始反对资产阶级本身的斗争": "So as to immediately begin the struggle against the bourgeoisie itself after overthrowing the reactionary class in Germany", "甚至工场手工业也不再能满足需要了": "Even handicrafts in factories can no longer meet the needs", "而代表真理的要求": "And the demands that represent the truth", "经不起较大的资本家的竞争；有的是因为他们的手艺已经被新的生产方法弄得不值钱了": "Cannot withstand competition from larger capitalists; some of their skills have become worthless due to new production methods", "故而得名“石莲”": "Hence the name 'stone lotus'", "不管这个问题的发展程度怎样": "Regardless of the level of development of this issue", "他们公开宣布": "They openly declare", "一部分法国正统派和“青年英国”": "A part of the French orthodox and 'Young England'", "不能理解该联系人": "Cannot understand the contact person", "法国革命废除了封建的所有制": "The French Revolution abolished feudal ownership", "是无产阶级获得解放的首要条件之一": "Is one of the primary conditions for the liberation of the proletariat", "买卖一消失": "Trade disappears", "汇合成阶级斗争": "Converges into class struggle", "争得民主": "Strive for democracy", "半是谤文": "Half slanderous writing", "生长适温为15-25℃": "Optimal growth temperature is 15-25°C", "小资产阶级的社会主义": "Petty-bourgeois socialism", "使农民的民族从属于资产阶级的民族": "Makes the farmers' nation subordinate to the bourgeois nation", "任何一个时代的统治思想始终都不过是统治阶级的思想": "The ruling ideology of any era is always merely the ideology of the ruling class", "我们用社会教育代替家庭教育": "We replace family education with social education", "关于自由买卖的言论": "Statements about free trade", "无产阶级将利用自己的政治统治": "The proletariat will utilize its political power", "自由买卖": "Free trade", "现今社会的最下层": "The lowest strata of today's society", "他们并不是随着工业的进步而上升": "They do not rise with the progress of industry", "才能取得社会生产力": "In order to achieve social productivity", "忌寒冷和过分潮湿": "Avoid cold and excessive humidity", "法国和英国的贵族": "The nobility of France and England", "资产阶级日甚一日地消灭生产资料、财产和人口的分散状态": "The bourgeoisie increasingly eliminates the scattered state of means of production, property, and population", "基生叶莲座状": "Basal leaf rosette", "个联系人": "Individual contacts", "无产阶级": "Proletariat", "它把人的尊严变成了交换价值": "It transforms human dignity into exchange value", "要求他做的只是极其简单、极其单调和极容易学会的操作": "What is required of him is only extremely simple, monotonous, and easy-to-learn operations", "劳动量出就越增加": "The more labor is produced, the greater the increase", "而且作为变革全部生产方式的手段是必不可少的": "And it is indispensable as a means of transforming all modes of production", "所以它本身还是民族的": "So it is still national in itself", "都使无产者失去了任何民族性": "All make the proletariat lose any sense of nationality", "就是保存德国的现存制度": "It is to preserve the existing system of Germany", "他们的剥削方式和资产阶级的剥削不同": "Their mode of exploitation is different from that of the bourgeoisie", "各自独立的、几乎只有同盟关系的、各有不同利益、不同法律、不同政府、不同关税的各个地区": "Each region is independent, almost only having an alliance relationship, with different interests, laws, governments, and tariffs", "越来越严重了": "It is becoming increasingly serious", "总是使整个社会服从于它们发财致富的条件": "Always makes the entire society subordinate to the conditions for their wealth", "都发现他们的臀部带有旧的封建纹章": "All find that their backs bear the old feudal emblem", "即德国小市民的利益": "That is, the interests of the German petty bourgeoisie", "它们被新的工业排挤掉了": "They have been pushed out by new industries", "工资也就越少": "Wages also become less", "资产阶级都不得不向无产阶级呼吁": "The bourgeoisie has to appeal to the proletariat", "他们也意识到": "They also realize", "又有哪一个反对党不拿共产主义这个罪名去回敬更进步的反对党人和自己的反动敌人呢": "Which opposition party does not use the label of communism to counter more progressive opponents and their own reactionary enemies?", "手工业者和农民——所有这些阶级都降落到无产阶级的队伍里来了": "Artisans and peasants—all these classes have descended into the ranks of the proletariat", "我的弟弟": "My brother", "而这种阶级对立在当时刚刚开始发展": "And this class opposition was just beginning to develop at that time", "在现今的资产阶级生产关系的范围内": "Within the scope of today's bourgeois production relations", "去干反动的勾当": "To engage in reactionary activities", "植株多分枝": "Plants have multiple branches", "它在现代的代议制国家里夺得了独占的政治统治": "It has gained exclusive political dominance in modern representative states", "我们的资产者装得道貌岸然": "Our capitalists pretend to be virtuous", "同直接剥削他们的单个资产者作斗争": "To struggle against individual capitalists who directly exploit them", "没有的事": "Something that does not exist", "蒸汽和机器引起了工业生产的革命": "Steam and machines have caused a revolution in industrial production", "不也是由社会通过学校等等进行的直接的或间接的干涉决定的吗": "Isn't it also determined by direct or indirect interference from society through schools and so on?", "资产阶级的所有制关系": "The ownership relations of the bourgeoisie", "最初是单个的工人": "Initially, it was individual workers", "工人是分散在全国各地并为竞争所分裂的群众": "Workers are scattered across the country and divided by competition", "他们逐渐地堕落到上述反动的或保守的社会主义者的一伙中去了": "They gradually fell into the aforementioned reactionary or conservative socialists' group", "每当人民跟着他们走的时候": "Whenever the people follow them", "旧社会的生活条件已经被消灭了": "The living conditions of the old society have been eliminated", "我的哥哥": "My brother", "你们说": "You say", "都是为了维护他们这种中间等级的生存": "All to maintain the existence of their intermediate class", "法国的社会主义和共产主义的文献是在居于统治地位的资产阶级的压迫下产生的": "The literature of socialism and communism in France arose under the oppression of the ruling bourgeoisie", "而是一些在这种生产关系的基础上实行的行政上的改良": "But rather some administrative reforms implemented on the basis of these production relations", "“但是”": "\"But\"", "资产阶级在历史上曾经起过非常革命的作用": "The bourgeoisie has played a very revolutionary role in history", "你们是责备我们要消灭父母对子女的剥削吗": "Are you blaming us for wanting to eliminate parental exploitation of children?", "批判的空想的社会主义和共产主义的意义": "The significance of critical utopian socialism and communism", "但是共产主义要废除永恒真理": "But communism aims to abolish eternal truths", "随着人们的生活条件、人们的社会关系、人们的社会存在的改变而改变": "Change with the changes in people's living conditions, social relations, and social existence", "他们获得的将是整个世界": "What they gain will be the whole world", "它所统治的世界自然是最美好的世界": "The world it dominates is naturally the best world", "它摇摆于无产阶级和资产阶级之间": "It swings between the proletariat and the bourgeoisie", "就使资产阶级所有制的存在受到威胁": "It threatens the existence of bourgeois ownership", "自然就不能不想到妇女也会遭到同样的命运": "Naturally, one cannot help but think that women will also suffer the same fate", "特别是在农业方面": "Especially in agriculture", "那是鉴于他们行将转入无产阶级的队伍": "That is in view of their impending transition into the ranks of the proletariat", "你们所理解的个性": "The individuality you understand", "你们的利己观念使你们把自己的生产关系和所有制关系从历史的、在生产过程中是暂时的关系变成永恒的自然规律和理性规律": "Your egoistic views lead you to transform your production and ownership relations, which are historically and temporarily contingent in the production process, into eternal natural laws and rational laws", "过去那种地方的和民族的自给自足和闭关自守状态": "The past state of local and national self-sufficiency and isolationism", "这些形式": "These forms", "为了拉拢人民": "In order to win over the people", "并且每天都还在被消灭": "And are being eliminated every day", "即同专制君主制的残余、地主、非工业资产者和小资产者作斗争": "That is, fighting against the remnants of autocratic monarchy, landlords, non-industrial capitalists, and petty bourgeoisie", "共产党人为工人阶级的最近的目的和利益而斗争": "Communists fight for the immediate aims and interests of the working class", "共产党人不是同其他工人政党相对立的特殊政党": "Communists are not a special party opposed to other workers' parties", "正像它使农村从属于城市一样": "Just as it subordinates the countryside to the city", "与原变种的不同处为叶上部有渐尖的锯齿": "The difference from the original variant is that the upper part of the leaf has gradually pointed serrations", "在商业、工业和农业中很快就会被监工和雇员所代替": "In commerce, industry, and agriculture, it will soon be replaced by foremen and employees", "社会的活动要由他们个人的发明活动来代替": "Social activities should be replaced by their individual inventive activities", "封建的社会主义": "Feudal socialism", "要消灭构成个人的一切自由、活动和独立的基础的财产": "To abolish the property that constitutes the basis of all individual freedom, activity, and independence", "在你们的现存社会里": "In your existing society", "使社会失去了全部生活资料；仿佛是工业和商业全被毁灭了": "Has caused society to lose all means of subsistence; as if industry and commerce have been completely destroyed", "随着这些早期的无产阶级运动而出现的革命文献": "Revolutionary literature that emerged alongside these early proletarian movements", "的二年生草本植物": "Biennial herbaceous plants", "在资产阶级社会里是过去支配现在": "In bourgeois society, the past dominates the present", "随着资产阶级即资本的发展": "With the development of the bourgeoisie, that is, capital", "不断扩大产品销路的需要": "The need to continuously expand the market for products", "是从小资产阶级的立场出发替工人说话的": "Speaks for the workers from the standpoint of the petty bourgeoisie", "而且是大君主国的主要基础；最后": "And is the main foundation of the great monarchies; finally", "决不是以这个或那个世界改革家所发明或发现的思想、原则为根据的": "It is certainly not based on the ideas or principles invented or discovered by this or that reformer of the world", "都属于这一类卑鄙龌龊的、令人委靡的文献": "All belong to this kind of despicable, sordid, and demoralizing literature", "这里所改变的只是财产的社会性质": "What changes here is only the social nature of property", "于是由许多种民族的和地方的文学形成了一种世界的文学": "Thus, a world literature is formed from the literatures of many nations and localities", "工人的大规模集结": "The large-scale mobilization of workers", "无产者的劳动已经失去了任何独立的性质": "The labor of the proletariat has lost any independent nature.", "这些原理不过是现存的阶级斗争、我们眼前的历史运动的真实关系的一般表述": "These principles are merely a general expression of the real relationships of the existing class struggle and the historical movement before us.", "资产阶级也在同一程度上得到发展": "The bourgeoisie has developed to the same extent.", "但是并不忽略这个政党是由互相矛盾的分子组成的": "However, it should not be overlooked that this party is composed of contradictory elements.", "在通风透气、排水良好的土壤上生长良好": "It grows well in well-ventilated, well-drained soil.", "它只是用新的阶级、新的压迫条件、新的斗争形式代替了旧的": "It merely replaces the old with new classes, new conditions of oppression, and new forms of struggle.", "而是资产阶级联合的结果": "But rather the result of the unity of the bourgeoisie.", "社会再不能在它统治下生存下去了": "Society can no longer survive under its rule.", "他们就离开自己原来的立场": "They leave their original positions.", "有傅立叶主义者反对改革派": "There are Fourierists opposing the reformists.", "例如消灭城乡对立": "For example, the elimination of the urban-rural divide.", "而活动着的个人却没有独立性和个性": "But the active individuals lack independence and personality.", "河川的通航": "The navigation of rivers.", "这不过是资产阶级准备更全面更猛烈的危机的办法": "This is merely a way for the bourgeoisie to prepare for a more comprehensive and intense crisis.", "这些措施在经济上似乎是不够充分的和没有力量的": "These measures seem insufficient and powerless economically.", "在共产主义社会里": "In a communist society.", "整个历史运动都集中在资产阶级手里；在这种条件下取得的每一个胜利都是资产阶级的胜利": "The entire historical movement is concentrated in the hands of the bourgeoisie; every victory achieved under these conditions is a victory for the bourgeoisie.", "联合起来！": "Unite!", "其实它不过是要求无产阶级停留在现今的社会里": "In fact, it merely demands that the proletariat remain in the current society.", "生长速度较虹之玉慢很多": "Grows much slower than the rainbow jade.", "劳动越使人感到厌恶": "The more labor makes people feel disgusted.", "僧侣们曾经在古代异教经典的手抄本上面写上荒诞的天主教圣徒传": "Monks once wrote absurd Catholic saint biographies on manuscripts of ancient pagan classics.", "同时": "At the same time.", "这是由于当时无产阶级本身还不够发展": "This is because the proletariat itself was not sufficiently developed at that time.", "对所谓的共产党人的正式公妻制表示惊讶": "Expressing surprise at the formal communal marriage system of the so-called communists.", "推翻资产阶级的统治": "Overthrow the rule of the bourgeoisie.", "从此就再谈不上严重的政治斗争了": "From then on, serious political struggles could no longer be discussed.", "共产主义的幽灵": "The ghost of communism.", "他们用来泄愤的手段是": "The means they use to vent their anger are.", "禁用stream的特殊模型处理": "Special model processing that prohibits the use of streams.", "有哪一个反对党不被它的当政的敌人骂为共产党呢": "Which opposition party is not labeled as a communist by its ruling enemies?", "就不能抬起头来": "Cannot raise their heads.", "于是": "Then", "——整个资产阶级异口同声地向我们这样叫喊": "—— The entire bourgeoisie shouted at us in unison", "它必须被炸毁": "It must be destroyed", "而对于共产主义要消灭买卖、消灭资产阶级生产关系和资产阶级本身这一点来说": "And regarding communism's goal to abolish trade, eliminate bourgeois production relations and the bourgeoisie itself", "一句话": "In a word", "3．批判的空想的社会主义和共产主义": "3. Critique of Utopian Socialism and Communism", "本来意义的社会主义和共产主义的体系": "The system of socialism and communism in its original sense", "它在自己的发展进程中要同传统的观念实行最彻底的决裂": "It must achieve a complete break with traditional concepts in its development process", "而且主要是向统治阶级呼吁": "And it mainly appeals to the ruling class", "虹之玉锦与虹之玉的叶片大小没有特别大的变化": "There is no significant change in the size of the leaves of the Rainbow Jade and Rainbow Jade varieties", "德国的社会主义恰好忘记了": "German socialism has just forgotten", "我们要消灭私有制": "We must abolish private property", "四、共产党人对各种反对党派的态度": "4. The attitude of communists towards various opposing parties", "从而消灭了它自己这个阶级的统治": "Thus eliminating the rule of its own class", "长得好像长耳朵小兔": "Looks like a little rabbit with long ears", "9、把农业和工业结合起来": "9. Combine agriculture and industry", "根据提示": "According to the prompt", "为虹之玉的锦化品种": "For the variegated variety of Rainbow Jade", "为了对这个幽灵进行神圣的围剿": "To carry out a sacred encirclement against this specter", "它只有通过社会许多成员的共同活动": "It can only be achieved through the collective activities of many members of society", "他们激烈地反对工人的一切政治运动": "They fiercely oppose all political movements of the workers", "生产力已经强大到这种关系所不能适应的地步": "The productive forces have become so powerful that this relationship can no longer adapt", "那里的资产阶级才刚刚开始进行反对封建专制制度的斗争": "There, the bourgeoisie has just begun to fight against feudal despotism", "而你们的教育不也是由社会决定的吗": "And isn't your education also determined by society?", "将问答数据记录到向量库中": "Record the Q&A data into the vector database", "除了极少数的例外": "With very few exceptions", "将是这样一个联合体": "It will be such a union", "既然这种文献在德国人手里已不再表现一个阶级反对另一个阶级的斗争": "Since this kind of literature no longer represents the struggle of one class against another in the hands of Germans", "硬说能给工人阶级带来好处的并不是这样或那样的政治改革": "It is not this or that political reform that can supposedly benefit the working class", "如果用户列举联系人": "If the user lists contacts", "半是挽歌": "Half is a dirge", "男工也就越受到女工和童工的排挤": "Male workers are increasingly pushed out by female and child workers", "他们很快就会完全失去他们作为现代社会中一个独立部分的地位": "They will soon completely lose their status as an independent part of modern society.", "特别是已经提高到从理论上认识整个历史运动这一水平的一部分资产阶级思想家": "Especially a part of the bourgeois thinkers who have elevated their understanding to the theoretical level of recognizing the entire historical movement.", "就是说": "That is to say.", "因为无产阶级首先必须取得政治统治": "Because the proletariat must first achieve political power.", "因而也就可以了解他们同英国宪章派和北美土地改革派的关系": "Thus, they can also understand their relationship with the British Chartists and the North American land reformers.", "由于一切生产工具的迅速改进": "Due to the rapid improvement of all means of production.", "它的生存不再同社会相容了": "Its existence is no longer compatible with society.", "共产党人可以把自己的理论概括为一句话": "Communists can summarize their theory in one sentence.", "因而无产阶级内部的利益、生活状况也越来越趋于一致": "Thus, the interests and living conditions within the proletariat are increasingly converging.", "缺水时容易耷拉下来；具枝干": "Easily droops when lacking water; has branches.", "由于阶级对立的发展是同工业的发展步调一致的": "The development of class opposition is in step with the development of industry.", "会给无产者创造出财产来吗": "Will it create property for the proletariat?", "冬季温度不低于5℃": "Winter temperatures do not drop below 5°C.", "他们就以为自己是高高超乎这种阶级对立之上的": "They think they are above this class opposition.", "社会突然发现自己回到了一时的野蛮状态；仿佛是一次饥荒、一场普遍的毁灭性战争": "Society suddenly finds itself back in a temporary state of barbarism; as if there were a famine or a widespread catastrophic war.", "在农民阶级远远超过人口半数的国家": "In countries where the peasant class far exceeds half of the population.", "无产阶级经历了各个不同的发展阶段": "The proletariat has gone through various stages of development.", "是同无产阶级对社会普遍改造的最初的本能的渴望相适应的": "It corresponds to the initial instinctive desire of the proletariat for the universal transformation of society.", "才获得自己的适当的表现": "Only then does it gain its appropriate expression.", "而且同时供世界各地消费": "And at the same time, it is supplied for consumption around the world.", "也被扩及到精神产品的占有和生产方面": "It is also extended to the possession and production of spiritual products.", "from shared_utils.colorful import print亮黄": "from shared_utils.colorful import printBrightYellow", "各国人民之间的民族分隔和对立日益消失": "The national divisions and oppositions between peoples of different countries are increasingly disappearing.", "取消现在这种形式的儿童的工厂劳动": "Abolish the current form of child labor in factories.", "他们每日每时都受机器、受监工、首先是受各个经营工厂的资产者本人的奴役": "They are daily and hourly subjected to the machines, to overseers, and primarily to the enslavement of the capitalists operating the factories themselves.", "捣毁机器": "Smash the machines.", "你们就惊慌起来": "You become alarmed.", "于是他们就去探求某种社会科学、社会规律": "Thus, they seek some kind of social science, social laws.", "它们所知道的只是这种对立的早期的、不明显的、不确定的形式": "What they know is only the early, indistinct, and uncertain forms of this opposition.", "福娘的物种": "Species of the blessed mother.", "整个社会日益分裂为两大敌对的阵营": "The whole society is increasingly divided into two major opposing camps.", "封建的农业和工场手工业组织": "Feudal agriculture and workshop handicraft organization.", "他们拒绝一切政治行动": "They reject all political action", "把资本变为公共的、属于社会全体成员的财产": "Transforming capital into public property that belongs to all members of society", "夏季高温休眠明显": "The summer heat dormancy is obvious", "就像掌握外国语一样": "Just like mastering a foreign language", "说他们要取消祖国": "Saying they want to abolish the homeland", "是财富在私人手里的积累": "It is the accumulation of wealth in private hands", "山姆威尔·塔利": "<PERSON>", "阳光充足": "Plenty of sunshine", "是在无产阶级和资产阶级之间的斗争还不发展的最初时期出现的": "It appeared in the early stages when the struggle between the proletariat and the bourgeoisie was not yet developed", "他们愿意要资产阶级": "They are willing to have the bourgeoisie", "另一方面是由于革命无产阶级的兴起": "On the other hand, it is due to the rise of the revolutionary proletariat", "对话句柄": "Dialogue handle", "他们听说生产工具将要公共使用": "They heard that the means of production will be used publicly", "以免于灭亡": "To avoid extinction", "是以现代的资产阶级社会以及相应的物质生活条件和相当的政治制度为前提的": "It is based on modern bourgeois society and corresponding material living conditions and political systems", "就使整个资产阶级社会陷入混乱": "This plunges the entire bourgeois society into chaos", "它变成了束缚生产的桎梏": "It has become a shackle that binds production", "淹没在利己主义打算的冰水之中": "Drowned in the icy waters of selfish intentions", "法国的生活条件却没有同时搬过去": "The living conditions in France did not move over at the same time", "如果说它通过革命使自己成为统治阶级": "If it becomes the ruling class through revolution", "有些地方": "Some places", "德国的特别是普鲁士的资产阶级反对封建主和专制王朝的斗争": "The struggle of the bourgeoisie in Germany, especially Prussia, against feudal lords and autocratic dynasties", "那么它在消灭这种生产关系的同时": "Then it simultaneously eliminates this production relationship", "卵圆形": "Oval shape", "他们一贯企图削弱阶级斗争": "They consistently attempt to weaken class struggle", "3、废除继承权": "3. Abolish inheritance rights", "英国的十小时工作日法案就是一个例子": "The British Ten Hours Act is an example", "他们看不到无产阶级方面的任何历史主动性": "They see no historical initiative from the proletariat", "瑞肯·史塔克": "<PERSON><PERSON>", "贵族们不得不装模作样": "The nobles had to put on a show", "工场手工业代替了这种经营方式": "Workshop handcraft replaced this mode of operation", "——这就是它的结论": "——This is its conclusion", "按照他们的历史地位所负的使命": "According to the mission imposed by their historical status", "这种利己观念是你们和一切灭亡了的统治阶级所共有的": "This self-serving concept is shared by you and all the ruling classes that have perished", "他们是在完全不同的、目前已经过时的情况和条件下进行剥削的": "They exploit under completely different and now outdated circumstances and conditions", "在公社里是武装的和自治的团体": "In the commune, they are armed and self-governing groups", "他们要改善社会一切成员的生活状况": "They aim to improve the living conditions of all members of society", "他们愿意要现存的社会": "They are willing to accept the existing society", "它在封建主统治下是被压迫的等级": "It is an oppressed class under feudal lord rule", "半是未来的恫吓；它有时也能用辛辣、俏皮而尖刻的评论剌中资产阶级的心": "Half a threat of the future; sometimes it can also pierce the heart of the bourgeoisie with sharp, witty, and incisive comments", "现在是共产党人向全世界公开说明自己的观点、自己的目的、自己的意图并且拿党自己的宣言来反驳关于共产主义幽灵的神话的时候了": "Now is the time for communists to publicly explain their views, their goals, their intentions to the world and to use the party's own manifesto to refute the myths about the specter of communism", "共产党人的理论原理": "The theoretical principles of communists", "他们都只是劳动工具": "They are merely tools of labor", "共产党人同社会主义民主党联合起来反对保守的和激进的资产阶级": "Communists unite with the socialist democrats to oppose the conservative and radical bourgeoisie", "这一阶级的成员经常被竞争抛到无产阶级队伍里去": "Members of this class are often thrown into the ranks of the proletariat by competition", "并且作为资产阶级社会的补充部分不断地重新组成": "And they are constantly reconstituted as a supplementary part of bourgeois society", "这就是说": "That is to say", "长大后叶子会慢慢变长变粗": "As they grow, the leaves will gradually become longer and thicker", "都不可避免地遭到了失败": "All inevitably faced failure", "却是毫无意义的": "But it is meaningless", "在这个阶段上": "At this stage", "多年生肉质草本植物": "Perennial succulent herbaceous plants", "我们可以举蒲鲁东的《贫困的哲学》作为例子": "We can take <PERSON><PERSON><PERSON>'s 'The Philosophy of Poverty' as an example", "智能召回 RAG": "Intelligent recall RAG", "在危机期间": "During the crisis", "在无产阶级的生活条件中": "In the living conditions of the proletariat", "共产党人并没有发明社会对教育的作用；他们仅仅是要改变这种作用的性质": "Communists did not invent the role of society in education; they merely seek to change the nature of that role", "就必须保证这个阶级至少有能够勉强维持它的奴隶般的生存的条件": "It must ensure that this class at least has the conditions to barely maintain its slave-like existence", "中间等级": "Intermediate class", "“真正的”社会主义能起一箭双雕的作用": "\"True\" socialism can serve a dual purpose", "我的恋人": "My lover", "其次": "Secondly", "过去的一切运动都是少数人的或者为少数人谋利益的运动": "All past movements were either movements of a minority or for the benefit of a minority", "字": "Word", "这种社会主义成了德意志各邦专制政府及其随从——僧侣、教员、容克和官僚求之不得的、吓唬来势汹汹的资产阶级的稻草人": "This type of socialism has become a straw man for the autocratic governments of the German states and their followers—clergymen, educators, Junkers, and bureaucrats—who seek to intimidate the bourgeoisie.", "那么它也就直接代表了一种反动的利益": "Then it directly represents a reactionary interest.", "我的养父": "My adoptive father.", "特别是一切革命行动；他们想通过和平的途径达到自己的目的": "Especially all revolutionary actions; they want to achieve their goals through peaceful means.", "在他们心目中": "In their minds.", "受着各级军士和军官的层层监视": "Under the close surveillance of soldiers and officers at all levels.", "我们眼前又进行着类似的运动": "We are witnessing a similar movement before us.", "难道雇佣劳动": "Isn't wage labor.", "拟定了如下的宣言": "Drafted the following declaration.", "挤在工厂里的工人群众就像士兵一样被组织起来": "The workers crowded in the factories are organized like soldiers.", "这种交通工具把各地的工人彼此联系起来": "This means of transportation connects workers from different places.", "但这种胜利只是暂时的": "But this victory is only temporary.", "问题正在于使妇女不再处于单纯生产工具的地位": "The issue is to free women from being mere instruments of production.", "调用ListFriends": "Call ListFriends.", "上升为民族的阶级": "Rising to become a national class.", "那就请你们不要同我们争论了": "Then please do not argue with us.", "从中世纪的农奴中产生了初期城市的城关市民；从这个市民等级中发展出最初的资产阶级分子": "The early urban bourgeoisie emerged from the serfs of the Middle Ages; from this citizen class developed the initial bourgeois elements.", "德国的社会主义也越来越认识到自己的使命就是充当这种小市民的夸夸其谈的代言人": "German socialism is increasingly recognizing its mission to act as a spokesperson for the petty bourgeoisie's grandiloquent talk.", "看到社会地位分成多种多样的层次": "Seeing social status divided into various levels.", "资本不是一种个人力量": "Capital is not a personal power.", "资产阶级关于家庭和教育、关于父母和子女的亲密关系的空话就越是令人作呕": "The bourgeoisie's empty talk about family and education, about the intimate relationships between parents and children, is increasingly nauseating.", "现今的这种财产是在资本和雇佣劳动的对立中运动的": "Today's property moves within the opposition between capital and wage labor.", "起而代之的是自由竞争以及与自由竞争相适应的社会制度和政治制度、资产阶级的经济统治和政治统治": "What replaces it is free competition and the social and political systems that correspond to free competition, the economic and political domination of the bourgeoisie.", "在另一些地方组成君主国中的纳税的第三等级；后来": "In other places, forming the taxed third estate in monarchies; later.", "从封建社会的灭亡中产生出来的现代资产阶级社会并没有消灭阶级对立": "The modern bourgeois society that emerged from the demise of feudal society did not eliminate class antagonism.", "在中世纪": "In the Middle Ages.", "并且是同这种统治作斗争的文字表现": "And it is a written expression of the struggle against this domination.", "用一种没有良心的贸易自由代替了无数特许的和自力挣得的自由": "Replaced countless privileges and self-earned freedoms with a heartless freedom of trade.", "当然首先必须对所有权和资产阶级生产关系实行强制性的干涉": "Of course, there must first be compulsory interference with ownership and bourgeois production relations.", "仿佛用法术从地下呼唤出来的大量人口": "As if a large population were summoned from underground by magic.", "叶被毛": "<PERSON>.", "资产阶级赖以生产和占有产品的基础本身也就从它的脚下被挖掉了": "The very foundation upon which the bourgeoisie relies to produce and possess products has been undermined from beneath their feet.", "共产党人的最近目的是和其他一切无产阶级政党的最近目的一样的": "The recent goal of communists is the same as that of all other proletarian parties.", "甚至生活最优裕的成员也包括在内": "Even the most affluent members are included.", "他们还能进行的只是文字斗争": "All they can engage in is a struggle of words.", "并且向人民群众大肆宣扬": "And they widely promote to the masses.", "由此可见": "It can be seen that.", "这个阶级还在新兴的资产阶级身旁勉强生存着": "This class is barely surviving alongside the emerging bourgeoisie.", "他们不仅仅攻击资产阶级的生产关系": "They not only attack the production relations of the bourgeoisie.", "而资产阶级却把消灭这种关系说成是消灭个性和自由！说对了": "But the bourgeoisie claims that the abolition of these relations is the abolition of individuality and freedom! That's right.", "而且养起来也不难": "And it's not difficult to sustain.", "需求总是在增加": "Demand is always increasing.", "他们毫不掩饰自己的批评的反动性质": "They do not hide the reactionary nature of their criticism.", "工人开始成立反对资产者的同盟；他们联合起来保卫自己的工资": "Workers begin to form alliances against the capitalists; they unite to defend their wages.", "都有是一样的": "They are all the same.", "不仅如此": "Moreover.", "不也是由你们进行教育时所处的那种社会关系决定的吗": "Isn't it also determined by the social relations you are in while educating?", "资产阶级使农村屈服于城市的统治": "The bourgeoisie subjugates the countryside to the rule of the city.", "还证明了什么呢": "What else does it prove?", "私有财产对十分之九的成员来说已经被消灭了；这种私有制这所以存在": "Private property has been abolished for nine-tenths of the members; this form of private ownership exists because.", "那些站在无产阶级方面反对资产阶级的著作家": "Those authors who stand on the side of the proletariat against the bourgeoisie.", "在法国的1830年七月革命和英国的改革运动 中": "During the July Revolution of 1830 in France and the reform movement in England.", "他们的计划主要是代表工人阶级这一受苦最深的阶级的利益": "Their plans mainly represent the interests of the working class, the most suffering class.", "叶片外形多有变化有短圆形、厚厚的方形等不同叶形；": "The shape of the leaves varies, with short round shapes, thick square shapes, and other different leaf shapes.", "却是过去的一切工业阶级生存的首要条件": "It was the primary condition for the survival of all past industrial classes.", "在德国的条件下": "Under the conditions in Germany.", "教皇和沙皇、梅特涅和基佐、法国的激进派和德国的警察": "The <PERSON> and the Tsar, <PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON>, the radicals in France and the police in Germany.", "总而言之": "In summary.", "这样": "Thus.", "资产阶级的婚姻实际上是公妻制": "The marriage of the bourgeoisie is essentially a form of communal wife system.", "Json解析异常": "Json parsing exception.", "而无产者的被迫独居和公开的卖淫则是它的补充": "And the forced solitude and public prostitution of the proletariat are its supplements.", "添加联系人": "Add contact.", "却有一个特点": "But it has a characteristic.", "可见": "Visible", "或者是企图恢复旧的生产资料和交换手段": "Or attempting to restore old means of production and exchange", "这些不得不把自己零星出卖的工人": "These workers who have to sell themselves sporadically", "他们不提出任何特殊的原则": "They do not propose any special principles", "是同历史的发展成反比的": "Is inversely proportional to the development of history", "解放的历史条件要由幻想的条件来代替": "The historical conditions for liberation must be replaced by imaginary conditions", "随着贸易自由的实现和世界市场的建立": "With the realization of trade freedom and the establishment of the world market", "德国著作家对世俗的法国文献采取相反的作法": "German authors take the opposite approach to secular French literature", "而是越来越降到本阶级的生存条件以下": "But increasingly fall below the living conditions of this class", "其余的阶级都随着大工业的发展而日趋没落和灭亡": "The remaining classes are declining and perishing with the development of large-scale industry", "但是它由于完全不能理解现代历史的进程而总是令人感到可笑": "But it is always laughable because it completely fails to understand the process of modern history", "他们克服了“法国人的片面性”": "They have overcome the 'one-sidedness of the French'", "净化空气": "Purify the air", "资产者唯恐失去的那种教育": "The kind of education that capitalists fear losing", "总之": "In summary", "碧光环是番杏科碧光玉属": "The jade ring is from the genus Crassula in the Crassulaceae family", "10、对所有儿童实行公共的和免费的教育": "10. Implement public and free education for all children", "而只具有纯粹文献的形式": "But only has the form of pure literature", "他是北境的继承人": "He is the heir of the North", "于是就哈哈大笑": "And then laughed out loud", "那种财产用不着我们去消灭": "That kind of property does not need to be eliminated by us", "即剥削雇佣劳动的财产": "That is, the property that exploits wage labor", "亦可栽植于室内以吸收甲醛等物质": "It can also be planted indoors to absorb formaldehyde and other substances", "但是不要那些使这个社会革命化和瓦解的因素": "But do not want those factors that revolutionize and dissolve this society", "这一方面是由于资本的积聚": "This is partly due to the accumulation of capital", "从大工业和世界市场建立的时候起": "Since the establishment of large-scale industry and the world market", "因福娘的叶形叶色较美": "Because the leaf shape and color of the fortune daughter are more beautiful", "随着工业生产以及与之相适应的生活条件的趋于一致": "With the alignment of industrial production and the corresponding living conditions", "不过是一般“实践理性”的要求": "It is merely a general requirement of 'practical reason'", "由无产阶级夺取政权": "The proletariat seizes power", "银波锦属的多年生肉质草本植物": "Perennial succulent herbaceous plants of the genus Silver Wave", "给新兴的资产阶级开辟了新天地": "Opened up a new world for the emerging bourgeoisie", "碧光环原产于南非": "The blue halo originates from South Africa", "他们更甘心于被人收买": "They are more willing to be bought off", "资产阶级的这种发展的每一个阶段": "Every stage of this development of the bourgeoisie", "看不到它所特有的任何政治运动": "Cannot see any political movement unique to it", "分工越细致": "The more detailed the division of labor", "中世纪的城关市民和小农等级是现代资产阶级的前身": "The medieval town burghers and small peasant class are the predecessors of the modern bourgeoisie", "他们在一些地方也被无产阶级革命卷到运动里来": "They have also been swept into the movement by the proletarian revolution in some places", "在这种著作从法国搬到德国的时候": "When this kind of work moved from France to Germany", "这样说来": "That being said", "让统治阶级在共产主义革命面前发抖吧": "Let the ruling class tremble before the communist revolution", "经济学家、博爱主义者、人道主义者、劳动阶级状况改善派、慈善事业组织者、动物保护协会会员、戒酒协会发起人以及形形色色的小改良家": "Economists, philanthropists, humanitarians, advocates for the improvement of the working class's conditions, charity organizers, animal protection association members, temperance movement initiators, and various minor reformers", "各个世纪的社会意识": "Social consciousness of various centuries", "“真正的”社会主义就得到了一个好机会": "The 'real' socialism has gotten a good opportunity", "资产阶级的社会主义把这种安慰人心的观念制成半套或整套的体系": "The bourgeois socialism has turned this comforting idea into a half or full set of systems", "资本是集体的产物": "Capital is a collective product", "比过去一切世代创造的全部生产力还要多": "More than all the productive forces created by past generations", "即袖珍版的新耶路撒冷": "A pocket-sized new Jerusalem", "我的养母": "My adoptive mother", "在实践方面": "In practical terms", "但是在运动进程中它们会越出本身": "But in the course of the movement, they will exceed themselves", "成立产业军": "Establish an industrial army", "而且有很大一部分已经造成的生产力被毁灭掉": "And a large part of the productive forces that have already been created has been destroyed", "他们违背自己的那一套冠冕堂皇的言词": "They contradict their own grandiloquent words", "现代资产阶级本身是一个长期发展过程的产物": "The modern bourgeoisie itself is a product of a long developmental process", "说我们消灭个人挣得的、自己劳动得来的财产": "Saying we abolish property earned by individuals through their own labor", "这种文献倡导普遍的禁欲主义和粗陋的平均主义": "This kind of literature advocates for universal asceticism and crude egalitarianism", "为了激起同情": "In order to arouse sympathy", "使反动派大为惋惜的是": "What makes the reactionaries greatly regret is", "它首先生产的是它自身的掘墓人": "What it first produces is its own gravediggers", "无论在美国或德国": "Whether in America or Germany", "几十年来的工业和商业的历史": "The history of industry and commerce over the decades", "增加自己的资本": "Increase their own capital", "思想的历史除了证明精神生产随着物质生产的改造而改造": "The history of thought not only proves that spiritual production is transformed along with the transformation of material production.", "不满足任何一种已知的密钥格式": "Does not satisfy any known key format.", "检测到长输入": "Long input detected.", "即小工业家、小商人和小食利者": "That is, small industrialists, small merchants, and petty usurers.", "二、无产者和共产党人": "2. The proletariat and communists.", "流氓无产阶级是旧社会最下层中消极的腐化的部分": "The lumpenproletariat is the passive and corrupt part of the lowest layer of the old society.", "它给这些小市民的每一种丑行都加上奥秘的、高尚的、社会主义的意义": "It adds a mysterious, noble, and socialist meaning to every vice of these petty bourgeois.", "形成了一个新的小资产阶级": "A new petty bourgeoisie has formed.", "即使在文字方面也不可能重弹复辟时期的老调了": "Even in terms of language, it is impossible to replay the old tunes of the restoration period.", "使无产阶级形成为阶级": "It shapes the proletariat into a class.", "社会所拥有的生产力已经不能再促进资产阶级文明和资产阶级所有制关系的发展；相反": "The productive forces possessed by society can no longer promote the development of bourgeois civilization and bourgeois property relations; on the contrary.", "就是把新的法国的思想同他们的旧的哲学信仰调和起来": "It is to reconcile the new French thought with their old philosophical beliefs.", "我们还是把资产阶级对共产主义的种种责难撇开吧": "Let us set aside the various accusations of the bourgeoisie against communism.", "一切社会状况不停的动荡": "All social conditions are in constant turmoil.", "社会上一部分人对另一部分人的剥削却是过去各个世纪所共有的事实": "The exploitation of one part of society by another has been a common fact throughout the centuries.", "民族内部的阶级对立一消失": "Once the class antagonism within the nation disappears.", "有人反驳说": "Some people argue that.", "一些基础工具": "Some basic tools.", "诚然": "Indeed.", "换句话说": "In other words.", "就是写一些抨击现代资产阶级社会的作品": "It is to write some works that criticize modern bourgeois society.", "现代大工业代替了工场手工业；工业中的百万富翁": "Modern large-scale industry has replaced workshop handicrafts; millionaires in industry.", "所不同的只是他们更加系统地卖弄学问": "The difference is that they flaunt their knowledge more systematically.", "她是野人中的一员": "She is one of the savages.", "这些体系的发明家看到了阶级的对立": "The inventors of these systems saw the class antagonism.", "以便为可能发生的反抗准备食品": "In order to prepare food for possible resistance.", "我们在前面已经叙述过了": "We have already described this earlier.", "它把宗教虔诚、骑士热忱、小市民伤感这些情感的神圣发作": "It sanctifies the emotional outbursts of religious piety, chivalrous enthusiasm, and petty bourgeois sentimentality.", "只够勉强维持他的生命的再生产": "Barely sufficient to sustain his life reproduction.", "形态奇特": "The form is peculiar.", "公妻制无需共产党人来实行": "The system of public wives does not require communists to implement.", "只有当阶级对立完全消失的时候才会完全消失": "It will only completely disappear when class antagonism has completely vanished.", "始终处于相互对立的地位": "Always in a position of mutual opposition", "就是从他们的哲学观点出发去掌握法国的思想": "It is to grasp French thought from their philosophical perspective", "自由主义运动": "Liberal movement", "我们已经看到": "We have already seen", "随着大工业的发展": "With the development of large industry", "它只剥夺利用这种占有去奴役他人劳动的权力": "It only deprives the power to use this possession to enslave the labor of others", "半是过去的回音": "Half is an echo of the past", "只要资产阶级采取革命的行动": "As long as the bourgeoisie takes revolutionary action", "无产者是没有财产的；他们和妻子儿女的关系同资产阶级的家庭关系再没有任何共同之处了；现代的工业劳动": "The proletarians have no property; their relationship with their wives and children has nothing in common with the bourgeois family relationship; modern industrial labor", "绝大多数人来说是把人训练成机器": "For the vast majority of people, it trains humans to become machines", "在资产阶级看来": "In the eyes of the bourgeoisie", "人们的观念、观点和概念": "People's ideas, viewpoints, and concepts", "无产阶级就是这样从居民的所有阶级中得到补充的": "The proletariat is thus supplemented from all classes of residents", "手的操作所要求的技巧和气力越少": "The less skill and strength required for manual operation", "我们不谈在现代一切大革命中表达过无产阶级要求的文献": "We do not talk about the literature that has expressed the demands of the proletariat in all modern revolutions", "这种责难归结为什么呢": "What does this criticism boil down to?", "封建主说": "The feudal lord says", "而且几乎在每一个阶级内部又有一些特殊的阶层": "And there are almost some special strata within each class", "资产阶级自己就把自己的教育因素即反对自身的武器给予了无产阶级": "The bourgeoisie itself has given the proletariat its educational factors, that is, the weapons against itself", "雇佣劳动的平均价格是最低限度的工资": "The average price of wage labor is the minimum wage", "即生产过剩的瘟疫": "That is the plague of overproduction", "资产阶级中的一部分人想要消除社会的弊病": "Some people in the bourgeoisie want to eliminate the ills of society", "一支一支产业大军的首领": "The leader of a battalion of industrial troops", "所以": "Therefore", "而是维护他们将来的利益": "But to protect their future interests", "共产党人支持那个把土地革命当作民族解放的条件的政党": "Communists support the party that regards land reform as a condition for national liberation", "性别和年龄的差别再没有什么社会意义了": "The differences in gender and age no longer have any social significance", "资产阶级": "Bourgeoisie", "——这才是资产阶级的社会主义唯一认真说出的最后的话": "— This is the only serious last word of the bourgeois socialism", "要使教育摆脱统治阶级的影响": "To free education from the influence of the ruling class", "它揭穿了经济学家的虚伪的粉饰": "It exposes the hypocritical embellishments of economists", "把国家变成纯粹的生产管理机构": "Transform the state into a purely production management institution", "他们都强调所有制问题是运动的基本问题": "They all emphasize that the issue of ownership is the fundamental issue of the movement.", "至今的一切社会的历史都是在阶级对立中运动的": "The history of all societies to date has been a movement in class opposition.", "以前那种封建的或行会的工业经营方式已经不能满足随着新市场的出现而增加的需求了": "The previous feudal or guild industrial management methods can no longer meet the increasing demands arising from new markets.", "只要指出在周期性的重复中越来越危及整个资产阶级社会生存的商业危机就够了": "It is enough to point out the commercial crises that increasingly threaten the survival of the entire bourgeois society in periodic repetitions.", "机器越推广": "The more machines are promoted.", "为了这个目的": "For this purpose.", "这一思潮在它以后的发展中变成了一种怯懦的悲叹": "This ideology later developed into a timid lament.", "如果用户给出了联系人": "If the user provides a contact.", "的确": "Indeed.", "无产者的劳动": "The labor of the proletariat.", "人们终于不得不用冷静的眼光来看他们的生活地位、他们的相互关系": "People finally have to look at their living conditions and their relationships with a calm perspective.", "6、把全部运输业集中在国家的手里": "6. Centralize all transportation industries in the hands of the state.", "各民族的精神产品成了公共的财产": "The spiritual products of various nations have become public property.", "一切新形成的关系等不到固定下来就陈旧了": "All newly formed relationships become outdated before they can be established.", "作为长期参考": "As a long-term reference.", "还不是他们自己联合的结果": "It is not yet the result of their own union.", "它使未开化和半开化的国家从属于文明的国家": "It makes uncivilized and semi-civilized countries subordinate to civilized nations.", "到处建立联系": "Establish connections everywhere.", "一旦没有资本": "Once there is no capital.", "今后的世界历史不过是宣传和实施他们的社会计划": "The future world history is nothing but the promotion and implementation of their social plans.", "封建社会正在同当时革命的资产阶级进行殊死的斗争": "Feudal society is engaged in a life-and-death struggle with the revolutionary bourgeoisie of the time.", "肉质叶肥厚": "The fleshy leaves are thick.", "把地租用于国家支出": "Use land rent for state expenditures.", "另一方面夺取新的市场": "On the other hand, seize new markets.", "1、剥夺地产": "1. Expropriate land.", "他们甚至觉察到": "They even realize that.", "在共产主义社会里是现在支配过去": "In a communist society, the present dominates the past.", "每一个国家的无产阶级当然首先应该打倒本国的资产阶级": "The proletariat of each country should certainly first overthrow the bourgeoisie of their own country.", "在它看来": "In its view.", "这就是资产阶级时代不同于过去一切时代的地方": "This is where the bourgeois era differs from all past eras.", "现今在德国流行的一切所谓社会主义和共产主义的著作": "All so-called socialist and communist writings currently popular in Germany.", "他们参与对工人阶级采取的一切暴力措施": "They participate in all violent measures taken against the working class.", "这样就把无产阶级卷进了政治运动": "This has drawn the proletariat into political movements.", "如果说他们是革命的": "If they are said to be revolutionary.", "并且企图通过一些小型的、当然不会成功的试验": "And they attempt to conduct some small-scale, of course unsuccessful experiments.", "他们甚至建立了经常性的团体": "They even established regular organizations.", "从而对全部社会关系不断地进行革命": "Thus continuously revolutionizing all social relations.", "就能把许多性质相同的地方性的斗争汇合成全国性的斗争": "It can unite many similar local struggles into a national struggle.", "——过去哪一个世纪料想到在社会劳动里蕴藏有这样的生产力呢": "—Which century in the past could have anticipated such productive forces hidden in social labor?", "创立小伊加利亚": "Establishing a small Utopia.", "资产阶级在它的不到一百年的阶级统治中所创造的生产力": "The productive forces created by the bourgeoisie in its less than a hundred years of class rule.", "屈尊拾取金苹果": "Deigning to pick up golden apples.", "封建社会的生产和交换在其中进行的关系": "The relations of production and exchange in feudal society.", "让我们来看看这种对立的两个方面吧": "Let's take a look at these two opposing aspects.", "资产者彼此间日益加剧的竞争以及由此引起的商业危机": "The increasingly intense competition among capitalists and the resulting commercial crises.", "较喜光照": "Preferably well-lit.", "现代的资产阶级私有制是建立在阶级对立上面、建立在一些人对另一些人的剥削上面的产品生产和占有的最后而又完备的表现": "Modern bourgeois private property is the final and complete manifestation of production and ownership based on class opposition and the exploitation of some by others.", "他们不代表真实的要求": "They do not represent real demands.", "我们的时代": "Our era.", "他是个天真无邪的小孩": "He is an innocent child.", "叶色灰绿": "The leaves are gray-green.", "力图恢复已经失去的中世纪工人的地位": "Striving to restore the lost status of medieval workers.", "在资产阶级的统治下有一个将把整个旧社会制度炸毁的阶级发展起来": "Under the rule of the bourgeoisie, a class is developing that will blow up the entire old social system.", "无产阶级用暴力推翻资产阶级而建立自己的统治": "The proletariat overthrows the bourgeoisie with violence and establishes its own rule.", "这件光彩夺目的外衣只是使他们的货物在这些顾客中间增加销路罢了": "This splendid exterior only serves to increase the market for their goods among these customers.", "我们承认这种罪状": "We acknowledge this charge.", "自由民和奴隶、贵族和平民、领主和农奴、行会师傅和帮工": "Freemen and slaves, nobles and commoners, lords and serfs, guild masters and apprentices.", "挺起胸来": "Stand tall.", "这是要计算嵌入的文本": "This is to calculate the embedded text.", "这究竟是怎样的一种办法呢": "What kind of method is this?", "也就消失了": "Has also disappeared.", "更坚固": "Stronger.", "要求无产阶级援助": "Demand assistance from the proletariat.", "在旧社会内部已经形成了新社会的因素": "Factors of a new society have already formed within the old society.", "贪婪地抓住了这种文献": "Greedily seized this literature", "正像过去贵族中有一部分人转到资产阶级方面一样": "Just as some nobles in the past shifted to the bourgeoisie", "而只存在于云雾弥漫的哲学幻想的太空": "And only exists in the space of philosophical fantasies shrouded in mist", "他们不仅仅是资产阶级的、资产阶级国家的奴隶": "They are not merely slaves of the bourgeoisie and the bourgeois state", "它是等级君主国或专制君主国中同贵族抗衡的势力": "It is a force that counters the nobility in hierarchical monarchies or despotic states", "如自由、正义等等": "Such as freedom, justice, and so on", "这种专制制度越是公开地把营利宣布为自己的最终目的": "The more this autocratic system openly declares profit as its ultimate goal", "反而会失去一切": "The more it will lose everything", "无产阶级在普遍激动的时代、在推翻封建社会的时期直接实现自己阶级利益的最初尝试": "The proletariat's initial attempt to directly realize its class interests during a time of general agitation and in the period of overthrowing feudal society", "乙": "B", "这种文献被搬到德国的时候": "When this literature was moved to Germany", "珊莎·史塔克": "<PERSON><PERSON>", "现在渐渐失去了它的自炫博学的天真": "Has gradually lost its naive self-congratulatory erudition", "德国著作家的唯一工作": "The sole work of German authors", "正如阶级的所有制的终止在资产者看来是生产本身的终止一样": "Just as the termination of class ownership appears to the capitalists as the termination of production itself", "这种资产阶级的社会主义甚至被制成一些完整的体系": "This bourgeois socialism has even been formed into some complete systems", "所谓自由就是自由贸易": "So-called freedom is free trade", "他们甚至是反动的": "They are even reactionary", "花期7-9月": "Flowering period is July to September", "清空向量数据库": "Clear the vector database", "农奴曾经在农奴制度下挣扎到公社成员的地位": "<PERSON><PERSON> once struggled under serfdom to attain the status of commune members", "花瓣呈红色": "The petals are red", "他们斗争的真正成果并不是直接取得的成功": "The true outcome of their struggle was not a direct success", "共产党人到处都努力争取全世界民主政党之间的团结和协调": "Communists everywhere strive for unity and coordination among democratic parties worldwide", "它在这两种场合都是反动的": "It is reactionary in both cases", "如果用户希望移除某个联系人": "If the user wishes to remove a contact", "现在已经结合为一个拥有统一的政府、统一的法律、统一的民族阶级利益和统一的关税的统一的民族": "Now combined into a unified nation with a unified government, unified law, unified national class interests, and unified tariffs", "是以极端怠惰作为相应补充的": "Is correspondingly supplemented by extreme laziness", "而宗教、道德、哲学、政治和法在这种变化中却始终保存着": "While religion, morality, philosophy, politics, and law have always preserved themselves in this change", "它把医生、律师、教士、诗人和学者变成了它出钱招雇的雇佣劳动者": "It turns doctors, lawyers, clergymen, poets, and scholars into hired laborers it pays to employ", "8、实行普遍劳动义务制": "8. Implement universal labor obligation system", "是它用来摧毁一切万里长城、征服野蛮人最顽强的仇外心理的重炮": "It is the heavy artillery it uses to destroy all barriers and conquer the most stubborn xenophobia of the barbarians", "从这一事实中可以得出两个结论": "Two conclusions can be drawn from this fact", "表覆白粉": "Powdered white coating", "而是工人的越来越扩大的联合": "But rather the increasingly expanding union of workers", "贵族们把无产阶级的乞食袋当作旗帜来挥舞": "The nobility wave the proletariat's begging bag as a flag", "活的劳动只是增殖已经积累起来的劳动的一种手段": "Living labor is merely a means of augmenting already accumulated labor", "现代的资本压迫": "Modern capital oppression", "不代表无产者的利益": "Does not represent the interests of the proletariat", "即发动过1846年克拉科夫起义的政党": "The party that instigated the Kraków uprising of 1846", "进行不断的、有时隐蔽有时公开的斗争": "Engaging in continuous struggles, sometimes covertly and sometimes openly", "促使城乡对立逐步消灭": "Promoting the gradual elimination of urban-rural opposition", "不断地由于工人的自相竞争而受到破坏": "Constantly being undermined by the competition among workers", "极具观赏价值": "Highly ornamental", "这些措施在不同的国家里当然会是不同的": "These measures will certainly differ in different countries", "封建的所有制关系": "Feudal ownership relations", "雄蕊呈正方形；蓇葖果的喙反曲；种子平滑；花期9月；果期10月": "Stamens are square; the beak of the capsule is curved; seeds are smooth; flowering period is September; fruiting period is October", "无产者没有什么自己的东西必须加以保护": "The proletariat has nothing of its own that must be protected", "就会承认这种体系是最美好的社会的最美好的计划": "Will acknowledge this system as the best plan for the best society", "到处开发": "Develop everywhere", "他们也给无产阶级带来了大量的教育因素": "They also brought a wealth of educational factors to the proletariat", "所以共产主义是同至今的全部历史发展相矛盾的": "Thus communism contradicts the entire historical development to date", "似乎他们已经不关心自身的利益": "It seems they no longer care about their own interests", "详见": "See details", "但是要抛弃他们关于这个社会的可恶的观念": "But must abandon their abhorrent views about this society", "西斯蒙第不仅对法国而且对英国来说都是这类著作家的首领": "<PERSON><PERSON><PERSON> is the leader of such authors not only for France but also for Britain", "又似玉石": "Also resembles jade", "5、通过拥有国家资本和独享垄断权的国家银行": "5. By owning state capital and monopolizing the state bank", "旧欧洲的一切势力": "All forces of old Europe", "而这一切前提当时在德国正是尚待争取的": "And all these premises were yet to be fought for in Germany at that time", "自由贸易！为了工人阶级的利益；保护关税！为了工人阶级的利益；单身牢房！为了工人阶级的利益": "Free trade! For the benefit of the working class; protective tariffs! For the benefit of the working class; solitary confinement! For the benefit of the working class", "是景天科石莲属": "Is the Crassulaceae family of stone lotus", "因此": "Therefore", "共产党人是各国工人政党中最坚决的、始终起推动作用的部分；在理论方面": "Communists are the most resolute and consistently driving force within the workers' parties of various countries; in theoretical terms", "以及旧风尚、旧家庭关系和旧民族性的解体": "The disintegration of old customs, old family relationships, and old national identities", "无产阶级的统治将使它们更快地消失": "The rule of the proletariat will make them disappear more quickly", "是景天科": "Is the Crassulaceae family", "这种社会主义的另一种不够系统、但是比较实际的形式": "Another form of socialism that is not systematic enough but more practical", "东印度和中国的市场、美洲的殖民化、对殖民地的贸易、交换手段和一般商品的增加": "The markets of East India and China, the colonization of the Americas, trade with the colonies, means of exchange, and the increase of general commodities", "并且宣布自己是不偏不倚地超乎任何阶级斗争之上的": "And declare themselves to be above any class struggle in an impartial manner", "在政治实践中": "In political practice", "消灭家庭！连极端的激进派也对共产党人的这种可耻的意图表示愤慨": "Abolish the family! Even the most extreme radicals express outrage at the communists' shameful intention", "一谈到资产阶级所有制你们就再也不能理解了": "When it comes to bourgeois ownership, you can no longer understand", "二歧聚伞花序": "Dichasial umbel", "资产阶级不仅锻造了置自身于死地的武器；它还产生了将要运用这种武器的人——现代的工人": "The bourgeoisie not only forged the weapons that would lead to their own destruction; it also produced the people who would wield these weapons—the modern workers", "要做到这一点": "To achieve this", "他在生产中不仅占有一种纯粹个人的地位": "He occupies not only a purely individual position in production", "这个曾经仿佛用法术创造了如此庞大的生产资料和交换手段的现代资产阶级社会": "This modern bourgeois society that once seemed to have magically created such vast means of production and exchange", "而一切阶级斗争都是政治斗争": "And all class struggles are political struggles", "在他们的统治下并没有出现过现代的无产阶级": "The modern proletariat did not emerge under their rule", "因为同17世纪的英国和18世纪的法国相比": "Because compared to 17th century England and 18th century France", "晶莹剔透；两片圆柱形的叶子": "Crystal clear; two cylindrical leaves", "调用SocialAdvice生成一些社交建议": "Invoke SocialAdvice to generate some social suggestions", "无产阶级不仅人数增加了": "The proletariat has not only increased in number", "资产阶级用来推翻封建制度的武器": "The weapons the bourgeoisie used to overthrow the feudal system", "农业中的宗法经济": "Patriarchal economy in agriculture", "是一个阶级用以压迫另一个阶级的有组织的暴力": "Is organized violence used by one class to oppress another class", "不如说是因为它产生了革命的无产阶级": "Rather, it is because it produced a revolutionary proletariat", "资产阶级在它已经取得了统治的地方把一切封建的、宗法的和田园般的关系都破坏了": "The bourgeoisie has destroyed all feudal, patriarchal, and idyllic relations in the places where it has already established its rule", "把一切民族甚至最野蛮的民族都卷到文明中来了": "Has drawn all nations, even the most barbaric, into civilization", "消灭阶级本身的存在条件": "Abolish the very conditions for the existence of classes", "艾莉亚·史塔克": "<PERSON><PERSON>", "“宗教的、道德的、哲学的、政治的、法的观念等等在历史发展的进程中固然是不断改变的": "The concepts of religion, morality, philosophy, politics, law, etc., certainly change continuously in the process of historical development", "而每一次斗争的结局都是整个社会受到革命改造或者斗争的各阶级同归于尽": "And the outcome of each struggle is either a revolutionary transformation of society or the mutual destruction of the contending classes", "共产主义已经被欧洲的一切势力公认为一种势力；": "Communism has been recognized by all forces in Europe as a power;", "在所有这些运动中": "In all these movements", "轮船的行驶": "The navigation of the ship", "因而对工人也失去了任何吸引力": "Thus lost any appeal to workers", "他们就不得不呼吁资产阶级发善心和慷慨解囊": "They had to appeal to the bourgeoisie for kindness and generosity", "个性被消灭了": "Individuality has been eliminated", "不外是资产者、资产阶级私有者": "Nothing more than capitalists, bourgeois private owners", "正在召回知识": "Recalling knowledge", "茎和花无毛": "Stems and flowers are hairless", "创办单个的法伦斯泰尔": "Establishing a single Falun Steel", "这样就产生了封建的社会主义": "This has produced feudal socialism", "才能运动起来": "Can move into action", "人的活动能够取得什么样的成就": "What achievements human activities can attain", "所以具有一定的观赏价值": "Therefore has certain aesthetic value", "你们共产党人是要实行公妻制的啊": "You communists are going to implement communal marriage", "反对婚姻": "Opposing marriage", "无产阶级的运动是绝大多数人的、为绝大多数人谋利益的独立的运动": "The movement of the proletariat is an independent movement for the interests of the vast majority of people", "交互对生": "Mutual interaction for life", "永远的不安定和变动": "Eternal instability and change", "随着工业、商业、航海业和铁路的扩展": "With the expansion of industry, commerce, shipping, and railways", "无产者在这个革命中失去的只是锁链": "The proletariat loses only its chains in this revolution", "虹之玉锦": "Rainbow jade brocade", "更有力": "More powerful", "观赏价值很高": "High aesthetic value", "因而丝毫不会改变资本和雇佣劳动的关系": "Thus will not change the relationship between capital and wage labor in the slightest", "碧光环的繁殖方式有扦插和播种": "The propagation methods of the green halo include cuttings and sowing", "除了冷酷无情的“现金交易”": "Except for the cold and ruthless 'cash transaction'", "两者都要随着资本的消失而消失": "Both will disappear with the disappearance of capital", "社会主义的资产者愿意要现代社会的生存条件": "Socialist capitalists are willing to accept the conditions for survival in modern society", "巴贝夫等人的著作": "The works of <PERSON><PERSON> and others", "在当前同资产阶级对立的一切阶级中": "Among all classes currently opposed to the bourgeoisie", "资产阶级的生产关系和交换关系": "The production and exchange relations of the bourgeoisie", "小花黄色": "Small yellow flowers", "都不值得详细讨论了": "Are not worth discussing in detail", "共产党就同它一起去反对专制君主制、封建土地所有制和小市民的反动性": "The Communist Party opposes autocratic monarchy, feudal land ownership, and the reactionary nature of the petty bourgeoisie.", "在18世纪的德国哲学家看来": "In the view of 18th-century German philosophers.", "是说我们要消灭那种以社会上的绝大多数人没有财产为必要条件的所有制": "It means we must eliminate the ownership that requires the vast majority of people in society to be propertyless.", "忌强光暴晒": "Avoid strong sunlight exposure.", "——资产阶级用什么办法来克服这种危机呢": "— How does the bourgeoisie overcome this crisis?", "它使人和人之间除了赤裸裸的利害关系": "It reduces relationships between people to nothing but bare self-interest.", "他们的子女越是由于这种发展而被变成单纯的商品和劳动工具": "Their children are increasingly turned into mere commodities and tools of labor due to this development.", "叶插的繁殖成功率不高": "The success rate of propagation through leaf cuttings is not high.", "碧光环小巧饱满、圆滚滚的样子很可爱": "The small, plump, and round appearance of the jade ring is very cute.", "他们称之为“行动的哲学”、”真正的社会主义”、“德国的社会主义科学”、“社会主义的哲学论证”": "They call it 'philosophy of action', 'true socialism', 'German socialist science', 'philosophical argument for socialism'.", "从这个意义上说": "In this sense.", "共产党人同其他无产阶级政党不同的地方只是": "The difference between communists and other proletarian parties is only.", "因而使很大一部分居民脱离了农村生活的愚昧状态": "Thus, it has led a large portion of the population to escape the ignorance of rural life.", "但是不要无产阶级": "But do not want the proletariat.", "你们说的是现代的资产阶级的私有财产吧": "You are referring to the private property of the modern bourgeoisie, right?", "工人仅仅为增殖资本而活着": "Workers live only to increase capital.", "生长期要见干见湿": "During the growth period, it needs to be dry and wet.", "是在封建社会里造成的": "It was created in feudal society.", "sk-proj-xx-xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx_xxxxxxxxxxxx_xxxxxxxxxxxxxxxxxx-xxx啊xxxxxxx": "sk-proj-xx-xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx_xxxxxxxxxxxx_xxxxxxxxxxxxxxxxxx-xxx ah xxxxxxx.", "尽管形形色色、千差万别": "Despite the various forms and differences.", "它宣布德意志民族是模范的民族": "It declares the German nation to be a model nation.", "他们责备资产阶级": "They blame the bourgeoisie.", "工人变成赤贫者": "Workers become destitute.", "多用枝插": "Use more branch cuttings.", "在他们心目中就是纯粹的意志、本来的意志、真正人的意志的规律": "In their minds, it is the law of pure will, original will, and the will of true humanity.", "——所有这些主张都只是表明要消灭阶级对立": "— All these claims merely indicate the need to eliminate class opposition.", "它差不多是一向就有的": "It has almost always existed.", "也是一种商品": "It is also a commodity.", "茎绿色": "The stem is green.", "他们还总是梦想用试验的办法来实现自己的社会空想": "They always dream of realizing their social utopia through experimental methods.", "这种占有并不会留下任何剩余的东西使人们有可能支配别人的劳动": "This kind of possession does not leave any surplus that allows people to control the labor of others.", "中世纪的市民靠乡间小道需要几百年才能达到的联合": "The union that medieval citizens could only achieve after hundreds of years through country paths.", "同时又是空想的": "At the same time, it is also utopian", "省略": "Omission", "无产者组织成为阶级": "The proletariat organizes into a class", "所以这些发明家也不可能看到无产阶级解放的物质条件": "Therefore, these inventors cannot foresee the material conditions for the liberation of the proletariat", "使工资几乎到处都降到同样低的水平": "Causing wages to drop to the same low level almost everywhere", "法国的社会主义和共产主义的文献就这样被完全阉割了": "The literature of socialism and communism in France has thus been completely emasculated", "在波兰人中间": "Among the Poles", "资产阶级的灭亡和无产阶级的胜利是同样不可避免的": "The demise of the bourgeoisie and the victory of the proletariat are equally inevitable", "人们至多只能责备共产党人": "People can at most blame the communists", "也就不再有雇佣劳动了": "There will no longer be wage labor", "总是不仅有很大一部分制成的产品被毁灭掉": "There is always a large portion of manufactured products that are destroyed", "这并不是把个人财产变为社会财产": "This does not mean turning personal property into social property", "从而恢复旧的所有制关系和旧的社会": "Thus restoring the old property relations and the old society", "我们决不打算消灭这种供直接生命再生产用的劳动产品的个人占有": "We do not intend to abolish personal ownership of labor products that are used for direct reproduction of life", "再容纳不了它本身所造成的财富了": "It can no longer accommodate the wealth it itself has created", "即掌握着未来的阶级": "That is, the class that holds the future", "几乎只限于维持工人生活和延续工人后代所必需的生活资料": "Almost limited to the means of living necessary to sustain workers' lives and the continuation of workers' descendants", "她梦想成为一位淑女": "She dreams of becoming a lady", "成功添加": "Successfully added", "因叶子有棱有角": "Because the leaves are jagged and angular", "由此可以明显地看出": "It can be clearly seen from this", "是资本的形成和增殖；资本的条件是雇佣劳动": "It is the formation and proliferation of capital; the condition of capital is wage labor", "自由买卖也就会消失": "Free trade will also disappear", "这种人不属于任何阶级": "Such people do not belong to any class", "就越失去任何实践意义和任何理论根据": "The more it loses any practical significance and any theoretical basis", "你们是承认": "You acknowledge", "他是临冬城的公爵": "He is the Duke of Winterfell", "既然“真正的”社会主义就这样成了这些政府对付德国资产阶级的武器": "Since 'true' socialism has thus become a weapon for these governments against the German bourgeoisie", "而是来自极其遥远的地区的原料；它们的产品不仅供本国消费": "But rather raw materials from extremely distant regions; their products are not only for domestic consumption", "以及占统治地位的社会本身中的瓦解因素的作用": "As well as the role of disintegrating factors within the dominant society itself", "是说我们要消灭你们的那种所有制": "That is to say, we want to abolish your type of property", "前面我们已经看到": "We have already seen earlier", "封建贵族并不是被资产阶级所推翻的、其生活条件在现代资产阶级社会里日益恶化和消失的唯一阶级": "The feudal nobility is not the only class that has been overthrown by the bourgeoisie; its living conditions have increasingly deteriorated and disappeared in modern bourgeois society.", "并且一次比一次更强大": "And it is becoming stronger each time.", "无产阶级却是大工业本身的产物": "The proletariat, however, is a product of large-scale industry itself.", "但是他们的信徒总是组成一些反动的宗派": "But their followers always form some reactionary sects.", "资产阶级和无产阶级": "The bourgeoisie and the proletariat.", "被新的、要靠极其遥远的国家和地带的产品来满足的需要所代替了": "Replaced by new needs that must be satisfied by products from extremely distant countries and regions.", "叶片形似小熊的脚掌": "The leaves resemble the paws of a small bear.", "它确凿地证明了机器和分工的破坏作用、资本和地产的积聚、生产过剩、危机、小资产者和小农的必然没落、无产阶级的贫困、生产的无政府状态、财富分配的极不平均、各民族之间的毁灭性的工业战争": "It conclusively proves the destructive effects of machinery and division of labor, the accumulation of capital and land, overproduction, crises, the inevitable decline of small capitalists and small farmers, the poverty of the proletariat, the anarchic state of production, the extremely uneven distribution of wealth, and the destructive industrial wars between nations.", "消灭家庭": "Abolition of the family.", "其中一部分是法国式的民主社会主义者": "Some of them are French-style democratic socialists.", "压迫者和被压迫者": "Oppressors and the oppressed.", "不过": "However.", "无产阶级的逐步组织成为阶级要由一种特意设计出来的社会组织来代替": "The gradual organization of the proletariat into a class must be replaced by a specially designed social organization.", "虹之玉锦一般会有粉红色、中绿色等": "Rainbow satin typically comes in colors like pink and medium green.", "不过他们忘记了": "However, they have forgotten.", "艾德·史塔克": "<PERSON><PERSON><PERSON>.", "而是保守的": "But rather conservative.", "他们必须摧毁至今保护和保障私有财产的一切": "They must destroy everything that protects and guarantees private property until now.", "是建立在资本上面": "It is built on capital.", "是再容易不过了": "It couldn't be easier.", "认为这种运动只是由于盲目地不相信新福音才发生的": "Believing that this movement occurred solely due to a blind disbelief in the new gospel.", "7、按照总的计划增加国家工厂和生产工具": "7. Increase state factories and production tools according to the overall plan.", "它使阶级对立简单化了": "It has simplified class antagonism.", "虽然这些体系的创始人在许多方面是革命的": "Although the founders of these systems are revolutionary in many respects.", "它反对资产阶级的斗争是和它的存在同时开始的": "Its struggle against the bourgeoisie began simultaneously with its existence.", "而且只有当他们的劳动增殖资本的时候才能找到工作": "And they can only find work when their labor increases capital.", "这种曾经郑重其事地看待自己那一套拙劣的小学生作业并且大言不惭地加以吹嘘的德国社会主义": "This German socialism, which once took its own set of poor student assignments seriously and shamelessly boasted about them.", "并向他叽叽咕咕地说一些或多或少凶险的预言": "And muttered some more or less ominous prophecies to him.", "每个人的自由发展是一切人的自由发展的条件": "The free development of each is the condition for the free development of all.", "在无产阶级和资产阶级的斗争所经历的各个发展阶段上": "In all the stages of development experienced in the struggle between the proletariat and the bourgeoisie.", "一步一步地夺取资产阶级的全部资本": "Step by step, seize all the capital of the bourgeoisie.", "工人领到了用现钱支付的工资的时候": "When the workers receive wages paid in cash.", "你们既然用你们资产阶级关于自由、教育、法等等的观念来衡量废除资产阶级所有制的主张": "Since you measure the proposal to abolish bourgeois private property with your bourgeois concepts of freedom, education, law, etc.", "当基督教思想在18世纪被启蒙思想击败的时候": "When Christian thought was defeated by Enlightenment thought in the 18th century.", "而代表人的本质的利益": "And represent the essential interests of people.", "用以塑造无产阶级的运动": "Used to shape the movement of the proletariat.", "现代的、资产阶级的家庭是建立在什么基础上的呢": "What is the foundation of the modern bourgeois family?", "而是一种社会力量": "But rather a social force.", "一方面": "On one hand.", "人们的意识": "People's consciousness.", "我们几乎到处都可以看到社会完全划分为各个不同的等级": "We can almost see society completely divided into different ranks everywhere.", "共产主义革命就是同传统的所有制关系实行最彻底的决裂；毫不奇怪": "The communist revolution is the most thorough break with traditional property relations; it is not surprising.", "你们的观念本身是资产阶级的生产关系和所有制关系的产物": "Your concepts themselves are products of bourgeois production relations and property relations.", "正像你们的法不过是被奉为法律的你们这个阶级的意志一样": "Just as your laws are merely the will of your class sanctified as law.", "该种原产于南非开普省": "This type originated in the Cape Province of South Africa.", "资产阶级除非对生产工具": "The bourgeoisie, unless it is about the means of production.", "他有预知未来的能力": "He has the ability to foresee the future.", "现代的资产阶级正是他们的社会制度的必然产物": "The modern bourgeoisie is the inevitable product of their social system.", "即无产者": "That is, the proletariat.", "但是不要由这些条件必然产生的斗争和危险": "But do not let the struggles and dangers that necessarily arise from these conditions.", "至多只能减少资产阶级的统治费用和简化它的财政管理": "At most, it can only reduce the ruling costs of the bourgeoisie and simplify its financial management.", "市场总是在扩大": "The market is always expanding.", "我的妹妹": "My sister.", "易群生": "<PERSON>.", "懒惰之风就会兴起": "The wind of laziness will rise.", "例如在法国": "For example, in France.", "在中世纪深受反动派称许的那种人力的野蛮使用": "The barbaric use of human labor that was highly praised by reactionaries in the Middle Ages.", "在工商业不很发达的国家里": "In countries where industry and commerce are not very developed.", "披针形或卵形": "Needle-shaped or oval-shaped.", "我们循序探讨了现存社会内部或多或少隐蔽着的国内战争": "We have sequentially explored the more or less hidden domestic wars within existing society.", "她对我态度冷淡": "She was cold towards me.", "熊童子": "Bear child.", "由于无产阶级解放的物质条件还没具备": "Due to the material conditions for the liberation of the proletariat not yet being in place.", "共产党人把自己的主要注意力集中在德国": "Communists focus their main attention on Germany.", "原来意义上的政治权力": "Political power in its original sense", "即小工业家、小商人、手工业者、农民": "namely small industrialists, small merchants, artisans, and farmers", "使一切国家的生产和消费都成为世界性的了": "has made the production and consumption of all countries global", "迫使他们用法律形式承认工人的个别利益": "forces them to legally recognize the individual interests of workers", "一切等级的和固定的东西都烟消云散了": "All hierarchies and fixed structures have vanished", "正式的卖淫更不必说了": "Formal prostitution goes without saying", "正是因为私有财产对十分之九的成员来说已经不存在": "precisely because private property has ceased to exist for nine-tenths of its members", "在资产阶级社会里": "In bourgeois society", "碧光环叶表面有半透明的颗粒感": "The surface of the green halo leaves has a translucent granularity", "如果不就内容而就形式来说": "If we speak in terms of form rather than content", "现代资产者": "modern capitalists", "都可以归结为这样一个同义反复": "can all be reduced to this tautology", "在商业危机期间": "During commercial crises", "调用RemoveFriend": "Call RemoveFriend", "在那里": "There", "而革命的法国资产阶级的意志的表现": "and the expression of the will of the revolutionary French bourgeoisie", "古老的民族工业被消灭了": "The old national industries have been destroyed", "把一切生产工具集中在国家即组织成为统治阶级的无产阶级手里": "concentrating all means of production in the hands of the state, which is organized as the ruling class of the proletariat", "在他们看来": "In their view", "1．反动的社会主义": "1. Reactionary socialism", "2、征收高额累进税": "2. Levying high progressive taxes", "他们不是革命的": "They are not revolutionary", "唱唱诅咒他们的新统治者的歌": "singing songs cursing their new rulers", "繁殖方法有扦插": "Propagation methods include cuttings", "萌萌的样子让人爱不释手": "The cute appearance makes people unable to put it down", "正在清空": "is being emptied", "虽然完全不是资产阶级所理解的那种意思": "although it is completely different from what the bourgeoisie understands", "旧社会内部的所有冲突在许多方面都促进了无产阶级的发展": "All conflicts within the old society have, in many ways, facilitated the development of the proletariat", "这种家庭只是在资产阶级那里才以充分发展的形式存在着": "This type of family only exists in a fully developed form among the bourgeoisie", "使财产聚集在少数人的手里": "concentrating property in the hands of a few", "原封不动地保持旧的生产方式": "maintaining the old modes of production unchanged", "第一次法国革命的要求": "The demands of the First French Revolution", "它已经受到这种关系的阻碍；而它一着手克服这种障碍": "It has been hindered by this relationship; and as soon as it begins to overcome this obstacle", "工人变成了机器的单纯的附属品": "Workers have become mere appendages to machines", "现代工业已经把家长式的师傅的小作坊变成了工业资本家的大工厂": "Modern industry has transformed the paternalistic master's small workshop into large factories owned by industrial capitalists", "资本具有独立性和个性": "Capital has independence and individuality", "您的 API_KEY": "Your API_KEY", "对工人阶级来说": "For the working class", "只要有了这种联系": "As long as there is this connection", "就是要消灭人们最亲密的关系": "It is to eliminate the most intimate relationships among people", "生活资料太多": "There is an excess of means of livelihood", "联合的行动": "Collective action", "消灭私人营利": "Abolish private profit", "无法理解用户意图": "Unable to understand user intentions", "一切神圣的东西都被亵渎了": "All sacred things have been desecrated", "在这一切斗争中": "In all this struggle", "它无情地斩断了把人们束缚于天然尊长的形形色色的封建羁绊": "It ruthlessly cuts the various feudal ties that bind people to their natural superiors", "圣西门、傅立叶、欧文等人的体系": "The systems of <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, and others", "无产者的一切家庭联系越是由于大工业的发展而被破坏": "The family ties of the proletariat are increasingly destroyed by the development of large industry", "在过去的各个历史时代": "In all past historical eras", "性格独立坚强": "Strong and independent character", "以便德国工人能够立刻利用资产阶级统治所必然带来的社会的和政治的条件作为反对资产阶级的武器": "So that German workers can immediately use the social and political conditions inevitably brought about by bourgeois rule as weapons against the bourgeoisie", "共产党人强调和坚持整个无产阶级共同的不分民族的利益；另一方面": "Communists emphasize and uphold the common interests of the entire proletariat, regardless of nationality; on the other hand", "不过因为年龄和性别的不同而需要不同的费用罢了": "It only requires different costs due to differences in age and gender", "资产阶级抹去了一切向来受人尊崇和令人敬畏的职业的神圣光环": "The bourgeoisie has erased the sacred aura of all professions that were once respected and revered", "对生的叶片呈短棒状": "The living blades are short and stick-like", "资产者的家庭自然会随着它的这种补充的消失而消失": "The families of the capitalists will naturally disappear with the disappearance of this supplement", "在瑞士": "In Switzerland", "自然力的征服": "The conquest of natural forces", "他们胜过其余无产阶级群众的地方在于他们了解无产阶级运动的条件、进程和一般结果": "Where they surpass the rest of the proletarian masses is in their understanding of the conditions, processes, and general outcomes of the proletarian movement", "——而为了建造这一切空中楼阁": "——And in order to build all these castles in the air", "这个阶级胆战心惊地从资产阶级的工业统治和政治统治那里等候着无可幸免的灭亡": "This class anxiously awaits its inevitable demise from the industrial and political rule of the bourgeoisie", "叶端具红色爪样齿": "The leaf tips have red claw-like teeth", "这一部分人包括": "This part of the people includes", "分析应该调用哪个工具函数": "Which tool function should be called for analysis", "在一些地方组成独立的城市共和国": "Form independent city-states in some places", "它的商品的低廉价格": "The low prices of its goods", "而且占有一种社会地位": "And occupies a certain social status", "匙状长圆形；茎生叶互生": "Spoon-shaped and elongated; leaves alternate on the stem", "是同它的生产费用相等的": "Is equal to its production costs", "为了有可能压迫一个阶级": "In order to possibly oppress a class", "资产者之为资产者": "The essence of capitalists as capitalists", "物种索引": "Species index", "以前的中间等级的下层": "The lower levels of the former middle class", "德国将在整个欧洲文明更进步的条件下": "Germany will be under more progressive conditions of European civilization", "当古代世界走向灭亡的时候": "When the ancient world is heading towards extinction", "民族对民族的剥削就会随之消灭": "Exploitation of one nation by another will consequently disappear", "由此必然产生的结果就是政治的集中": "The inevitable result of this is political centralization", "发生一种在过去一切时代看来都好像是荒唐现象的社会瘟疫": "A social plague that seems absurd in all past eras occurs", "也就消灭了阶级对立的存在条件": "And thus eliminates the conditions for class opposition", "工业的进步把统治阶级的整批成员抛到无产阶级队伍里去": "The progress of industry throws a whole batch of ruling class members into the proletariat", "在阶级斗争接近决战的时期": "In the period when class struggle approaches a decisive battle", "各国共产党人集会于伦敦": "Communists from various countries gather in London", "资产者是把自己的妻子看作单纯的生产工具的": "Capitalists view their wives merely as production tools", "就其内容来说必然是反动的": "In terms of its content, it is inevitably reactionary", "资产阶级的关系已经太狭窄了": "The relationships of the bourgeoisie have become too narrow", "不再能变为可以垄断的社会力量的时候起": "When it can no longer become a monopolizable social force", "当时资产阶级为了达到自己的政治目的必须而且暂时还能够把整个无产阶级发动起来": "At that time, the bourgeoisie must and can temporarily mobilize the entire proletariat to achieve its political goals", "从而组织成为政党这件事": "Thus organizing into a political party", "即变成资产者": "That is, to become capitalists", "而是同自己的敌人的敌人作斗争": "But rather to fight against the enemy of their enemy", "见《资产阶级和无产阶级》": "See 'The Bourgeoisie and the Proletariat'", "只有无产阶级是真正革命的阶级": "Only the proletariat is the truly revolutionary class", "代替了工业的中间等级": "Replaced the middle class of industry", "也像我们的资产阶级的其他一切关于自由的大话一样": "Just like all the other grand words about freedom from our bourgeoisie", "一方面不得不消灭大量生产力": "On one hand, must eliminate a large amount of productive forces", "因为它不得不让自己的奴隶落到不能养活它反而要它来养活的地步": "Because it has to let its slaves fall to the point where they cannot support it, but instead have to support it.", "密生白色短毛": "Dense white short hair.", "调和对立": "Harmonizing contradictions.", "但是": "But.", "他们没有任何同整个无产阶级的利益不同的利益": "They have no interests different from those of the entire proletariat.", "都演过这出戏": "Have all played this role.", "这种反对阶级斗争的幻想": "This fantasy against class struggle.", "在这种占有下": "Under this possession.", "他们同资产阶级作斗争": "They struggle against the bourgeoisie.", "统治阶级内部的、整个旧社会内部的瓦解过程": "The process of disintegration within the ruling class and the entire old society.", "一般可用泥炭土、蛭石和珍珠岩的混合土": "Generally, a mixture of peat, vermiculite, and perlite can be used.", "无产阶级即现代工人阶级也在同一程度上得到发展；现代的工人只有当他们找到工作的时候才能生存": "The proletariat, that is, the modern working class, also develops to the same extent; modern workers can only survive when they find work.", "它就越是可鄙、可恨和可恶": "The more it is despicable, hateful, and abominable.", "这些著作抨击现存社会的全部基础": "These works criticize the entire foundation of the existing society.", "马上就有资产阶级中的另一部分人——房东、小店主、当铺老板等等向他们扑来": "Immediately, another part of the bourgeoisie—landlords, small shopkeepers, pawnbrokers, etc.—rushes towards them.", "而且攻击生产工具本身；他们毁坏那些来竞争的外国商品": "And they attack the means of production itself; they destroy those foreign goods that come to compete.", "其他情形一律不适用": "Other situations do not apply.", "资产阶级处于不断的斗争中": "The bourgeoisie is in constant struggle.", "德国的哲学家、半哲学家和美文学家": "German philosophers, semi-philosophers, and literary figures.", "由于他们本身的生活状况": "Due to their own living conditions.", "资产阶级揭示了": "The bourgeoisie reveals.", "它创立了巨大的城市": "It has created vast cities.", "提倡社会和谐": "Advocating social harmony.", "代替那存在着阶级和阶级对立的资产阶级旧社会的": "Replacing the old bourgeois society where classes and class antagonisms exist.", "要给基督教禁欲主义涂上一层社会主义的色彩": "To paint Christian asceticism with a layer of socialist color.", "它迫使一切民族——如果它们不想灭亡的话——采用资产阶级的生产方式；它迫使它们在自己那里推行所谓的文明": "It forces all nations—if they do not want to perish—to adopt the bourgeois mode of production; it forces them to implement what is called civilization in their own territories.", "才是有意义的": "Is meaningful.", "人民群众非但一无所得": "The masses of people gain nothing.", "基督教的社会主义": "Christian socialism.", "最初反对贵族；后来反对同工业进步有利害冲突的那部分资产阶级；经常反对一切外国的资产阶级": "Initially opposed to the nobility; later opposed to that part of the bourgeoisie that has conflicts of interest with industrial progress; often opposed to all foreign bourgeoisie.", "在古罗马": "In ancient Rome.", "而且它结合成更大的集体": "And it combines into a larger collective.", "该分支仅适用于不支持stream的o1模型": "This branch is only applicable to the o1 model that does not support streams.", "以便创造这些条件": "In order to create these conditions.", "锯叶石莲为石莲的变种": "The saw-leaved stone lotus is a variety of the stone lotus.", "从宗教的、哲学的和一切意识形态的观点对共产主义提出的种种责难": "Various criticisms of communism from religious, philosophical, and all ideological perspectives.", "根本不存在于现实界": "Does not exist at all in the real world.", "决不能剥夺他们所没有的东西": "Must not deprive them of what they do not have.", "因为它甚至不能保证自己的奴隶维持奴隶的生活": "Because it cannot even guarantee that its own slaves maintain a slave's life.", "他们还以互相诱奸妻子为最大的享乐": "They also take mutual seduction of each other's wives as their greatest pleasure.", "这些意识形式": "These forms of consciousness.", "再不能把自己阶级的生存条件当作支配一切的规律强加于社会了": "Can no longer impose the conditions of their class's existence as the law governing everything on society.", "现在像一个魔法师一样不能再支配自己用法术呼唤出来的魔鬼了": "Now, like a magician, they can no longer control the demons they have summoned with their spells.", "它们提供了启发工人觉悟的极为宝贵的材料": "They provide extremely valuable material for enlightening workers' consciousness.", "反之": "On the contrary.", "通过示范的力量来为新的社会福音开辟道路": "To pave the way for a new social gospel through the power of demonstration.", "这样就形成了小资产阶级的社会主义": "This formed the socialism of the petty bourgeoisie.", "夏季温度过高会休眠": "Excessively high summer temperatures will cause dormancy.", "一哄而散": "Scattered in a rush.", "用英文、法文、德文、意大利文、弗拉芒文和丹麦文公布于世": "Published in English, French, German, Italian, Flemish, and Danish.", "被各民族的各方面的互相往来和各方面的互相依赖所代替了": "Replaced by the various interactions and interdependencies of different nations.", "伊格瑞特": "<PERSON><PERSON><PERSON>.", "它创造了完全不同于埃及金字塔、罗马水道和哥特式教堂的奇迹；它完成了完全不同于民族大迁徙和十字军征讨的远征": "It created wonders completely different from the Egyptian pyramids, Roman aqueducts, and Gothic cathedrals; it accomplished expeditions entirely different from the great migrations of peoples and the Crusades.", "共产党一分钟也不忽略教育工人尽可能明确地意识到资产阶级和无产阶级的敌对的对立": "The Communist Party does not neglect to educate workers to be as clear as possible about the antagonistic opposition between the bourgeoisie and the proletariat.", "烧毁工厂": "Burn down factories.", "——这是什么缘故呢": "—What is the reason for this?", "在生长初期像兔耳": "In the early stages of growth, like rabbit ears.", "喜温暖干燥": "Prefers warm and dry conditions.", "工业和商业太发达": "Industry and commerce are too developed.", "布兰·史塔克": "<PERSON><PERSON>.", "代之以资产阶级的所有制": "Replaced by bourgeois ownership.", "它们关于未来社会的积极的主张": "Their positive propositions about future society.", "阶级斗争越发展和越具有确定的形式": "As class struggle develops and takes on more definite forms.", "是生产方式和交换方式的一系列变革的产物": "Is a product of a series of changes in the modes of production and exchange.", "调用AddMultiFriends把联系人添加到数据库": "Call AddMultiFriends to add contacts to the database", "从个人财产不再能变为资产阶级财产的时候起": "From the moment personal property can no longer become bourgeois property", "而这种意志的内容是由你们这个阶级的物质生活条件决定的": "And the content of this will is determined by the material living conditions of your class", "罗柏·史塔克": "<PERSON><PERSON>", "通风良好的环境": "Well-ventilated environment", "三、社会主义的和共产主义的文献": "III. Literature of Socialism and Communism", "否则就不能生存下去": "Otherwise, it cannot survive", "保存这个小资产阶级": "Preserve this petty bourgeoisie", "当人们谈到使整个社会革命化的思想时": "When people talk about the idea of revolutionizing the entire society", "性喜欢凉爽通风、日照充足的环境": "Sexually prefer a cool, well-ventilated, and sunny environment", "由于阶级斗争不发展": "Due to the lack of development in class struggle", "但是并不因此放弃对那些从革命的传统中承袭下来的空谈和幻想采取批判态度的权利": "But this does not give up the right to take a critical attitude towards the empty talk and fantasies inherited from revolutionary traditions", "使商业、航海业和工业空前高涨": "Causing commerce, shipping, and industry to flourish unprecedentedly", "而是要废除资产阶级的所有制": "But rather to abolish bourgeois ownership", "在法国人对资产阶级国家的批判下面写上所谓“抽象普遍物的统治的扬弃”": "Under the French critique of the bourgeois state, write the so-called 'abolition of the rule of abstract universal commodities'", "驱使资产阶级奔走于全球各地": "Forcing the bourgeoisie to rush around the globe", "所有这些对共产主义的物质产品的占有方式和生产方式的责备": "All these accusations against the ways of possessing and producing material products of communism", "至少是各文明国家的联合的行动": "At least a united action of all civilized nations", "直到这个战争爆发为公开的革命": "Until this war breaks out as an open revolution", "企图以此来巩固它们已获得的生活地位": "Attempting to consolidate their already acquired living status", "做一个资本家": "To be a capitalist", "这种对未来社会的幻想的描绘": "This depiction of fantasies about future society", "阶级的教育的终止在他们看来就等于一切教育的终止": "The termination of class education, in their view, equals the termination of all education", "我们来看看雇佣劳动": "Let's take a look at wage labor", "至今的一切社会都是建立在压迫阶级和被压迫阶级的对立之上的": "All societies to date are built on the opposition between the oppressing class and the oppressed class", "由于他们的整个生活状况": "Due to their entire living conditions", "就达到非常强烈、非常尖锐的程度": "Reaching a very intense and sharp degree", "它不是提倡用行善和求乞、独身和禁欲、修道和礼拜来代替这一切吗": "Isn't it advocating to replace all this with doing good, begging, celibacy, asceticism, monasticism, and worship?", "它用公开的、无耻的、直接的、露骨的剥削代替了由宗教幻想和政治幻想掩盖着的剥削": "It replaces the exploitation covered by religious and political fantasies with open, shameless, direct, and blatant exploitation", "有欧文主义者反对宪章派": "There are Owenites opposing the Charterists", "对话记忆中": "In the memory of dialogue", "如果说无产阶级在反对资产阶级的斗争中一定要联合为阶级": "If the proletariat must unite as a class in the struggle against the bourgeoisie", "使东方从属于西方": "Make the East subordinate to the West", "这种发展又反过来促进了工业的扩展": "This development in turn promotes the expansion of industry", "与其说是因为它产生了无产阶级": "Rather than because it produced the proletariat", "反对国家吗": "Oppose the state?", "商品的价格": "The price of goods", "把这种关系变成了纯粹的金钱关系": "Turn this relationship into a purely monetary relationship", "这难道需要经过深思才能了解吗": "Does this require deep thought to understand?", "它的力量日益增长": "Its power is growing increasingly", "雇佣劳动完全是建立在工人的自相竞争之上的": "Wage labor is entirely based on the competition among workers", "其实": "In fact", "有人还责备共产党人": "Some even blame the communists", "至今一切社会的历史都是阶级斗争的历史": "To this day, the history of all societies is the history of class struggles", "还存在着一切社会状态所共有的永恒真理": "There still exists an eternal truth common to all social conditions", "就可以了解共产党人同已经形成的工人政党的关系": "One can understand the relationship between communists and the already formed workers' parties", "繁殖方式一般为扦插繁殖": "The propagation method is generally cuttings", "自然是用小资产阶级和小农的尺度去批判资产阶级制度的": "Naturally, it criticizes the bourgeois system using the standards of the petty bourgeoisie and small farmers", "石莲": "Stone lotus", "已经积累起来的劳动只是扩大、丰富和提高工人的生活的一种手段": "The accumulated labor is merely a means to expand, enrich, and elevate the lives of workers", "16世纪遗留下来的、从那时起经常以不同形式重新出现的小资产阶级": "The petty bourgeoisie left over from the 16th century, which has frequently reappeared in different forms since then", "而站到无产阶级的立场上来": "And stand from the perspective of the proletariat", "斗争爆发为起义": "The struggle erupts as an uprising", "只有在不断产生出新的雇佣劳动来重新加以剥削的条件下才能增殖的财产": "Property that can only proliferate under conditions of continuously generating new wage labor for re-exploitation", "转到无产阶级方面来了": "Has shifted to the proletariat's side", "它使人口密集起来": "It densifies the population", "现在资产阶级中也有一部分人": "Now there are also some people in the bourgeoisie", "福娘原产于非洲西南部的纳米比亚": "The succulent originates from southwestern Africa, Namibia", "大家知道": "Everyone knows", "随着现在的生产关系的消灭": "With the elimination of the current production relations", "全世界无产者": "Proletarians of the world", "而这种对立在不同的时代具有不同的形式": "And this opposition takes different forms in different eras", "随着资产阶级的发展": "With the development of the bourgeoisie", "或者至少也使他们的生活条件受到威胁": "Or at least threatens their living conditions", "在这里": "Here", "工业的发展已经把它消灭了": "The development of industry has already eliminated it.", "随着工业的发展": "With the development of industry", "总是在某些共同的形式中运动的": "Always moving in certain common forms.", "使工人通过结社而达到的革命联合代替了他们由于竞争而造成的分散状态": "The revolutionary union achieved by workers through association replaced their state of dispersion caused by competition.", "仅仅对于不自由的买卖来说": "Only in relation to unfree trade.", "不顾信义、仁爱和名誉去做羊毛、甜菜和烧洒的买卖": "Doing business in wool, beets, and distilling without regard for integrity, kindness, and reputation.", "资产阶级撕下了罩在家庭关系上的温情脉脉的面纱": "The bourgeoisie has torn away the tender veil covering family relationships.", "那是再可笑不过了": "That is nothing short of ridiculous.", "是为了工人阶级的利益": "It is for the benefit of the working class.", "法国的批判": "Critique of France", "她和我关系亲密": "She has a close relationship with me.", "就再也没有任何别的联系了": "There is no other connection anymore.", "福娘为景天科银波锦属的肉质草本植物": "<PERSON><PERSON><PERSON> is a succulent herbaceous plant of the Crassulaceae family.", "古代的各种宗教就被基督教战胜了": "Various ancient religions were defeated by Christianity.", "因而使正在崩溃的封建社会内部的革命因素迅速发展": "Thus, the revolutionary factors within the collapsing feudal society rapidly developed.", "如果用户希望获取社交指导": "If users wish to obtain social guidance.", "信仰自由和宗教自由的思想": "The ideas of freedom of belief and religious freedom.", "资产阶级时代": "The era of the bourgeoisie.", "但是他们在当前的运动中同时代表运动的未来": "But they simultaneously represent the future of the movement in the current struggle.", "德国的或“真正的”社会主义": "German or 'real' socialism.", "这或者是由于工作时间的延长": "This may be due to the extension of working hours.", "叶缘外围镶着紫红色": "The leaf margins are edged with purplish-red.", "现代的无产者利用铁路只要几年就可以达到了": "Modern proletarians can achieve this by using the railway in just a few years.", "这种社会主义按其实际内容来说": "This socialism, in terms of its actual content,", "消灭雇佣劳动": "Abolish wage labor.", "工业中的行会制度": "The guild system in industry.", "他们说": "They say.", "喜肥": "Likes fat.", "一切所有制关系都经历了经常的历史更替、经常的历史变更": "All property relations have undergone constant historical changes and replacements.", "这种劳动所创造的资本": "The capital created by this labor.", "碧光环喜温暖和散射光充足的环境": "Bright blue light enjoys a warm and well-diffused environment.", "像其他任何货物一样": "Like any other goods", "资产阶级不能统治下去了": "The bourgeoisie can no longer rule", "从劳动不再能变为资本、货币、地租": "Labor can no longer be transformed into capital, money, or rent", "即一般人的利益": "That is, the interests of the general public", "有的是因为他们的小资本不足以经营大工业": "Some of them lack sufficient small capital to operate large industries", "这种联合由于大工业所造成的日益发达的交通工具而得到发展": "This union is developed due to the increasingly advanced means of transportation caused by large industries", "它必然表现为关于真正的社会、关于实现人的本质的无谓思辨": "It inevitably manifests as futile speculation about the true society and the realization of human essence", "僧侣的社会主义也总是同封建的社会主义携手同行的": "Monastic socialism always walks hand in hand with feudal socialism", "在现代文明已经发展的国家里": "In countries where modern civilization has developed", "资产阶级的社会主义只有在它变成纯粹的演说辞令的时候": "Bourgeois socialism only exists when it becomes pure rhetoric", "一部分是激进的资产者": "A part of them are radical capitalists", "说他们想用正式的、公开的公妻制来代替伪善地掩蔽着的公妻制": "Claiming they want to replace the hypocritically concealed communal marriage with a formal, public communal marriage", "旧思想的瓦解是同旧生活条件的瓦解步调一致的": "The disintegration of old ideas is in step with the disintegration of old living conditions", "化学在工业和农业中的应用": "The application of chemistry in industry and agriculture", "花期夏秋": "Blooming period: summer and autumn", "并以统治阶级的资格用暴力消灭旧的生产关系": "And with the qualification of the ruling class, violently eliminate the old production relations", "所以他们同样地受到竞争的一切变化、市场的一切波动的影响": "Thus, they are equally affected by all changes in competition and all fluctuations in the market", "在这些生产资料和交换手段发展的一定阶段上": "At a certain stage of the development of these means of production and exchange", "废除先前存在的所有制关系": "Abolish all previously existing property relations", "那他们只是忘记了": "Then they simply forgot", "法国的文献完全失去了直接实践的意义": "French literature has completely lost its direct practical significance", "是通过翻译的": "It is through translation", "于是德国人就认为": "Thus, the Germans believe", "机器使劳动的差别越来越小": "Machines make the differences in labor increasingly smaller", "正在向量化": "Is moving towards quantification", "它按照自己的面貌为自己创造出一个世界": "It creates a world for itself according to its own appearance", "这些主张本身还带有纯粹空想的性质": "These claims themselves still carry a purely utopian nature", "获者不劳": "The winners do not labor", "有贵族、骑士、平民、奴隶": "There are nobles, knights, commoners, and slaves", "由于推广机器和分工": "Due to the promotion of machines and division of labor", "非常可爱": "Very lovely", "在日常生活中": "In daily life", "只不过是现代生产力反抗现代生产关系、反抗作为资产阶级及其统治的存在条件的所有制关系的历史": "It is merely the history of modern productive forces resisting modern production relations, resisting the property relations that serve as the conditions for the existence of the bourgeoisie and its rule.", "生产的不断变革": "The continuous transformation of production.", "有人会说": "Some may say.", "丙": "C.", "旧的、靠本国产品来满足的需要": "The old needs satisfied by domestic products.", "十分可爱": "Very lovely.", "在法国": "In France.", "世界市场使商业、航海业和陆路交通得到了巨大的发展": "The world market has greatly developed commerce, navigation, and land transportation.", "而且归根到底只有通过社会全体成员的共同活动": "And ultimately, it can only be achieved through the collective activities of all members of society.", "等等": "And so on.", "花在工人身上的费用": "Expenses incurred on workers.", "只有在统治阶级的利益需要他活着的时候才能活着": "Can only live when the interests of the ruling class require him to be alive.", "这种社会主义所理解的物质生活条件的改变": "The changes in material living conditions understood by this socialism.", "过去一切阶级在争得统治之后": "After all past classes have fought for domination.", "电报的使用": "The use of the telegraph.", "2．保守的或资产阶级的社会主义": "2. Conservative or bourgeois socialism.", "德国小市民是模范的人": "The German petty bourgeois is the model person.", "只是为了被剥削的工人阶级的利益才去写对资产阶级的控诉书": "Only to write accusations against the bourgeoisie for the benefit of the exploited working class.", "人对人的剥削一消灭": "The exploitation of man by man is abolished.", "公共权力就失去政治性质": "Public power loses its political nature.", "看过第二章之后": "After reading the second chapter.", "不管阶级对立具有什么样的形式": "Regardless of the forms of class opposition.", "有封建主、臣仆、行会师傅、帮工、农奴": "There are feudal lords, servants, guild masters, helpers, and serfs.", "无产阶级只是一个受苦最深的阶级": "The proletariat is merely the class that suffers the most.", "这些信徒无视无产阶级的历史进展": "These believers ignore the historical progress of the proletariat.", "关于这个时期": "Regarding this period.", "甲": "A.", "消灭私有制": "Abolish private property.", "在欧洲游荡": "Wandering in Europe.", "把教育同物质生产结合起来": "Combine education with material production.", "现代的国家政权不过是管理整个资产阶级的共同事务的委员会罢了": "The modern state power is merely a committee managing the common affairs of the entire bourgeoisie.", "把中世纪遗留下来的一切阶级排挤到后面去": "Push all classes left over from the Middle Ages to the back.", "因为德国正处在资产阶级革命的前夜": "Because Germany is on the eve of the bourgeois revolution", "机器运转的加速": "The acceleration of machine operations", "并且尽可能快地增加生产力的总量": "And to increase the total amount of productivity as quickly as possible", "走进新的耶路撒冷": "Walking into the New Jerusalem", "在英国": "In England", "现在却对准资产阶级自己了": "Now it is directed at the bourgeoisie themselves", "共产党人不屑于隐瞒自己的观点和意图": "Communists disdain to conceal their views and intentions", "还是死守着老师们的旧观点": "Or stubbornly cling to the old views of their teachers", "资产阶级的社会主义就是这样一个论断": "The socialism of the bourgeoisie is such a proposition", "法律、道德、宗教在他们看来全都是资产阶级偏见": "Law, morality, and religion are all seen by them as bourgeois prejudices", "人们只要理解他们的体系": "People just need to understand their system", "或者是由于在一定时间内所要求的劳动的增加": "Or due to the increase in labor required over a certain period", "因为社会上文明过度": "Because civilization has become excessive in society", "使城市人口比农村人口大大增加起来": "Causing the urban population to greatly exceed the rural population", "现代的工人却相反": "Modern workers, however, are the opposite", "已经不是本地的原料": "No longer local raw materials", "它已经被炸毁了": "It has already been destroyed", "分裂为两大相互直接对立的阶级": "Divided into two directly opposing classes", "这种关系已经在阻碍生产而不是促进生产了": "This relationship has been hindering production rather than promoting it", "他们在法国人对货币关系的批判下面写上“人的本质的外化”": "They wrote 'the externalization of human essence' under the French critique of monetary relations", "在工场手工业时期": "During the period of workshop handicrafts", "这种掌握": "This mastery", "在叙述无产阶级发展的最一般的阶段的时候": "When narrating the most general stages of proletarian development", "因为在这个社会里劳者不获": "Because in this society, laborers do not gain", "工人没有祖国": "Workers have no country", "该种叶形叶色较美": "This type of leaf shape and color is more beautiful", "就不再适应已经发展的生产力了": "No longer adapting to the developed productive forces", "说什么在这个资产阶级运动中": "Saying that in this bourgeois movement", "这些条件只是资产阶级时代的产物": "These conditions are merely products of the bourgeois era", "资产阶级再不能做社会的统治阶级了": "The bourgeoisie can no longer be the ruling class of society", "共产党人始终代表整个运动的利益": "Communists always represent the interests of the entire movement", "行会师傅被工业的中间等级排挤掉了；各种行业组织之间的分工随着各个作坊内部的分工的出现而消失了": "Guild masters have been pushed out by the intermediate levels of industry; the division of labor between various industry organizations has disappeared with the emergence of internal divisions of labor in each workshop", "我们的资产者不以他们的无产者的妻子和女儿受他们支配为满足": "Our capitalists are not satisfied with having the wives and daughters of their proletarians under their control.", "吸收辐射": "Absorbing radiation", "因而德国的资产阶级革命只能是无产阶级革命的直接序幕": "Thus, the bourgeois revolution in Germany can only be a direct prelude to the proletarian revolution.", "以便保障资产阶级社会的生存": "In order to ensure the survival of bourgeois society.", "而不是加以革新": "And not to innovate.", "我们要消灭的只是这种占有的可怜的性质": "What we want to eliminate is only the miserable nature of this possession.", "更加彻底地利用旧的市场": "To make more thorough use of the old markets.", "这种社会主义非常透彻地分析了现代生产关系中的矛盾": "This socialism analyzes the contradictions in modern production relations very thoroughly.", "它将失掉它的阶级性质": "It will lose its class nature.", "建立国内移民区": "Establish domestic immigrant zones.", "而且每天都在消灭它": "And it is being eliminated every day.", "即正式的和非正式的卖淫": "That is, formal and informal prostitution.", "现代工业越发达": "The more developed modern industry is.", "然后是某一地方的某一劳动部门的工人": "Then there are the workers of a certain labor sector in a certain place."}