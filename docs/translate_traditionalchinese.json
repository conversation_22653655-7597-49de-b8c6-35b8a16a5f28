{"print亮黄": "PrintBrightYellow", "print亮绿": "PrintBrightGreen", "print亮红": "PrintBrightRed", "print红": "PrintRed", "print绿": "PrintGreen", "print黄": "PrintYellow", "print蓝": "PrintBlue", "print紫": "PrintPurple", "print靛": "PrintIndigo", "print亮蓝": "PrintBrightBlue", "print亮紫": "PrintBrightPurple", "print亮靛": "PrintBrightIndigo", "读文章写摘要": "ReadArticleWriteSummary", "批量生成函数注释": "BatchGenerateFunctionComments", "生成函数注释": "GenerateFunctionComments", "解析项目本身": "ParseProjectItself", "解析项目源代码": "ParseProjectSourceCode", "解析一个Python项目": "ParsePythonProject", "解析一个C项目的头文件": "ParseCProjectHeaderFile", "解析一个C项目": "ParseCProject", "解析一个Rust项目": "ParseRustProject", "解析一个Java项目": "ParseJavaProject", "解析一个前端项目": "ParseAFrontEndProject", "高阶功能模板函数": "HigherOrderFeatureTemplateFunction", "高级功能函数模板": "AdvancedFeatureFunctionTemplate", "全项目切换英文": "SwitchEntireProjectToEnglish", "代码重写为全英文_多线程": "RewriteCodeToEnglishMultithreading", "Latex英文润色": "LatexEnglishPolishing", "Latex全文润色": "LatexWholeDocumentPolishing", "同时问询": "InquireSimultaneously", "询问多个大语言模型": "InquireMultipleLargeLanguageModels", "解析一个Lua项目": "ParseALuaProject", "解析一个CSharp项目": "ParseACSharpProject", "总结word文档": "SummarizeWordDocument", "解析ipynb文件": "ParseIpynbFile", "解析JupyterNotebook": "ParseJupyterNotebook", "Conversation_To_File": "ConversationHistoryArchive", "载入Conversation_To_File": "LoadConversationHistoryArchive", "删除所有本地对话历史记录": "DeleteAllLocalConversationHistoryRecords", "Markdown英译中": "MarkdownEnglishToChinese", "Markdown_Translate": "BatchMarkdownTranslation", "批量总结PDF文档": "BatchSummarizePDFDocuments", "批量总结PDF文档pdfminer": "BatchSummarizePDFDocumentsPdfminer", "批量翻译PDF文档": "BatchTranslatePDFDocuments", "PDF_Translate": "BatchTranslatePdfDocumentsMultithreaded", "谷歌检索小助手": "GoogleSearchAssistant", "理解PDF文档内容标准文件输入": "StandardFileInputForUnderstandingPdfDocumentContent", "理解PDF文档内容": "UnderstandingPdfDocumentContent", "Latex中文润色": "ChineseProofreadingInLatex", "Latex中译英": "ChineseToEnglishTranslationInLatex", "Latex全文翻译": "FullTextTranslationInLatex", "Latex英译中": "EnglishToChineseTranslationInLatex", "Markdown中译英": "ChineseToEnglishTranslationInMarkdown", "下载arxiv论文并翻译摘要": "DownloadArxivPapersAndTranslateAbstract", "下载arxiv论文翻译摘要": "DownloadArxivPapersTranslateAbstract", "连接网络回答问题": "ConnectToInternetToAnswerQuestions", "联网的ChatGPT": "ChatGPTConnectedToInternet", "解析任意code项目": "ParsingAnyCodeProject", "同时问询_指定模型": "InquiryWithSpecifiedModelSimultaneously", "图片生成": "ImageGeneration", "test_解析ipynb文件": "TestParsingIpynbFile", "把字符太少的块清除为回车": "RemoveBlocksWithTooFewCharactersToNewline", "清理多余的空行": "CleaningUpExtraBlankLines", "合并小写开头的段落块": "MergeParagraphBlocksStartingWithLowerCase", "多文件润色": "ProofreadingMultipleFiles", "多文件翻译": "TranslationOfMultipleFiles", "解析docx": "ParseDocx", "解析PDF": "ParsePDF", "解析Paper": "ParsePaper", "ipynb解释": "IpynbInterpret", "解析源代码新": "ParseSourceCodeNew", "输入区": "輸入區", "获取文章meta信息": "獲取文章meta信息", "等待": "等待", "不能正常加载MOSS的参数！": "無法正常加載MOSS的參數！", "橙色": "橙色", "窗口布局": "窗口佈局", "需要安装pip install py7zr来解压7z文件": "需要安裝pip install py7zr來解壓7z文件", "上下布局": "上下佈局", "打开文件": "打開文件", "可能需要分组处理": "可能需要分組處理", "用tex格式": "用tex格式", "按Shift+Enter换行": "按Shift+Enter換行", "输入路径或上传压缩包": "輸入路徑或上傳壓縮包", "翻译成地道的中文": "翻譯成地道的中文", "上下文": "上下文", "请耐心完成后再提交新问题": "請耐心完成後再提交新問題", "可以直接修改对话界面内容": "可以直接修改對話界面內容", "检测输入参数": "檢測輸入參數", "也许会导致低配计算机卡死 ……": "也許會導致低配計算機卡死……", "html格式": "html格式", "不能识别的URL！": "無法識別的URL！", "第2步": "第2步", "若上传压缩文件": "若上傳壓縮文件", "多线程润色开始": "多線程潤色開始", "警告！API_URL配置选项将被弃用": "警告！API_URL配置選項將被棄用", "非OpenAI官方接口的出现这样的报错": "非OpenAI官方接口出現這樣的錯誤", "如果没找到任何文件": "如果沒找到任何文件", "生成一份任务执行报告": "生成一份任務執行報告", "而cl**h 的默认本地协议是http": "而cl**h的默認本地協議是http", "gpt_replying_buffer也写完了": "gpt_replying_buffer也寫完了", "是本次输出": "是本次輸出", "展现在报告中的输入": "展現在報告中的輸入", "和端口": "和端口", "Pay-as-you-go users的限制是每分钟3500次": "Pay-as-you-go用戶的限制是每分鐘3500次", "既可以写": "既可以寫", "输入清除键": "輸入清除鍵", "gpt模型参数": "gpt模型參數", "直接清除历史": "直接清除歷史", "当前模型": "當前模型", "；5、中文摘要翻译": "；5、中文摘要翻譯", "将markdown转化为好看的html": "將markdown轉換為好看的html", "谷歌学术检索助手": "谷歌學術檢索助手", "后语": "後語", "请确认是否满足您的需要": "請確認是否滿足您的需要", "本地路径": "本地路徑", "sk-此处填API密钥": "sk-此處填API密鑰", "正常结束": "正常結束", "排除了以上两个情况": "排除了以上兩個情況", "把gradio的运行地址更改到指定的二次路径上": "將gradio的運行地址更改到指定的二次路徑上", "配置其Path环境变量": "配置其Path環境變量", "的第": "的第", "减少重复": "減少重複", "如果超过期限没有喂狗": "如果超過期限沒有餵狗", "函数的说明请见 request_llms/bridge_all.py": "函數的說明請見 request_llms/bridge_all.py", "第7步": "第7步", "说": "說", "中途接收可能的终止指令": "中途接收可能的終止指令", "第5次尝试": "第5次嘗試", "gradio可用颜色列表": "gradio可用顏色列表", "返回的结果是": "返回的結果是", "出现的所有文章": "所有出現的文章", "更换LLM模型/请求源": "更換LLM模型/請求源", "调用NewBing时": "調用NewBing時", "AutoGPT是什么": "AutoGPT是什麼", "则换行符更有可能表示段落分隔": "則換行符更有可能表示段落分隔", "接收文件后与chatbot的互动": "接收文件後與chatbot的互動", "每个子任务展现在报告中的输入": "每個子任務展現在報告中的輸入", "按钮见functional.py": "按鈕見functional.py", "地址🚀": "地址🚀", "将长文本分离开来": "將長文本分離開來", "ChatGLM消耗大量的内存": "ChatGLM消耗大量的內存", "使用 lru缓存 加快转换速度": "使用lru緩存加快轉換速度", "屏蔽掉 chatglm的多线程": "屏蔽掉chatglm的多線程", "不起实际作用": "不起實際作用", "先寻找到解压的文件夹路径": "先尋找到解壓的文件夾路徑", "观察窗": "觀察窗", "请解释以下代码": "請解釋以下代碼", "使用中文回答我的问题": "使用中文回答我的問題", "备份一个文件": "備份一個文件", "未知": "未知", "其他錯誤": "其他錯誤", "等待NewBing响应": "等待NewBing回應", "找不到任何CSharp文件": "找不到任何CSharp檔案", "插件demo": "插件範例", "1. 把input的余量留出来": "1. 留出input的餘量", "如果文章被切分了": "如果文章被切分了", "或者您没有获得体验资格": "或者您沒有獲得體驗資格", "修正值": "修正值", "正在重试": "正在重試", "展示分割效果": "展示分割效果", "已禁用": "已禁用", "抽取摘要": "抽取摘要", "下载完成": "下載完成", "无法连接到该网页": "無法連接到該網頁", "根据以上的对话": "根據以上的對話", "第1次尝试": "第1次嘗試", "我们用最暴力的方法切割": "我們用最暴力的方法切割", "回滚代码到原始的浏览器打开函数": "回滾程式碼到原始的瀏覽器開啟函數", "先上传存档或输入路径": "先上傳存檔或輸入路徑", "避免代理网络产生意外污染": "避免代理網路產生意外污染", "发送图片时": "傳送圖片時", "第二步": "第二步", "完成": "完成", "搜索页面中": "搜索頁面中", "下载中": "下載中", "重试一次": "重試一次", "历史上的今天": "歷史上的今天", "2. 替换跨行的连词": "2. 替換跨行的連詞", "协议": "協議", "批量ChineseToEnglishTranslationInMarkdown": "批量Markdown中文轉英文翻譯", "也可以直接是": "也可以直接是", "插件模型的参数": "插件模型的參數", "也可以根据之前的内容长度来判断段落是否已经足够长": "也可以根據之前的內容長度來判斷段落是否已經足夠長", "引入一个有cookie的chatbot": "引入一個有cookie的聊天機器人", "任何文件": "任何文件", "代码直接生效": "代碼直接生效", "高级实验性功能模块调用": "高級實驗性功能模塊調用", "修改函数插件代码后": "修改函數插件代碼後", "按Enter提交": "按Enter提交", "天蓝色": "天藍色", "子任务失败时的重试次数": "子任務失敗時的重試次數", "格式须是": "請輸入正確的格式", "调用主体": "調用主體", "有些文章的正文部分字体大小不是100%统一的": "有些文章正文中字體大小不統一", "线程": "執行緒", "是否一键更新代码": "是否一鍵更新程式碼", "除了基础的pip依赖以外": "除了基礎的pip依賴外", "紫色": "紫色", "同样支持多线程": "同樣支援多執行緒", "这个中文的句号是故意的": "這個中文句號是故意的", "获取所有文章的标题和作者": "取得所有文章的標題和作者", "Incorrect API key. OpenAI以提供了不正确的API_KEY为由": "API金鑰錯誤。OpenAI提供了錯誤的API_KEY", "绿色": "綠色", "异常": "異常", "pip install pywin32 用于doc格式": "pip install pywin32 用於doc格式", "也可以写": "也可以寫", "请对下面的文章片段用中文做一个概述": "請用中文對下面的文章片段做一個概述", "上下文管理器是一种Python对象": "上下文管理器是一種Python物件", "处理文件的上传": "處理檔案的上傳", "尝试Prompt": "嘗試Prompt", "检查USE_PROXY选项是否修改": "檢查USE_PROXY選項是否修改", "改为True应用代理": "將True更改為應用代理", "3. 如果余量太小了": "如果餘量太小", "老旧的Demo": "舊版Demo", "第一部分": "第一部分", "插件参数区": "插件參數區", "历史中哪些事件发生在": "歷史中哪些事件發生在", "现将您的现有配置移动至config_private.py以防止配置丢失": "現在將您現有的配置移動到config_private.py以防止配置丟失", "当你想发送一张照片时": "當你想發送一張照片時", "接下来请将以下代码中包含的所有中文转化为英文": "接下來請將以下代碼中包含的所有中文轉化為英文", "i_say=真正给chatgpt的提问": "i_say=真正給chatgpt的提問", "解析整个C++项目头文件": "解析整個C++項目頭文件", "需要安装pip install rarfile来解压rar文件": "需要安裝pip install rarfile來解壓rar文件", "把已经获取的数据显示出去": "顯示已經獲取的數據", "红色": "紅色", "异步任务结束": "異步任務結束", "进行学术解答": "進行學術解答", "config_private.py放自己的秘密如API和代理网址": "config_private.py放自己的秘密如API和代理網址", "学术中英互译": "學術中英互譯", "选择处理": "選擇處理", "利用以上信息": "利用以上信息", "暂时先这样顶一下": "暫時先這樣頂一下", "如果中文效果不理想": "如果中文效果不理想", "常见协议无非socks5h/http": "常見協議無非socks5h/http", "返回文本内容": "返回文本內容", "用于重组输入参数": "用於重組輸入參數", "第8步": "第8步", "可能处于折叠状态": "可能處於折疊狀態", "重置": "重置", "清除": "清除", "放到每个子线程中分别执行": "放到每個子線程中分別執行", "载入对话历史文件": "載入對話歷史文件", "列举两条并发送相关图片": "列舉兩條並發送相關圖片", "然后重试": "然後重試", "重新URL重新定向": "重新URL重新定向", "内部函数通过使用importlib模块的reload函数和inspect模块的getmodule函数来重新加载并获取函数模块": "內部函數通過使用importlib模塊的reload函數和inspect模塊的getmodule函數來重新加載並獲取函數模塊", "第一层列表是子任务分解": "第一層列表是子任務分解", "为发送请求做准备": "為發送請求做準備", "暂时没有用武之地": "暫時沒有用武之地", "并对文件中的所有函数生成注释": "並對文件中的所有函數生成註釋", "分解连字": "分解連字", "不输入文件名": "不輸入檔案名稱", "并相应地进行替换": "並相應地進行替換", "在实验过程中发现调用predict_no_ui处理长文档时": "在實驗過程中發現調用predict_no_ui處理長文檔時", "提取文本块主字体": "提取文本塊主字體", "temperature是chatGPT的内部调优参数": "temperature是chatGPT的內部調優參數", "没办法了": "沒辦法了", "获取正文主字体": "獲取正文主字體", "看门狗": "看門狗", "当前版本": "當前版本", "这个函数是用来获取指定目录下所有指定类型": "這個函數是用來獲取指定目錄下所有指定類型", "api_key已导入": "api_key已導入", "找不到任何.tex或.pdf文件": "找不到任何.tex或.pdf檔案", "You exceeded your current quota. OpenAI以账户额度不足为由": "您超出了當前配額。OpenAI以帳戶額度不足為由", "自动更新程序": "自動更新程式", "并且不要有反斜线": "並且不要有反斜線", "你必须逐个文献进行处理": "您必須逐個文獻進行處理", "本地文件地址": "本地檔案地址", "提取精炼信息": "提取精煉資訊", "设置用户名和密码": "設置使用者名稱和密碼", "请不吝PR！": "請不吝PR！", "通过把連字": "通過將連字", "文件路徑列表": "檔案路徑清單", "判定為數據流的結束": "判定為資料流的結束", "參數": "參數", "避免不小心傳github被別人看到": "避免不小心傳到github被別人看到", "記錄刪除註釋後的文本": "記錄刪除註釋後的文字", "比正文字體小": "比正文字體小", "上傳本地文件可供紅色函數插件調用": "上傳本地文件供紅色函數插件調用", "生成圖像": "生成圖像", "追加歷史": "追加歷史", "網絡代理狀態": "網絡代理狀態", "不需要再次轉化": "不需要再次轉換", "帶超時倒計時": "帶有超時倒數計時", "保存當前對話": "儲存目前對話", "等待響應": "等待回應", "依賴檢測通過": "依賴檢測通過", "如果要使用ChatGLM": "如果要使用ChatGLM", "對IPynb文件進行解析": "對IPynb檔案進行解析", "先切換模型到openai或api2d": "先切換模型到openai或api2d", "塊元提取": "區塊元素提取", "调用路径参数已自动修正到": "調用路徑參數已自動修正到", "且下一个字符为大写字母": "且下一個字符為大寫字母", "无": "無", "$c$是光速": "$c$是光速", "发送请求到OpenAI后": "發送請求到OpenAI後", "您也可以选择删除此行警告": "您也可以選擇刪除此行警告", "i_say_show_user=给用户看的提问": "i_say_show_user=給用戶看的提問", "Endpoint 重定向": "Endpoint 重定向", "基础功能区": "基礎功能區", "根据以上你自己的分析": "根據以上你自己的分析", "以上文件将被作为输入参数": "以上文件將被作為輸入參數", "已完成": "已完成", "第2次尝试": "第2次嘗試", "若输入0": "若輸入0", "自动缩减文本": "自動縮減文本", "顺利完成": "順利完成", "收到": "收到", "打开浏览器": "打開瀏覽器", "第5步": "第5步", "Free trial users的限制是每分钟3次": "Free trial users的限制是每分鐘3次", "请用markdown格式输出": "請用 Markdown 格式輸出", "模仿ChatPDF": "模仿 ChatPDF", "等待多久判定为超时": "等待多久判定為超時", "请结合互联网信息回答以下问题": "請結合互聯網信息回答以下問題", "IP查询频率受限": "IP查詢頻率受限", "高级参数输入区的显示提示": "高級參數輸入區的顯示提示", "的高级参数说明": "的高級參數說明", "默认开启": "默認開啟", "为实现更多强大的功能做基础": "為實現更多強大的功能做基礎", "中文学术润色": "中文學術潤色", "注意这里的历史记录被替代了": "注意這裡的歷史記錄被替代了", "子线程任务": "子線程任務", "个": "個", "正在加载tokenizer": "正在加載 tokenizer", "生成http请求": "生成 HTTP 請求", "从而避免解析压缩文件": "從而避免解析壓縮文件", "加载参数": "加載參數", "由于输入长度限制": "由於輸入長度限制", "如果直接在海外服务器部署": "如果直接在海外伺服器部署", "你提供了错误的API_KEY": "你提供了錯誤的API_KEY", "history 是之前的对话列表": "history 是之前的對話列表", "实现更换API_URL的作用": "實現更換API_URL的作用", "Json解析不合常规": "Json解析不合常規", "函数插件-下拉菜单与随变按钮的互动": "函數插件-下拉菜單與隨變按鈕的互動", "则先将公式转换为HTML格式": "則先將公式轉換為HTML格式", "1. 临时解决方案": "1. 臨時解決方案", "如1812.10695": "如1812.10695", "最后用中文翻译摘要部分": "最後用中文翻譯摘要部分", "MOSS响应异常": "MOSS響應異常", "读取pdf文件": "讀取pdf文件", "重试的次数限制": "重試的次數限制", "手动指定询问哪些模型": "手動指定詢問哪些模型", "情况会好转": "情況會好轉", "超过512个": "超過512個", "多线": "多線", "合并小写字母开头的段落块并替换为空格": "合併小寫字母開頭的段落塊並替換為空格", "暗色主题": "暗色主題", "提高限制请查询": "提高限制請查詢", "您还需要运行": "您還需要執行", "将双空行": "將雙空行", "请削减单次输入的文本量": "請減少單次輸入的文本量", "提高语法、清晰度和整体可读性": "提高語法、清晰度和整體可讀性", "删除其中的所有注释": "刪除其中的所有註釋", "列表长度为子任务的数量": "列表長度為子任務的數量", "直接在输入区键入api_key": "直接在輸入區鍵入api_key", "方法会在代码块被执行前被调用": "方法會在代碼塊被執行前被調用", "懂的都懂": "懂的都懂", "加一个live2d装饰": "加一個live2d裝飾", "请从中提取出“标题”、“收录会议或期刊”、“作者”、“摘要”、“编号”、“作者邮箱”这六个部分": "請從中提取出“標題”、“收錄會議或期刊”、“作者”、“摘要”、“編號”、“作者郵箱”這六個部分", "聊天历史": "聊天歷史", "将插件中出的所有问题显示在界面上": "將插件中出的所有問題顯示在界面上", "每个子任务的输入": "每個子任務的輸入", "yield一次以刷新前端页面": "yield一次以刷新前端頁面", "不能自定义字体和颜色": "不能自定義字體和顏色", "如果本地使用不建议加这个": "如果本地使用不建議加這個", "例如chatglm&gpt-3.5-turbo&api2d-gpt-4": "例如chatglm&gpt-3.5-turbo&api2d-gpt-4", "尝试": "嘗試", "什么都没有": "什麼都沒有", "代理设置": "代理設置", "请求处理结束": "請求處理結束", "将结果写入markdown文件中": "將結果寫入markdown文件中", "experiment等": "實驗等", "添加一个萌萌的看板娘": "添加一個萌萌的看板娘", "现在": "現在", "当前软件运行的端口号": "當前軟件運行的端口號", "第n组插件": "第n組插件", "不受git管控": "不受git管控", "基础功能区的回调函数注册": "基礎功能區的回調函數註冊", "句子结束标志": "句子結束標誌", "GPT参数": "GPT參數", "按输入的匹配模式寻找上传的非压缩文件和已解压的文件": "按輸入的匹配模式尋找上傳的非壓縮文件和已解壓的文件", "函数插件贡献者": "函數插件貢獻者", "用户提示": "用戶提示", "此版本使用pdfminer插件": "此版本使用pdfminer插件", "如果换行符前为句子结束标志": "如果換行符前為句子結束標誌", "在gpt输出代码的中途": "在gpt輸出代碼的中途", "中转网址预览": "中轉網址預覽", "自动截断": "自動截斷", "当無法用標點、空行分割時": "當無法用標點、空行分割時", "意外Json結構": "意外Json結構", "需要讀取和清理文本的pdf文件路徑": "需要讀取和清理文本的pdf文件路徑", "HotReload的裝飾器函數": "HotReload的裝飾器函數", "chatGPT 分析報告": "chatGPT 分析報告", "如參考文獻、腳註、圖註等": "如參考文獻、腳註、圖註等", "的api-key": "的api-key", "第二組插件": "第二組插件", "當前代理可用性": "當前代理可用性", "列表遞歸接龍": "列表遞歸接龍", "這個bug沒找到觸發條件": "這個bug沒找到觸發條件", "喚起高級參數輸入區": "喚起高級參數輸入區", "但大部分場合下並不需要修改": "但大部分場合下並不需要修改", "盡量是完整的一個section": "盡量選擇完整的一個章節", "如果OpenAI不響應": "如果OpenAI不響應", "等文本特殊符號轉換為其基本形式來對文本進行歸一化處理": "等文本特殊符號轉換為其基本形式來對文本進行歸一化處理", "你的回答必須簡單明了": "你的回答必須簡單明了", "對話歷史文件損壞！": "對話歷史文件損壞！", "每一塊": "每一塊", "如果某個子任務出錯": "如果某個子任務出錯", "切分和重新整合": "切分和重新整合", "Token限制下的截断与处理": "Token限制下的截斷與處理", "仅支持Win平台": "僅支持Win平臺", "并行任务数量限制": "並行任務數量限制", "已重置": "已重置", "如果要使用Newbing": "如果要使用Newbing", "前言": "前言", "理解PDF论文内容": "理解PDF論文內容", "如果有的话": "如果有的話", "功能区显示开关与功能区的互动": "功能區顯示開關與功能區的互動", "前者API2D的": "前者API2D的", "如果要使用MOSS": "如果要使用MOSS", "源文件太多": "源文件太多", "ChatGLM尚未加载": "ChatGLM尚未加載", "不可高于3": "不可高於3", "运行方法 python crazy_functions/crazy_functions_test.py": "運行方法 python crazy_functions/crazy_functions_test.py", "清除历史": "清除歷史", "如果要使用jittorllms": "如果要使用jittorllms", "更换模型 & SysPrompt & 交互界面布局": "更換模型 & SysPrompt & 交互界面布局", "是之前的对话列表": "是之前的對話列表", "开始了吗": "開始了嗎", "输入": "輸入", "打开你的*学*网软件查看代理的协议": "打開你的*學*網軟件查看代理的協議", "默认False": "默認False", "获取页面上的文本信息": "獲取頁面上的文本信息", "第一页清理后的文本内容列表": "第一頁清理後的文本內容列表", "并定义了一个名为decorated的内部函数": "並定義了一個名為decorated的內部函數", "你是一个学术翻译": "你是一個學術翻譯", "OpenAI拒绝了请求": "OpenAI拒絕了請求", "提示": "提示", "返回重试": "返回重試", "以下“红颜色”标识的函数插件需从输入区读取路径作为参数": "以下“紅顏色”標識的函數插件需從輸入區讀取路徑作為參數", "这个函数用stream的方式解决这个问题": "這個函數用stream的方式解決這個問題", "ChatGPT 学术优化": "ChatGPT 學術優化", "去除短块": "去除短塊", "第一组插件": "第一組插件", "这是什么": "這是什麼", "在传递chatbot的过程中不要将其丢弃": "在傳遞chatbot的過程中不要將其丟棄", "下载PDF文档": "下載PDF文檔", "以下是信息源": "以下是信息源", "本组文件为": "本組檔案為", "更新函数代码": "更新函數代碼", "解析的结果如下": "解析的結果如下", "逻辑较乱": "邏輯較亂", "存入": "存入", "具备完备的交互功能": "具備完備的交互功能", "安装jittorllms依赖后将完全破坏现有的pytorch环境": "安裝jittorllms依賴後將完全破壞現有的pytorch環境", "看门狗的耐心": "看門狗的耐心", "点击展开“文件上传区”": "點擊展開“文件上傳區”", "翻译摘要等": "翻譯摘要等", "返回值": "返回值", "默认允许多少路线程同时访问OpenAI": "默認允許多少路線程同時訪問OpenAI", "这是第": "這是第", "把本项目源代码切换成全英文": "把本項目源代碼切換成全英文", "找不到任何html文件": "找不到任何html文件", "假如重启失败": "假如重啟失敗", "感谢热情的": "感謝熱情的", "您若希望分享新的功能模组": "您若希望分享新的功能模組", "并在新模块中重新加载函数": "並在新模塊中重新加載函數", "则会在溢出时暴力截断": "則會在溢出時暴力截斷", "源码自译解": "原始碼自譯解", "开始正式执行任务": "開始正式執行任務", "ChatGLM响应异常": "ChatGLM響應異常", "用户界面对话窗口句柄": "用戶界面對話窗口句柄", "左右布局": "左右佈局", "后面两句是": "後面兩句是", "可同时填写多个API-KEY": "可同時填寫多個API-KEY", "对各个llm模型进行单元测试": "對各個llm模型進行單元測試", "为了更好的效果": "為了更好的效果", "jittorllms 没有 sys_prompt 接口": "jittorllms沒有sys_prompt接口", "直接取出来": "直接取出來", "不具备多线程能力的函数": "不具備多線程能力的函數", "单行 + 字体大": "單行+字體大", "正在分析一个源代码项目": "正在分析一個源代碼項目", "直接退出": "直接退出", "稍后可能需要再试一次": "稍後可能需要再試一次", "开始重试": "開始重試", "没有 sys_prompt 接口": "沒有sys_prompt接口", "只保留文件名节省token": "只保留文件名節省token", "肯定已经都结束了": "肯定已經都結束了", "用&符號分隔": "&", "但本地存儲了以下歷史文件": "以下是本地儲存的歷史文件清單", "對全文進行概括": "全文概述", "以下是一篇學術論文的基礎信息": "以下是學術論文的基本信息", "正在提取摘要並下載PDF文檔……": "正在提取摘要並下載PDF文件……", "1. 對原始文本進行歸一化處理": "1. 正規化原始文本", "問題": "問題", "用於基礎的對話功能": "用於基礎的對話功能", "獲取設置": "獲取設置", "如果缺少依賴": "如果缺少依賴項", "第6步": "第6步", "處理markdown文本格式的轉變": "處理Markdown文本格式轉換", "功能、貢獻者": "功能、貢獻者", "中文Latex項目全文潤色": "中文LaTeX項目全文潤色", "等待newbing回復的片段": "等待newbing回復的片段", "寫入文件": "寫入文件", "下載pdf文件未成功": "下載PDF文件失敗", "將生成的報告自動投射到文件上傳區": "將生成的報告自動上傳到文件區", "函數插件作者": "函數插件作者", "將要匹配的模式": "將要匹配的模式", "正在分析一个项目的源代码": "正在分析一個專案的源代碼", "使每个段落之间有两个换行符分隔": "使每個段落之間有兩個換行符分隔", "并在被装饰的函数上执行": "並在被裝飾的函數上執行", "更新完成": "更新完成", "请先把模型切换至gpt-xxxx或者api2d-xxxx": "請先把模型切換至gpt-xxxx或者api2d-xxxx", "结果写入文件": "結果寫入文件", "在执行过程中遭遇问题": "在執行過程中遭遇問題", "找不到任何文件": "找不到任何文件", "给gpt的静默提醒": "給gpt的靜默提醒", "远程返回错误": "遠程返回錯誤", "例如\\section": "例如\\section", "该函数详细注释已添加": "該函數詳細注釋已添加", "对文本进行归一化处理": "對文本進行歸一化處理", "注意目前不能多人同时调用NewBing接口": "注意目前不能多人同時調用NewBing接口", "来保留函数的元信息": "來保留函數的元信息", "一般是文本过长": "一般是文本過長", "切割PDF": "切割PDF", "开始下一个循环": "開始下一個循環", "正在开始汇总": "正在開始匯總", "建议使用docker环境！": "建議使用docker環境！", "质能方程是描述质量与能量之间的当量关系的方程": "質能方程是描述質量與能量之間的當量關係的方程", "子进程执行": "子進程執行", "清理后的文本内容字符串": "清理後的文本內容字串", "石板色": "石板色", "Bad forward key. API2D账户额度不足": "Bad forward key. API2D帳戶額度不足", "摘要在 .gs_rs 中的文本": "摘要在 .gs_rs 中的文本", "请复制并转到以下URL": "請複製並轉到以下URL", "然后用for+append循环重新赋值": "然後用for+append循環重新賦值", "文章极长": "文章極長", "请从数据中提取信息": "請從數據中提取信息", "为了安全而隐藏绝对地址": "為了安全而隱藏絕對地址", "OpenAI绑了信用卡的用户可以填 16 或者更高": "OpenAI綁了信用卡的用戶可以填 16 或者更高", "gpt4现在只对申请成功的人开放": "gpt4現在只對申請成功的人開放", "问号": "問號", "并合并为一个字符串": "並合併為一個字串", "文件上传区": "文件上傳區", "这个函数运行在主进程": "這個函數運行在主進程", "执行中": "執行中", "修改函数插件后": "修改函數插件後", "请你阅读以下学术论文相关的材料": "請你閱讀以下學術論文相關的材料", "加载需要一段时间": "加載需要一段時間", "单线程": "單線程", "5s之后重启": "5秒後重啟", "文件名是": "文件名是", "主进程执行": "主進程執行", "如何理解传奇?": "如何理解傳奇?", "解析整个Java项目": "解析整個Java項目", "已成功": "已成功", "该函数面向希望实现更多有趣功能的开发者": "該函數面向希望實現更多有趣功能的開發者", "代理所在地": "代理所在地", "解析Jupyter Notebook文件": "解析Jupyter Notebook文件", "观测窗": "觀測窗", "更好的UI视觉效果": "更好的UI視覺效果", "在此处替换您要搜索的关键词": "在此處替換您要搜索的關鍵詞", "Token溢出": "Token溢出", "这段代码来源 https": "這段代碼來源 https", "请求超时": "請求超時", "已经被转化过": "已經被轉化過", "LLM_MODEL 格式不正确！": "LLM_MODEL 格式不正確！", "先输入问题": "請輸入問題", "灰色": "灰色", "锌色": "鋅色", "里面包含以指定类型为后缀名的所有文件的绝对路径": "包含指定類型後綴名的所有文件的絕對路徑", "实现插件的热更新": "實現插件的熱更新", "请对下面的文章片段用中文做概述": "請用中文概述下面的文章片段", "如果需要在二级路径下运行": "如果需要在二級路徑下運行", "的分析如下": "的分析如下", "但端口号都应该在最显眼的位置上": "但端口號都應該在最顯眼的位置上", "当输入部分的token占比小于限制的3/4时": "當輸入部分的token占比小於限制的3/4時", "第一次运行": "第一次運行", "失败了": "失敗了", "如果包含数学公式": "如果包含數學公式", "需要配合修改main.py才能生效!": "需要配合修改main.py才能生效！", "它的作用是……额……就是不起作用": "它的作用是......额......就是不起作用", "通过裁剪来缩短历史记录的长度": "通過裁剪來縮短歷史記錄的長度", "chatGPT对话历史": "chatGPT對話歷史", "它可以作为创建新功能函数的模板": "它可以作為創建新功能函數的模板", "生成一个请求线程": "生成一個請求線程", "$m$是质量": "$m$是質量", "；4、引用数量": "；4、引用數量", "NewBing响应缓慢": "NewBing響應緩慢", "提交": "提交", "test_联网回答问题": "test_聯網回答問題", "加载tokenizer完毕": "加載tokenizer完畢", "HotReload 的意思是热更新": "HotReload 的意思是熱更新", "随便显示点什么防止卡顿的感觉": "隨便顯示點什麼防止卡頓的感覺", "对整个Markdown项目进行翻译": "對整個Markdown項目進行翻譯", "替换操作": "替換操作", "然后通过getattr函数获取函数名": "然後通過getattr函數獲取函數名", "并替换为空字符串": "並替換為空字符串", "逐个文件分析已完成": "逐個文件分析已完成", "填写之前不要忘记把USE_PROXY改成True": "填寫之前不要忘記把USE_PROXY改成True", "不要遗漏括号": "不要遺漏括號", "避免包括解释": "避免包括解釋", "把newbing的长长的cookie放到这里": "把newbing的長長的cookie放到這裡", "如API和代理网址": "如API和代理網址", "模块预热": "模塊預熱", "Latex项目全文英译中": "Latex項目全文英譯中", "尝试计算比例": "嘗試計算比例", "OpenAI所允許的最大並行過載": "OpenAI所允許的最大並行過載", "向chatbot中添加簡單的意外錯誤信息": "向chatbot中添加簡單的意外錯誤信息", "history至少釋放二分之一": "history至少釋放二分之一", "”補上": "”補上", "我們剝離Introduction之後的部分": "我們剝離Introduction之後的部分", "嘗試加載": "嘗試加載", "**函數功能**": "**函數功能**", "藍色": "藍色", "重置文件的創建時間": "重置文件的創建時間", "再失敗就沒辦法了": "再失敗就沒辦法了", "解析整個Python項目": "解析整個Python項目", "此處不修改": "此處不修改", "安裝ChatGLM的依賴": "安裝ChatGLM的依賴", "使用wraps": "使用wraps", "優先級1. 獲取環境變量作為配置": "優先級1. 獲取環境變量作為配置", "遞歸地切割PDF文件": "遞歸地切割PDF文件", "隨變按鈕的回調函數註冊": "隨變按鈕的回調函數註冊", "我們": "我們", "然後請使用Markdown格式封裝": "然後請使用Markdown格式封裝", "網絡的遠程文件": "網絡的遠程文件", "主进程统一调用函数接口": "主進程統一調用函數介面", "请按以下描述给我发送图片": "請按以下描述給我發送圖片", "正常对话时使用": "正常對話時使用", "不需要高级参数": "不需要高級參數", "双换行": "雙換行", "初始值是摘要": "初始值是摘要", "已经对该文章的所有片段总结完毕": "已經對該文章的所有片段總結完畢", "proxies格式错误": "proxies格式錯誤", "一次性完成": "一次性完成", "设置一个token上限": "設置一個token上限", "接下来": "接下來", "以_array结尾的输入变量都是列表": "以_array結尾的輸入變量都是列表", "收到以下文件": "收到以下文件", "但显示Token不足": "但顯示Token不足", "可以多线程并行": "可以多線程並行", "带Cookies的Chatbot类": "帶Cookies的Chatbot類", "空空如也的输入栏": "空空如也的輸入欄", "然后回车键提交后即可生效": "然後回車鍵提交後即可生效", "这是必应": "這是必應", "聊天显示框的句柄": "聊天顯示框的句柄", "集合文件": "集合文件", "并显示到聊天当中": "並顯示到聊天當中", "设置5秒即可": "設置5秒即可", "不懂就填localhost或者127.0.0.1肯定错不了": "不懂就填localhost或者127.0.0.1肯定錯不了", "安装方法": "安裝方法", "Openai 限制免费用户每分钟20次请求": "Openai 限制免費用戶每分鐘20次請求", "建议": "建議", "将普通文本转换为Markdown格式的文本": "將普通文本轉換為Markdown格式的文本", "应急食品是“原神”游戏中的角色派蒙的外号": "應急食品是“原神”遊戲中的角色派蒙的外號", "不要修改!!": "不要修改!!", "注意无论是inputs还是history": "注意無論是inputs還是history", "读取Latex文件": "讀取Latex文件", "\\n 翻译": "\\n 翻譯", "第 1 步": "第 1 步", "代理配置": "代理配置", "temperature是LLM的内部调优参数": "temperature是LLM的內部調優參數", "解析整个Lua项目": "解析整個Lua項目", "重试几次": "重試幾次", "接管gradio默认的markdown处理方式": "接管gradio默認的markdown處理方式", "请注意自我隐私保护哦！": "請注意自我隱私保護哦！", "导入软件依赖失败": "導入軟件依賴失敗", "方便调试和定位问题": "方便調試和定位問題", "请用代码块输出代码": "請用代碼塊輸出代碼", "字符数小于100": "字符數小於100", "程序终止": "程序終止", "处理历史信息": "處理歷史信息", "在界面上显示结果": "在界面上顯示結果", "自动定位": "自動定位", "读Tex论文写摘要": "讀Tex論文寫摘要", "截断时的颗粒度": "截斷時的顆粒度", "第 4 步": "第 4 步", "正在处理中": "正在處理中", "酸橙色": "酸橙色", "分别为 __enter__": "分別為 __enter__", "Json异常": "Json異常", "输入过长已放弃": "輸入過長已放棄", "按照章节切割PDF": "按照章節切割PDF", "作为切分点": "作為切分點", "用一句话概括程序的整体功能": "用一句話概括程序的整體功能", "PDF文件也已经下载": "PDF文件也已經下載", "您可能选择了错误的模型或请求源": "您可能選擇了錯誤的模型或請求源", "则终止": "則終止", "完成了吗": "完成了嗎", "表示要搜索的文件类型": "表示要搜索的文件類型", "文件内容是": "文件內容是", "亮色主题": "亮色主題", "函数插件输入输出接驳区": "函數插件輸入輸出接驳區", "异步任务开始": "異步任務開始", "Index 2 框框": "索引 2 框框", "方便实现复杂的功能逻辑": "方便實現複雜的功能邏輯", "警告": "警告", "放在这里": "放在這裡", "处理中途中止的情况": "處理中途中止的情況", "结尾除去一次": "結尾除去一次", "代码开源和更新": "代碼開源和更新", "列表": "列表", "状态": "狀態", "第9步": "第9步", "的标识": "的標識", "Call jittorllms fail 不能正常加载jittorllms的参数": "Call jittorllms 失敗 不能正常加載 jittorllms 的參數", "中性色": "中性色", "优先": "優先", "读取配置": "讀取配置", "jittorllms消耗大量的内存": "jittorllms消耗大量的內存", "Latex项目全文中译英": "Latex項目全文中譯英", "在代理软件的设置里找": "在代理軟件的設置裡找", "否则将导致每个人的NewBing问询历史互相渗透": "否則將導致每個人的NewBing問詢歷史互相滲透", "这个函数运行在子进程": "這個函數運行在子進程", "2. 长效解决方案": "2. 長效解決方案", "Windows上还需要安装winrar软件": "Windows上還需要安裝winrar軟件", "正在执行一些模块的预热": "正在執行一些模塊的預熱", "一键DownloadArxivPapersAndTranslateAbstract": "一鍵DownloadArxivPapersAndTranslateAbstract", "完成全部响应": "完成全部響應", "输入中可能存在乱码": "輸入中可能存在亂碼", "用了很多trick": "用了很多trick", "填写格式是": "填寫格式是", "预处理一波": "預處理一波", "如果只询问1个大语言模型": "如果只詢問1個大語言模型", "第二部分": "第二部分", "或历史数据过长. 历史缓存数据已部分释放": "或歷史數據過長. 歷史緩存數據已部分釋放", "文章内容是": "文章內容是", "二、论文翻译": "二、論文翻譯", "汇总报告已经添加到右侧“文件上传区”": "匯總報告已經添加到右側“檔案上傳區”", "图像中转网址": "圖像中轉網址", "第4次尝试": "第4次嘗試", "越新越好": "越新越好", "解决一个mdx_math的bug": "解決一個mdx_math的bug", "中间过程不予显示": "中間過程不予顯示", "路径或网址": "路徑或網址", "您可以试试让AI写一个Related Works": "您可以試試讓AI寫一個Related Works", "开始接收chatglm的回复": "開始接收chatglm的回覆", "环境变量可以是": "環境變數可以是", "请将此部分润色以满足学术标准": "請將此部分潤色以滿足學術標準", "* 此函数未来将被弃用": "* 此函數未來將被棄用", "替换其他特殊字符": "替換其他特殊字元", "该模板可以实现ChatGPT联网信息综合": "該模板可以實現ChatGPT聯網資訊綜合", "当前问答": "當前問答", "洋红色": "洋紅色", "不需要重启程序": "不需要重啟程式", "所有线程同时开始执行任务函数": "所有線程同時開始執行任務函數", "因此把prompt加入 history": "因此將prompt加入歷史", "刷新界面": "重新整理介面", "青色": "藍綠色", "实时在UI上反馈远程数据流": "即時在UI上回饋遠程數據流", "第一种情况": "第一種情況", "的耐心": "的耐心", "提取所有块元的文本信息": "提取所有塊元的文本信息", "裁剪时": "裁剪時", "对从 PDF 提取出的原始文本进行清洗和格式化处理": "對從PDF提取出的原始文本進行清洗和格式化處理", "如果是第一次运行": "如果是第一次運行", "程序完成": "程式完成", "api-key不满足要求": "API金鑰不滿足要求", "布尔值": "布林值", "尝试导入依赖": "嘗試匯入相依性", "逐个文件分析": "逐個檔案分析", "详情见get_full_error的输出": "詳情見get_full_error的輸出", "检测到": "偵測到", "手动指定和筛选源代码文件类型": "手動指定和篩選原始程式碼檔案類型", "进入任务等待状态": "進入任務等待狀態", "当 输入部分的token占比 小于 全文的一半时": "當輸入部分的token佔比小於全文的一半時", "查询代理的地理位置": "查詢代理的地理位置", "是否在输入过长时": "是否在輸入過長時", "chatGPT分析报告": "chatGPT分析報告", "然后yield出去": "然後yield出去", "用户取消了程序": "使用者取消了程式", "琥珀色": "琥珀色", "这里是特殊函数插件的高级参数输入区": "這裡是特殊函數插件的高級參數輸入區", "第 2 步": "第 2 步", "字符串": "字串", "检测到程序终止": "偵測到程式終止", "对整个Latex项目进行润色": "對整個Latex專案進行潤色", "方法则会被调用": "方法則會被調用", "把完整输入-输出结果显示在聊天框": "把完整輸入-輸出結果顯示在聊天框", "本地文件预览": "本地檔案預覽", "接下来请你逐文件分析下面的论文文件": "接下來請你逐檔案分析下面的論文檔案", "英语关键词": "英語關鍵詞", "一-鿿": "一-鿿", "尝试识别section": "嘗試識別section", "用于显示给用户": "用於顯示給使用者", "newbing回复的片段": "newbing回覆的片段", "的转化": "的轉換", "将要忽略匹配的文件名": "將要忽略匹配的檔案名稱", "生成正则表达式": "生成正則表示式", "失败时的重试次数": "失敗時的重試次數", "亲人两行泪": "親人兩行淚", "故可以只分析文章内容": "故可以只分析文章內容", "然后回车提交": "然後按下Enter提交", "并提供改进建议": "並提供改進建議", "不可多线程": "不可多執行緒", "这个文件用于函数插件的单元测试": "這個檔案用於函數插件的單元測試", "用一张Markdown表格简要描述以下文件的功能": "用一張Markdown表格簡要描述以下檔案的功能", "可用clear将其清空": "可用clear將其清空", "发送至LLM": "發送至LLM", "先在input输入编号": "先在input輸入編號", "更新失败": "更新失敗", "相关功能不稳定": "相關功能不穩定", "自动解压": "自動解壓", "效果奇好": "效果奇佳", "拆分过长的IPynb文件": "拆分過長的IPynb檔案", "份搜索结果": "搜尋結果", "如果没有指定文件名": "如果沒有指定檔案名稱", "有$标识的公式符号": "有$標識的公式符號", "跨平台": "跨平台", "最终": "最終", "第3次尝试": "第三次嘗試", "检查代理服务器是否可用": "檢查代理伺服器是否可用", "再例如一个包含了待处理文件的路径": "再例如一個包含了待處理檔案的路徑", "注意文章中的每一句话都要翻译": "注意文章中的每一句話都要翻譯", "修改它": "修改它", "发送 GET 请求": "發送 GET 請求", "判定为不是正文": "判定為不是正文", "默认是.md": "預設是.md", "终止按钮的回调函数注册": "終止按鈕的回調函數註冊", "搜索需要处理的文件清单": "搜尋需要處理的檔案清單", "当历史上下文过长时": "當歷史上下文過長時", "不包含任何可用于": "不包含任何可用於", "本项目现已支持OpenAI和API2D的api-key": "本專案現已支援OpenAI和API2D的api-key", "异常原因": "異常原因", "additional_fn代表点击的哪个按钮": "additional_fn代表點擊的哪個按鈕", "注意": "注意", "找不到任何.docx或doc文件": "找不到任何.docx或doc文件", "刷新用户界面": "刷新使用者介面", "失败": "失敗", "Index 0 文本": "索引 0 文本", "你需要翻译以下内容": "你需要翻譯以下內容", "chatglm 没有 sys_prompt 接口": "chatglm 沒有 sys_prompt 介面", "您的 API_KEY 是": "您的 API_KEY 是", "请缩减输入文件的数量": "請減少輸入檔案的數量", "并且将结合上下文内容": "並且將結合上下文內容", "返回当前系统中可用的未使用端口": "返回目前系統中可用的未使用埠口", "以下配置可以优化体验": "以下配置可以優化體驗", "常规情况下": "一般情況下", "递归": "遞迴", "分解代码文件": "分解程式碼檔案", "用户反馈": "使用者回饋", "第 0 步": "第 0 步", "即将更新pip包依赖……": "即將更新pip套件相依性......", "请从": "請從", "第二种情况": "第二種情況", "NEWBING_COOKIES未填寫或有格式錯誤": "NEWBING_COOKIES未填寫或格式錯誤", "以上材料已經被寫入": "以上材料已經被寫入", "找圖片": "尋找圖片", "函數插件-固定按鈕區": "函數插件-固定按鈕區", "該文件中主要包含三個函數": "該文件主要包含三個函數", "用於與with語句一起使用": "用於與with語句一起使用", "插件初始化中": "插件初始化中", "文件讀取完成": "文件讀取完成", "讀取文件": "讀取文件", "高危設置！通過修改此設置": "高危設置！通過修改此設置", "所有文件都總結完成了嗎": "所有文件都總結完成了嗎", "限制的3/4時": "限制的3/4時", "取決於": "取決於", "預處理": "預處理", "至少一個線程任務Token溢出而失敗": "至少一個線程任務Token溢出而失敗", "一、論文概況": "一、論文概況", "TGUI不支持函數插件的實現": "TGUI不支持函數插件的實現", "拒絕服務": "拒絕服務", "請更換為API_URL_REDIRECT配置": "請更換為API_URL_REDIRECT配置", "是否自動處理token溢出的情況": "是否自動處理token溢出的情況", "和": "和", "双层列表": "雙層列表", "做一些外观色彩上的调整": "做一些外觀色彩上的調整", "发送请求到子进程": "發送請求到子進程", "配置信息如下": "配置信息如下", "从而实现分批次处理": "從而實現分批次處理", "找不到任何.ipynb文件": "找不到任何.ipynb文件", "代理网络的地址": "代理網絡的地址", "新版本": "新版本", "用于实现Python函数插件的热更新": "用於實現Python函數插件的熱更新", "将中文句号": "將中文句號", "警告！被保存的对话历史可以被使用该系统的任何人查阅": "警告！被保存的對話歷史可以被使用該系統的任何人查閱", "用于数据流可视化": "用於數據流可視化", "第三部分": "第三部分", "界面更新": "界面更新", "**输出参数说明**": "**輸出參數說明**", "其中$E$是能量": "其中$E$是能量", "这个内部函数可以将函数的原始定义更新为最新版本": "這個內部函數可以將函數的原始定義更新為最新版本", "不要修改任何LaTeX命令": "不要修改任何LaTeX命令", "英译中": "英譯中", "将错误显示出来": "顯示錯誤", "*代表通配符": "*代表通配符", "找不到任何lua文件": "找不到任何lua文件", "准备文件的下载": "準備下載文件", "爬取搜索引擎的结果": "爬取搜尋引擎的結果", "例如在windows cmd中": "例如在windows cmd中", "一般原样传递下去就行": "一般原樣傳遞下去就行", "免费用户填3": "免費用戶填3", "在汇总报告中隐藏啰嗦的真实输入": "在匯總報告中隱藏啰嗦的真實輸入", "Tiktoken未知错误": "Tiktoken未知錯誤", "整理结果": "整理結果", "也许等待十几秒后": "也許等待十幾秒後", "将匹配到的数字作为替换值": "將匹配到的數字作為替換值", "对每一个源代码文件": "對每一個源代碼文件", "补上后面的": "補上後面的", "调用时": "調用時", "也支持同时填写多个api-key": "也支持同時填寫多個api-key", "第二层列表是对话历史": "第二層列表是對話歷史", "询问多个GPT模型": "詢問多個GPT模型", "您可能需要手动安装新增的依赖库": "您可能需要手動安裝新增的依賴庫", "隨機負載均衡": "隨機負載均衡", "等待多線程操作": "等待多線程操作", "質能方程式": "質能方程式", "需要預先pip install py7zr": "需要預先pip install py7zr", "是否丟棄掉 不是正文的內容": "是否丟棄掉 不是正文的內容", "加載失敗!": "加載失敗!", "然後再寫一段英文摘要": "然後再寫一段英文摘要", "從以上搜索結果中抽取信息": "從以上搜索結果中抽取信息", "response中會攜帶traceback報錯信息": "response中會攜帶traceback報錯信息", "放到history中": "放到history中", "不能正常加載jittorllms的參數！": "不能正常加載jittorllms的參數！", "需要預先pip install rarfile": "需要預先pip install rarfile", "以免輸入溢出": "以免輸入溢出", "MOSS消耗大量的內存": "MOSS消耗大量的內存", "獲取預處理函數": "獲取預處理函數", "缺少MOSS的依賴": "缺少MOSS的依賴", "多線程": "多線程", "結束": "結束", "請使用Markdown": "請使用Markdown", "匹配^數字^": "匹配^數字^", "负责把学术论文准确翻译成中文": "負責將學術論文準確翻譯成中文", "否则可能导致显存溢出而造成卡顿": "否則可能導致顯存溢出而造成卡頓", "不输入即全部匹配": "不輸入即全部匹配", "下面是一些学术文献的数据": "下面是一些學術文獻的數據", "网络卡顿、代理失败、KEY失效": "網絡卡頓、代理失敗、KEY失效", "其他的排队等待": "其他的排隊等待", "表示要搜索的文件或者文件夹路径或网络上的文件": "表示要搜索的文件或者文件夾路徑或網絡上的文件", "当输入部分的token占比": "當輸入部分的token佔比", "你的任务是改进所提供文本的拼写、语法、清晰、简洁和整体可读性": "你的任務是改進所提供文本的拼寫、語法、清晰、簡潔和整體可讀性", "这是什么功能": "這是什麼功能", "剩下的情况都开头除去": "剩下的情況都開頭除去", "清除换行符": "清除換行符", "请提取": "請提取", "覆盖和重启": "覆蓋和重啟", "发送至chatGPT": "發送至chatGPT", "+ 已经汇总的文件组": "+ 已經匯總的文件組", "插件": "插件", "OpenAI模型选择是": "OpenAI模型選擇是", "原文": "原文", "您可以随时在history子文件夹下找回旧版的程序": "您可以隨時在history子文件夾下找回舊版的程序", "以确保一些资源在代码块执行期间得到正确的初始化和清理": "以確保一些資源在程式碼區塊執行期間得到正確的初始化和清理", "它们会继续向下调用更底层的LLM模型": "它們會繼續向下調用更底層的LLM模型", "GPT输出格式错误": "GPT輸出格式錯誤", "中译英": "中譯英", "无代理状态下很可能无法访问OpenAI家族的模型": "無代理狀態下很可能無法訪問OpenAI家族的模型", "已失败": "已失敗", "最大线程数": "最大線程數", "读取时首先看是否存在私密的config_private配置文件": "讀取時首先看是否存在私密的config_private配置文件", "必要时": "必要時", "在装饰器内部": "在裝飾器內部", "api2d 正常完成": "api2d 正常完成", "您可以调用“LoadConversationHistoryArchive”还原当下的对话": "您可以調用“LoadConversationHistoryArchive”還原當下的對話", "找不到任何golang文件": "找不到任何golang文件", "找不到任何rust文件": "找不到任何rust文件", "输入了已经经过转化的字符串": "輸入了已經經過轉換的字串", "是否在结束时": "是否在結束時", "存档文件详情": "存檔文件詳情", "用英文逗号分割": "用英文逗號分割", "已删除": "已刪除", "收到消息": "收到訊息", "系统输入": "系統輸入", "读取配置文件": "讀取配置檔", "跨线程传递": "跨線程傳遞", "Index 1 字体": "索引 1 字型", "设定一个最小段落长度阈值": "設定最小段落長度閾值", "流式获取输出": "流式取得輸出", "默认按钮颜色是 secondary": "預設按鈕顏色為 secondary", "请对下面的程序文件做一个概述": "請對下面的程式檔案做一個概述", "当文件被上传时的回调函数": "當檔案被上傳時的回撥函數", "对话窗的高度": "對話窗的高度", "Github更新地址": "Github更新位址", "然后在用常规的": "然後再用常規的", "读取Markdown文件": "讀取Markdown檔案", "会把列表拆解": "會拆解列表", "OpenAI绑定信用卡可解除频率限制": "OpenAI綁定信用卡可解除頻率限制", "可能需要一点时间下载参数": "可能需要一點時間下載參數", "需要访问谷歌": "需要訪問谷歌", "根据给定的匹配结果来判断换行符是否表示段落分隔": "根據給定的匹配結果來判斷換行符是否表示段落分隔", "请提交新问题": "請提交新問題", "测试功能": "測試功能", "尚未充分测试的函数插件": "尚未充分測試的函數插件", "解析此项目本身": "解析此專案本身", "提取摘要": "提取摘要", "用于输入给GPT的前提提示": "用於輸入給GPT的前提提示", "第一步": "第一步", "此外": "此外", "找不到任何前端相关文件": "找不到任何前端相關檔案", "输入其他/无输入+回车=不更新": "輸入其他/無輸入+回車=不更新", "句号": "句號", "如果最后成功了": "如果最後成功了", "导致输出不完整": "導致輸出不完整", "并修改代码拆分file_manifest列表": "並修改程式碼拆分file_manifest列表", "在读取API_KEY时": "在讀取API_KEY時", "迭代地历遍整个文章": "迭代地歷遍整個文章", "存在一行极长的文本！": "存在一行極長的文字！", "private_upload里面的文件名在解压zip后容易出现乱码": "private_upload裡面的檔案名在解壓縮zip後容易出現亂碼", "清除当前溢出的输入": "清除當前溢出的輸入", "只输出转化后的英文代码": "只輸出轉換後的英文程式碼", "打开插件列表": "打開外掛程式列表", "查询版本和用户意见": "查詢版本和使用者意見", "需要用此选项防止高频地请求openai导致错误": "需要用此選項防止高頻地請求openai導致錯誤", "有肉眼不可见的小变化": "有肉眼不可見的小變化", "返回一个新的字符串": "返回一個新的字串", "如果是.doc文件": "如果是.doc文件", "英语学术润色": "英語學術潤色", "已经全部完成": "已經全部完成", "该文件中主要包含2个函数": "該文件中主要包含2個函數", "捕捉函数f中的异常并封装到一个生成器中返回": "捕捉函數f中的異常並封裝到一個生成器中返回", "兼容旧版的配置": "兼容舊版的配置", "LLM的内部调优参数": "LLM的內部調優參數", "请查收": "請查收", "输出了前面的": "輸出了前面的", "用多种方式组合": "用多種方式組合", "等待中": "等待中", "从最长的条目开始裁剪": "從最長的條目開始裁剪", "就是临时文件夹的路径": "就是臨時文件夾的路徑", "体验gpt-4可以试试api2d": "體驗gpt-4可以試試api2d", "提交任务": "提交任務", "已配置": "已配置", "第三方库": "第三方庫", "将y中最后一项的输入部分段落化": "將y中最後一項的輸入部分段落化", "高级函数插件": "Advanced Function Plugin", "等待jittorllms响应中": "Waiting for jitto<PERSON><PERSON>s response", "解析整个C++项目": "Parsing the entire C++ project", "你是一名专业的学术教授": "You are a professional academic professor", "截断重试": "Truncated retry", "即在代码结构不变得情况下取代其他的上下文管理器": "That is, replace other context managers without changing the code structure", "表示函数是否成功执行": "Indicates whether the function was executed successfully", "处理多模型并行等细节": "Handling details such as parallelism of multiple models", "不显示中间过程": "Do not display intermediate process", "chatGPT的内部调优参数": "Internal tuning parameters of chatGPT", "你必须使用Markdown表格": "You must use Markdown tables", "第 5 步": "Step 5", "jittorllms响应异常": "jittorllms response exception", "在项目根目录运行这两个指令": "Run these two commands in the project root directory", "获取tokenizer": "Get tokenizer", "chatbot 为WebUI中显示的对话列表": "chatbot is the list of conversations displayed in WebUI", "test_解析一个Cpp项目": "test_parse a Cpp project", "将对话记录history以Markdown格式写入文件中": "Write the conversations record history to a file in Markdown format", "装饰器函数": "Decorator function", "玫瑰色": "Rose color", "将单空行": "刪除單行空白", "祖母绿": "綠松石色", "整合所有信息": "整合所有資訊", "如温度和top_p等": "例如溫度和top_p等", "重试中": "重試中", "月": "月份", "localhost意思是代理软件安装在本机上": "localhost意思是代理軟體安裝在本機上", "的长度必须小于 2500 个 Token": "長度必須小於 2500 個 Token", "抽取可用的api-key": "提取可用的api-key", "增强报告的可读性": "增強報告的可讀性", "对话历史": "對話歷史", "-1代表随机端口": "-1代表隨機端口", "在函数插件中被调用": "在函數插件中被調用", "向chatbot中添加错误信息": "向chatbot中添加錯誤訊息", "代理可能无效": "代理可能無效", "比如introduction": "例如introduction", "接下来请你逐文件分析下面的工程": "接下來請你逐文件分析下面的工程", "任务函数": "任務函數", "删除所有历史对话文件": "刪除所有歷史對話檔案", "找不到任何.md文件": "找不到任何.md文件", "给出输出文件清单": "給出輸出文件清單", "不能正常加载ChatGLM的参数！": "無法正常加載ChatGLM的參數！", "不详": "不詳", "提取出以下内容": "提取出以下內容", "请注意": "請注意", "不能加载Newbing组件": "無法加載Newbing組件", "您既可以在config.py中修改api-key": "您可以在config.py中修改api-key", "但推荐上传压缩文件": "但建議上傳壓縮文件", "支持任意数量的llm接口": "支持任意數量的llm接口", "材料如下": "材料如下", "停止": "停止", "gradio的inbrowser触发不太稳定": "gradio的inbrowser觸發不太穩定", "带token约简功能": "帶token約簡功能", "解析项目": "解析項目", "尝试识别段落": "嘗試識別段落", "输入栏用户输入的文本": "輸入欄用戶輸入的文本", "清理规则包括": "清理規則包括", "新版配置": "新版配置", "如果有": "如果有", "Call MOSS fail 不能正常加載MOSS的參數": "Call MOSS fail 不能正常加載MOSS的參數", "根據以上分析": "根據以上分析", "一些普通功能模塊": "一些普通功能模塊", "汇总报告如何远程获取": "如何遠程獲取匯總報告", "热更新prompt": "熱更新提示", "插件调度异常": "插件調度異常", "英文Latex项目全文润色": "英文Latex項目全文潤色", "此外我们也提供可同步处理大量文件的多线程Demo供您参考": "此外我們也提供可同步處理大量文件的多線程Demo供您參考", "则不解析notebook中的Markdown块": "則不解析notebook中的Markdown塊", "备选输入区": "備選輸入區", "个片段": "個片段", "总结输出": "總結輸出", "2. 把输出用的余量留出来": "2. 把輸出用的餘量留出來", "请对下面的文章片段做一个概述": "請對下面的文章片段做一個概述", "多线程方法": "多線程方法", "下面是对每个参数和返回值的说明": "下面是對每個參數和返回值的說明", "由于请求gpt需要一段时间": "由於請求gpt需要一段時間", "历史": "歷史", "用空格或段落分隔符替换原换行符": "用空格或段落分隔符替換原換行符", "查找语法错误": "查找語法錯誤", "输出 Returns": "輸出 Returns", "在config.py中配置": "在config.py中配置", "找不到任何.tex文件": "找不到任何.tex文件", "一键更新协议": "一鍵更新協議", "gradio版本较旧": "gradio版本較舊", "灵活而简洁": "靈活而簡潔", "等待NewBing响应中": "等待NewBing響應中", "更多函数插件": "更多函數插件", "作为一个标识而存在": "作為一個標識而存在", "GPT模型返回的回复字符串": "GPT模型返回的回復字串", "请从给定的若干条搜索结果中抽取信息": "請從給定的若干條搜索結果中抽取信息", "请对下面的文章片段做概述": "請對下面的文章片段做概述", "历史对话输入": "歷史對話輸入", "请稍等": "請稍等", "整理报告的格式": "整理報告的格式", "保存当前的对话": "保存當前的對話", "代理所在地查询超时": "代理所在地查詢超時", "inputs 是本次问询的输入": "inputs是本次問詢的輸入", "网页的端口": "網頁的端口", "仅仅服务于视觉效果": "僅僅服務於視覺效果", "把结果写入文件": "把結果寫入文件", "留空即可": "留空即可", "按钮颜色": "按鈕顏色", "借鉴了 https": "借鉴了 https", "Token溢出数": "Token溢出數", "找不到任何java文件": "找不到任何java文件", "批量总结Word文档": "批量總結Word文檔", "一言以蔽之": "一言以蔽之", "提取字体大小是否近似相等": "提取字體大小是否近似相等", "直接给定文件": "直接給定文件", "使用该模块需要额外依赖": "使用該模塊需要額外依賴", "的配置": "的配置", "pip install python-docx 用于docx格式": "pip install python-docx 用於docx格式", "正在查找对话历史文件": "正在查找對話歷史文件", "输入已识别为openai的api_key": "輸入已識別為openai的api_key", "对整个Latex项目进行翻译": "對整個Latex項目進行翻譯", "Y+回车=确认": "Y+回車=確認", "正在同时咨询ChatGPT和ChatGLM……": "正在同時諮詢ChatGPT和ChatGLM……", "根据 heuristic 规则": "根據heuristic規則", "如1024x1024": "如1024x1024", "函数插件区": "函數插件區", "*** API_KEY 导入成功": "*** API_KEY 導入成功", "请对下面的程序文件做一个概述文件名是": "請對下面的程序文件做一個概述文件名是", "內容太長了都會觸發token數量溢出的錯誤": "內容太長了都會觸發token數量溢出的錯誤", "沒有提供高級參數功能說明": "未提供高級參數功能說明", "和openai的連接容易斷掉": "和openai的連接容易斷掉", "分组+迭代处理": "分組+迭代處理", "安装Newbing的依赖": "安裝Newbing的依賴", "批": "批", "代理与自动更新": "代理與自動更新", "读取pdf文件并清理其中的文本内容": "讀取pdf文件並清理其中的文本內容", "多线程Demo": "多線程Demo", "\\cite和方程式": "\\cite和方程式", "可能会导致严重卡顿": "可能會導致嚴重卡頓", "将Markdown格式的文本转换为HTML格式": "將Markdown格式的文本轉換為HTML格式", "建议您复制一个config_private.py放自己的秘密": "建議您複製一個config_private.py放自己的秘密", "质能方程可以写成$$E=mc^2$$": "質能方程可以寫成$$E=mc^2$$", "的文件": "的文件", "是本次问询的输入": "是本次問詢的輸入", "第三种情况": "第三種情況", "如果同时InquireMultipleLargeLanguageModels": "如果同時InquireMultipleLargeLanguageModels", "小于正文的": "小於正文的", "将输入和输出解析为HTML格式": "將輸入和輸出解析為HTML格式", "您正在调用一个": "您正在調用一個", "缺少jittorllms的依赖": "缺少jittorllms的依賴", "是否重置": "是否重置", "解析整个前端项目": "解析整個前端專案", "是否唤起高级插件参数区": "是否喚起高級插件參數區", "pip包依赖安装出现问题": "pip包依賴安裝出現問題", "请先转化为.docx格式": "請先轉換為.docx格式", "整理history": "整理歷史記錄", "缺少api_key": "缺少api_key", "拆分过长的latex文件": "拆分過長的latex文件", "使用markdown表格输出结果": "使用markdown表格輸出結果", "搜集初始信息": "搜集初始信息", "但还没输出完后面的": "但還沒輸出完後面的", "在上下文执行开始的情况下": "在上下文執行開始的情況下", "不要用代码块": "不要用代碼塊", "比如你是翻译官怎样怎样": "例如你是翻譯官怎樣怎樣", "装饰器函数返回内部函数": "裝飾器函數返回內部函數", "请你作为一个学术翻译": "請你作為一個學術翻譯", "清除重复的换行": "清除重複的換行", "换行 -": "換行 -", "你好": "你好", "触发重置": "觸發重置", "安装MOSS的依赖": "安裝MOSS的依賴", "首先你在英文語境下通讀整篇論文": "首先你在英文語境下通讀整篇論文", "需要清除首尾空格": "需要清除首尾空格", "多線程函數插件中": "多線程函數插件中", "分析用戶提供的谷歌學術": "分析用戶提供的谷歌學術", "基本信息": "基本信息", "python 版本建議3.9+": "python 版本建議3.9+", "開始請求": "開始請求", "不會實時顯示在界面上": "不會實時顯示在界面上", "接下來兩句話只顯示在界面上": "接下來兩句話只顯示在界面上", "根據當前的模型類別": "根據當前的模型類別", "10個文件為一組": "10個文件為一組", "第三組插件": "第三組插件", "此函數逐漸地搜索最長的條目進行剪輯": "此函數逐漸地搜索最長的條目進行剪輯", "拆分過長的Markdown文件": "拆分過長的Markdown文件", "最多同時執行5個": "最多同時執行5個", "裁剪input": "裁剪input", "現在您點擊任意“紅顏色”標識的函數插件時": "現在您點擊任意“紅顏色”標識的函數插件時", "且沒有代碼段": "且沒有代碼段", "建議低於1": "建議低於1", "並且對於網絡上的文件": "並且對於網絡上的文件", "文件代码是": "檔案代碼是", "我上传了文件": "我上傳了檔案", "年份获取失败": "年份獲取失敗", "解析网页内容": "解析網頁內容", "但内部用stream的方法避免中途网线被掐": "但內部使用stream的方法避免中途網路斷線", "这个函数用于分割pdf": "這個函數用於分割PDF", "概括其内容": "概括其內容", "请谨慎操作": "請謹慎操作", "更新UI": "更新使用者介面", "输出": "輸出", "请先从插件列表中选择": "請先從插件列表中選擇", "函数插件": "函數插件", "的方式启动": "的方式啟動", "否则在回复时会因余量太少出问题": "否則在回覆時會因餘量太少出問題", "并替换为回车符": "並替換為換行符號", "Newbing失败": "Newbing失敗", "找不到任何.h头文件": "找不到任何.h頭檔案", "执行时": "執行時", "不支持通过环境变量设置!": "不支持透過環境變數設置!", "获取完整的从Openai返回的报错": "獲取完整的從Openai返回的錯誤", "放弃": "放棄", "系统静默prompt": "系統靜默提示", "如果子任务非常多": "如果子任務非常多", "打印traceback": "列印追蹤信息", "前情提要": "前情提要", "请在config文件中修改API密钥之后再运行": "請在config文件中修改API密鑰之後再運行", "使用正则表达式查找注释": "使用正則表達式查找註釋", "这段代码定义了一个名为DummyWith的空上下文管理器": "這段代碼定義了一個名為DummyWith的空上下文管理器", "用学术性语言写一段中文摘要": "用學術性語言寫一段中文摘要", "优先级3. 获取config中的配置": "優先級3. 獲取config中的配置", "此key无效": "此key無效", "对话历史列表": "對話歷史列表", "循环轮询各个线程是否执行完毕": "循環輪詢各個線程是否執行完畢", "处理数据流的主体": "處理數據流的主體", "综合": "綜合", "感叹号": "感嘆號", "浮点数": "浮點數", "必要时再进行切割": "必要時再進行切割", "请注意proxies选项的格式": "請注意proxies選項的格式", "我需要你找一张网络图片": "我需要你找一張網絡圖片", "裁剪输入": "裁剪輸入", "这里其实不需要join了": "這裡其實不需要join了", "例如 v2**y 和 ss* 的默认本地协议是socks5h": "例如 v2**y 和 ss* 的默認本地協議是socks5h", "粉红色": "粉紅色", "llm_kwargs参数": "llm_kwargs參數", "设置gradio的并行线程数": "設置gradio的並行線程數", "端口": "端口", "将每个换行符替换为两个换行符": "將每個換行符替換為兩個換行符", "防止回答时Token溢出": "防止回答時Token溢出", "单线": "單線", "成功读取环境变量": "成功讀取環境變量", "GPT返回的结果": "GPT返回的結果", "函数插件功能": "函數插件功能", "根据前后相邻字符的特点": "根據前後相鄰字符的特點", "发送到chatgpt进行分析": "發送到chatgpt進行分析", "例如": "例如", "翻译": "翻譯", "选择放弃": "選擇放棄", "将输出代码片段的“后面的": "將輸出代碼片段的“後面的", "两个指令来安装jittorllms的依赖": "兩個指令來安裝jittorllms的依賴", "不在arxiv中无法获取完整摘要": "無法在arxiv中取得完整摘要", "读取默认值作为数据类型转换的参考": "讀取預設值作為資料型態轉換的參考", "最后": "最後", "用于负责跨越线程传递已经输出的部分": "用於負責跨越線程傳遞已經輸出的部分", "请避免混用多种jittor模型": "請避免混用多種jittor模型", "等待输入": "等待輸入", "默认": "預設", "读取PDF文件": "讀取PDF文件", "作为一名中文学术论文写作改进助理": "作為一名中文學術論文寫作改進助理", "如果WEB_PORT是-1": "如果WEB_PORT是-1", "虽然不同的代理软件界面不一样": "雖然不同的代理軟體介面不一樣", "选择LLM模型": "選擇LLM模型", "回车退出": "按Enter退出", "第3步": "第3步", "找到原文本中的换行符": "找到原文本中的換行符號", "表示文件所在的文件夹路径": "表示文件所在的資料夾路徑", "您可以请再次尝试.": "您可以請再次嘗試。", "其他小工具": "其他小工具", "开始问问题": "開始問問題", "默认值": "預設值", "正在获取文献名！": "正在獲取文獻名稱！", "也可以在问题输入区输入临时的api-key": "也可以在問題輸入區輸入臨時的api-key", "单$包裹begin命令时多余": "單$包裹begin命令時多餘", "从而达到实时更新功能": "從而達到實時更新功能", "开始接收jittorllms的回复": "開始接收jittorllms的回覆", "防止爆token": "防止爆token", "等待重试": "等待重試", "解析整个Go项目": "解析整個Go項目", "解析整个Rust项目": "解析整個Rust項目", "则随机选取WEB端口": "則隨機選取WEB端口", "不输入代表全部匹配": "不輸入代表全部匹配", "在前端打印些好玩的东西": "在前端打印些好玩的東西", "而在上下文执行结束时": "而在上下文執行結束時", "会自动使用已配置的代理": "會自動使用已配置的代理", "第 3 步": "第 3 步", "稍微留一点余地": "稍微留一點余地", "靛蓝色": "靛藍色", "改变输入参数的顺序与结构": "改變輸入參數的順序與結構", "中提取出“标题”、“收录会议或期刊”等基本信息": "中提取出“標題”、“收錄會議或期刊”等基本信息", "刷新界面用 yield from update_ui": "刷新界面用 yield from update_ui", "下载编号": "下載編號", "来自EdgeGPT.py": "來自EdgeGPT.py", "每个子任务的输出汇总": "每個子任務的輸出匯總", "你是一位专业的中文学术论文作家": "你是一位專業的中文學術論文作家", "加了^代表不匹配": "加了^代表不匹配", "则覆盖原config文件": "則覆蓋原config文件", "提交按钮、重置按钮": "提交按鈕、重置按鈕", "对程序的整体功能和构架重新做出概括": "對程式的整體功能和架構重新做出概述", "未配置": "未配置", "文本过长将进行截断": "文本過長將進行截斷", "将英文句号": "將英文句號", "则使用当前时间生成文件名": "則使用當前時間生成檔名", "或显存": "或顯存", "请只提供文本的更正版本": "請只提供文本的更正版本", "大部分时候仅仅为了fancy的视觉效果": "大部分時候僅僅為了fancy的視覺效果", "不能达到预期效果": "不能達到預期效果", "css等": "css等", "该函数只有20多行代码": "該函數只有20多行程式碼", "以下是一篇学术论文中的一段内容": "以下是一篇學術論文中的一段內容", "Markdown/Readme英译中": "Markdown/Readme英譯中", "递归搜索": "遞歸搜尋", "检查一下是不是忘了改config": "檢查一下是不是忘了改config", "不需要修改": "不需要修改", "请求GPT模型同时维持用户界面活跃": "請求GPT模型同時維持用戶界面活躍", "是本次输入": "是本次輸入", "随便切一下敷衍吧": "隨便切一下敷衍吧", "紫罗兰色": "紫羅蘭色", "显示/隐藏功能区": "顯示/隱藏功能區", "加入下拉菜单中": "加入下拉菜單中", "等待ChatGLM响应中": "等待ChatGLM響應中", "代码已经更新": "代碼已經更新", "总结文章": "總結文章", "正常": "正常", "降低请求频率中": "降低請求頻率中", "3. 根据 heuristic 规则判断换行符是否是段落分隔": "3. 根據heuristic規則判斷換行符是否是段落分隔", "整理反复出现的控件句柄组合": "整理反復出現的控件句柄組合", "则给出安装建议": "則給出安裝建議", "我们先及时地做一次界面更新": "我們先及時地做一次界面更新", "数据流的显示最后收到的多少个字符": "數據流的顯示最後收到的多少個字符", "并将输出部分的Markdown和数学公式转换为HTML格式": "並將輸出部分的Markdown和數學公式轉換為HTML格式", "rar和7z格式正常": "rar和7z格式正常", "代码高亮": "程式碼高亮", "和 __exit__": "和 __exit__", "黄色": "黃色", "使用线程池": "使用線程池", "的主要内容": "的主要內容", "定义注释的正则表达式": "定義註釋的正則表達式", "Reduce the length. 本次输入过长": "減少長度。本次輸入過長", "具备多线程调用能力的函数": "具備多線程調用能力的函數", "你是一个程序架构分析师": "你是一個程式架構分析師", "MOSS尚未加载": "MOSS尚未載入", "环境变量": "環境變數", "请分析此页面中出现的所有文章": "請分析此頁面中出現的所有文章", "只裁剪历史": "只裁剪歷史", "在结束时": "在結束時", "缺一不可": "缺一不可", "第10步": "第10步", "安全第一条": "安全第一條", "解释代码": "解釋程式碼", "地址": "地址", "全部文件解析完成": "全部檔案解析完成", "乱七八糟的后处理": "亂七八糟的後處理", "输入时用逗号隔开": "輸入時用逗號隔開", "对最相关的两个搜索结果进行总结": "對最相關的兩個搜索結果進行總結", "第": "第", "清空历史": "清空歷史", "引用次数是链接中的文本": "引用次數是鏈接中的文本", "时": "時", "如没有给定输入参数": "如沒有給定輸入參數", "与gradio版本和网络都相关": "與gradio版本和網絡都相關", "润色": "潤色", "青蓝色": "青藍色", "如果浏览器没有自动打开": "如果瀏覽器沒有自動打開", "新功能": "新功能", "会把traceback和已经接收的数据转入输出": "會把traceback和已經接收的數據轉入輸出", "在这里输入分辨率": "在這裡輸入分辨率", "至少一个线程任务意外失败": "至少一個線程任務意外失敗", "子进程Worker": "子進程Worker", "使用yield from语句返回重新加载过的函数": "使用yield from語句返回重新加載過的函數", "网络等出问题时": "網絡等出問題時", "does not exist. 模型不存在": "不存在該模型", "本地LLM模型如ChatGLM的执行方式 CPU/GPU": "本地LLM模型如ChatGLM的執行方式 CPU/GPU", "如果选择自动处理": "如果選擇自動處理", "找不到本地项目或无权访问": "找不到本地專案或無權訪問", "是否在arxiv中": "是否在arxiv中", "版": "版", "数据流的第一帧不携带content": "數據流的第一幀不攜帶content", "OpenAI和API2D不会走这里": "OpenAI和API2D不會走這裡", "请编辑以下文本": "請編輯以下文本", "尽可能多地保留文本": "盡可能多地保留文本", "将文本按照段落分隔符分割开": "將文本按照段落分隔符分割開", "获取成功": "獲取成功", "然后回答问题": "然後回答問題", "同时分解长句": "同時分解長句", "刷新时间间隔频率": "刷新時間間隔頻率", "您可以将任意一个文件路径粘贴到输入区": "您可以將任意一個文件路徑粘貼到輸入區", "需要手动安装新增的依赖库": "需要手動安裝新增的依賴庫", "的模板": "的模板", "重命名文件": "重命名文件", "第1步": "第1步", "只输出代码": "只輸出代碼", "准备对工程源代码进行汇总分析": "準備對工程源代碼進行匯總分析", "是所有LLM的通用接口": "是所有LLM的通用接口", "等待回复": "等待回覆", "此线程失败前收到的回答": "此線程失敗前收到的回答", "Call ChatGLM fail 不能正常加载ChatGLM的参数": "呼叫ChatGLM失敗，無法正常加載ChatGLM的參數", "输入参数 Args": "輸入參數Args", "也可以获取它": "也可以獲取它", "请求GPT模型的": "請求GPT模型的", "您将把您的API-KEY和对话隐私完全暴露给您设定的中间人！": "您將把您的API-KEY和對話隱私完全暴露給您設定的中間人！", "等待MOSS响应中": "等待MOSS響應中", "文件保存到本地": "文件保存到本地", "例如需要翻译的一段话": "例如需要翻譯的一段話", "避免解析压缩文件": "避免解析壓縮文件", "另外您可以随时在history子文件夹下找回旧版的程序": "另外您可以隨時在history子文件夾下找回舊版的程式", "由于您没有设置config_private.py私密配置": "由於您沒有設置config_private.py私密配置", "缺少ChatGLM的依赖": "缺少ChatGLM的依賴", "试着补上后个": "試著補上後個", "如果是网络上的文件": "如果是網路上的檔案", "找不到任何.tex或pdf文件": "找不到任何.tex或pdf檔案", "直到历史记录的标记数量降低到阈值以下": "直到歷史記錄的標記數量降低到閾值以下", "当代码输出半截的时候": "當程式碼輸出一半時", "输入区2": "輸入區2", "则删除报错信息": "則刪除錯誤訊息", "如果需要使用newbing": "如果需要使用newbing", "迭代之前的分析": "迭代之前的分析", "单线程方法": "單線程方法", "装载请求内容": "載入請求內容", "翻译为中文": "翻譯為中文", "以及代理设置的格式是否正确": "以及代理設置的格式是否正確", "石头色": "石頭色", "输入谷歌学术搜索页url": "輸入谷歌學術搜索頁URL", "可选 ↓↓↓": "可選 ↓↓↓", "再点击按钮": "再點擊按鈕", "开发者们❤️": "開發者們❤️", "若再次失败则更可能是因为输入过长.": "若再次失敗則更可能是因為輸入過長。", "载入对话": "載入對話", "包括": "包括", "或者": "或者", "并执行函数的新版本": "並執行函數的新版本", "论文": "論文", "解析一个Golang项目": "ParseAGolangProject", "Latex英文纠错": "LatexEnglishCorrection", "连接bing搜索回答问题": "ConnectToBingSearchForAnswer", "联网的ChatGPT_bing版": "ChatGPT_BingVersionOnline", "总结音视频": "SummarizeAudioAndVideo", "动画生成": "GenerateAnimations", "数学动画生成manim": "GenerateMathematicalAnimationsWithManim", "Markdown翻译指定语言": "TranslateMarkdownToSpecifiedLanguage", "知识库问答": "KnowledgeBaseQA", "Langchain知识库": "LangchainKnowledgeBase", "读取知识库作答": "ReadKnowledgeBaseAndAnswerQuestions", "交互功能模板函数": "InteractiveFunctionTemplateFunctions", "交互功能函数模板": "InteractiveFunctionFunctionTemplates", "Latex英文纠错加PDF对比": "LatexEnglishCorrectionWithPDFComparison", "Latex_Function": "OutputPDFFromLatex", "Latex翻译中文并重新编译PDF": "TranslateLatexToChineseAndRecompilePDF", "语音助手": "VoiceAssistant", "微调数据集生成": "FineTuneDatasetGeneration", "chatglm微调工具": "ChatGLM_FineTuningTool", "启动微调": "StartFineTuning", "sprint亮靛": "SprintLiangDian", "寻找Latex主文件": "FindLatexMainFile", "专业词汇声明": "ProfessionalTerminologyDeclaration", "Latex精细分解与转化": "LatexFineDecompositionAndConversion", "编译Latex": "CompileLatex", "正在等您说完问题": "正在等您說完問題", "最多同时执行5个": "最多同時執行5個", "将文件复制一份到下载区": "將檔案複製一份到下載區", "您接下来不能再使用其他插件了": "您接下來不能再使用其他插件了", "如 绿帽子*深蓝色衬衫*黑色运动裤": "如 綠帽子*深藍色襯衫*黑色運動褲", "首先你在中文语境下通读整篇论文": "首先您在中文語境下通讀整篇論文", "根据给定的切割时长将音频文件切割成多个片段": "根據給定的切割時長將音訊檔切割成多個片段", "接下来两句话只显示在界面上": "接下來兩句話只顯示在介面上", "清空label": "清空標籤", "正在尝试自动安装": "正在嘗試自動安裝", "MOSS消耗大量的内存": "MOSS消耗大量的記憶體", "如果这里报错": "如果這裡報錯", "其他类型文献转化效果未知": "其他類型文獻轉換效果未知", "ChatGPT综合": "ChatGPT綜合", "音频文件的路径": "音訊檔案的路徑", "执行错误": "執行錯誤", "因此选择GenerateImage函数": "因此選擇GenerateImage函數", "从摘要中提取高价值信息": "從摘要中提取高價值資訊", "使用英文": "使用英文", "是否在提交时自动清空输入框": "是否在提交時自動清空輸入框", "生成数学动画": "生成數學動畫", "正在加载Claude组件": "正在載入Claude元件", "参数说明": "參數說明", "建议排查": "建議排查", "将消耗较长时间下载中文向量化模型": "將消耗較長時間下載中文向量化模型", "test_LangchainKnowledgeBase读取": "test_LangchainKnowledgeBase讀取", "安装Claude的依赖": "安裝Claude的相依性", "以下所有配置也都支持利用环境变量覆写": "以下所有配置也都支持利用環境變數覆寫", "需要被切割的音频文件名": "需要被切割的音頻文件名", "保存当前对话": "保存當前對話", "功能、贡献者": "功能、貢獻者", "Chuanhu-Small-and-Beautiful主题": "Chuanhu-小而美主題", "等待Claude响应": "等待Claude響應", "其他模型转化效果未知": "其他模型轉換效果未知", "版权归原文作者所有": "版權歸原文作者所有", "回答完问题后": "回答完問題後", "请先上传文件素材": "請先上傳文件素材", "上传本地文件/压缩包供函数插件调用": "上傳本地文件/壓縮包供函數插件調用", "P.S. 顺便把Latex的注释去除": "P.S. 順便把Latex的註釋去除", "您提供的api-key不满足要求": "您提供的api-key不滿足要求", "切割音频文件": "切割音頻文件", "对不同latex源文件扣分": "對不同latex源文件扣分", "以下是一篇学术论文的基础信息": "以下是一篇學術論文的基礎信息", "问题": "問題", "待注入的知识库名称id": "待注入的知識庫名稱id", "”的主要内容": "”的主要內容", "获取设置": "獲取設置", "str类型": "str類型", "多线程": "多線程", "尝试执行Latex指令失败": "嘗試執行Latex指令失敗", "然后再写一段英文摘要": "然後再寫一段英文摘要", "段音频的主要内容": "段音頻的主要內容", "临时地激活代理网络": "臨時地激活代理網絡", "网络的远程文件": "網絡的遠程文件", "不能正常加载ChatGLMFT的参数！": "無法正常載入ChatGLMFT的參數！", "正在编译PDF文档": "正在編譯PDF文件", "等待ChatGLMFT响应中": "等待ChatGLMFT回應中", "将": "將", "片段": "片段", "修复括号": "修復括號", "条": "條", "建议直接在API_KEY处填写": "建議直接在API_KEY處填寫", "根据需要切换prompt": "根據需要切換prompt", "使用": "使用", "请输入要翻译成哪种语言": "請輸入要翻譯成哪種語言", "实际得到格式": "實際得到格式", "例如 f37f30e0f9934c34a992f6f64f7eba4f": "例如 f37f30e0f9934c34a992f6f64f7eba4f", "请切换至“KnowledgeBaseQA”插件进行知识库访问": "請切換至“KnowledgeBaseQA”插件進行知識庫訪問", "用户填3": "用戶填3", "远程云服务器部署": "遠程雲服務器部署", "未知指令": "未知指令", "每个线程都要“喂狗”": "每個線程都要“喂狗”", "该项目的Latex主文件是": "該項目的Latex主文件是", "设置OpenAI密钥和模型": "設置OpenAI密鑰和模型", "填入你亲手写的部署名": "填入你親手寫的部署名", "仅调试": "僅調試", "依赖不足": "依賴不足", "右下角更换模型菜单中可切换openai": "右下角更換模型菜單中可切換openai", "解析整个CSharp项目": "解析整個CSharp項目", "唤起高级参数输入区": "喚起高級參數輸入區", "这个bug没找到触发条件": "這個bug沒找到觸發條件", "========================================= 插件主程序2 =====================================================": "========================================= 插件主程序2 =====================================================", "经过充分测试": "經過充分測試", "该文件中主要包含三个函数": "該文件中主要包含三個函數", "您可以到Github Issue区": "您可以到Github Issue區", "避免线程阻塞": "避免線程阻塞", "吸收iffalse注释": "吸收iffalse註釋", "from crazy_functions.虚空终端 import 终端": "from crazy_functions.虛空終端 import 終端", "异步方法": "異步方法", "块元提取": "塊元提取", "Your account is not active. OpenAI以账户失效为由": "您的帳戶未啟用。OpenAI以帳戶失效為由", "还原部分原文": "還原部分原文", "如果要使用Claude": "如果要使用Claude", "把文件复制过去": "把文件複製過去", "解压失败! 需要安装pip install rarfile来解压rar文件": "解壓失敗！需要安裝pip install rarfile來解壓rar文件", "正在锁定插件": "正在鎖定插件", "输入 clear 以清空对话历史": "輸入 clear 以清空對話歷史", "P.S. 但愿没人把latex模板放在里面传进来": "P.S. 但願沒人把latex模板放在裡面傳進來", "实时音频采集": "實時音頻採集", "开始最终总结": "開始最終總結", "拒绝服务": "拒絕服務", "配置教程&视频教程": "配置教程&視頻教程", "所有音频都总结完成了吗": "所有音頻都總結完成了嗎", "返回": "返回", "避免不小心传github被别人看到": "避免不小心傳github被別人看到", "否则将导致每个人的Claude问询历史互相渗透": "否則將導致每個人的Claude問詢歷史互相滲透", "提问吧! 但注意": "提問吧！但注意", "待处理的word文档路径": "待處理的word文檔路徑", "欢迎加README中的QQ联系开发者": "歡迎加README中的QQ聯繫開發者", "建议暂时不要使用": "建議暫時不要使用", "Latex没有安装": "Latex沒有安裝", "在这里放一些网上搜集的demo": "在這裡放一些網上搜集的demo", "实现消息发送、接收等功能": "實現消息發送、接收等功能", "用于与with语句一起使用": "用於與with語句一起使用", "解压失败! 需要安装pip install py7zr来解压7z文件": "解壓失敗! 需要安裝pip install py7zr來解壓7z文件", "借助此参数": "借助此參數", "判定为数据流的结束": "判定為數據流的結束", "提取文件扩展名": "提取文件擴展名", "GPT结果已输出": "GPT結果已輸出", "读取文件": "讀取文件", "如果OpenAI不响应": "如果OpenAI不響應", "输入部分太自由": "輸入部分太自由", "用于给一小段代码上代理": "用於給一小段代碼上代理", "输入 stop 以终止对话": "輸入 stop 以終止對話", "这个paper有个input命令文件名大小写错误！": "這個paper有個input命令文件名大小寫錯誤！", "等待Claude回复的片段": "等待Claude回復的片段", "开始": "開始", "将根据报错信息修正tex源文件并重试": "將根據報錯信息修正tex源文件並重試", "建议更换代理协议": "建議更換代理協議", "递归地切割PDF文件": "遞歸地切割PDF文件", "读 docs\\use_azure.md": "讀 docs\\use_azure.md", "参数": "參數", "屏蔽空行和太短的句子": "屏蔽空行和太短的句子", "分析上述回答": "分析上述回答", "因为在同一个频道里存在多人使用时历史消息渗透问题": "因為在同一個頻道裡存在多人使用時歷史消息滲透問題", "使用latexdiff生成論文轉化前後對比": "使用latexdiff生成論文轉化前後對比", "檢查結果": "檢查結果", "請在此處追加更細緻的校錯指令": "請在此處追加更細緻的校錯指令", "報告如何遠程獲取": "報告如何遠程獲取", "發現已經存在翻譯好的PDF文檔": "發現已經存在翻譯好的PDF文檔", "插件鎖定中": "插件鎖定中", "正在精細切分latex文件": "正在精細切分latex文件", "數學GenerateAnimations": "數學GenerateAnimations", "上傳文件自動修正路徑": "上傳文件自動修正路徑", "請檢查ALIYUN_TOKEN和ALIYUN_APPKEY是否過期": "請檢查ALIYUN_TOKEN和ALIYUN_APPKEY是否過期", "上傳Latex項目": "上傳LaTeX項目", "Aliyun音頻服務異常": "Aliyun音頻服務異常", "為了防止大語言模型的意外謬誤產生擴散影響": "為了防止大語言模型的意外謬誤產生擴散影響", "調用Claude時": "調用Claude時", "解除插件鎖定": "解除插件鎖定", "暗色模式 / 亮色模式": "暗色模式 / 亮色模式", "只有第二步成功": "只有第二步成功", "分析结果": "分析結果", "用第二人称": "使用第二人稱", "详情见https": "詳情請見https", "记住当前的label": "記住當前的標籤", "当无法用标点、空行分割时": "當無法用標點符號、空行分割時", "如果分析错误": "如果分析錯誤", "如果有必要": "如果有必要", "不要修改!! 高危设置！通过修改此设置": "不要修改!! 高危設置！通過修改此設置", "ChatGLMFT消耗大量的内存": "ChatGLMFT消耗大量的內存", "摘要生成后的文档路径": "摘要生成後的文件路徑", "对全文进行概括": "對全文進行概述", "LLM_MODEL是默认选中的模型": "LLM_MODEL是默認選中的模型", "640个字节为一组": "640個字節為一組", "获取关键词": "獲取關鍵詞", "解析为简体中文": "解析為簡體中文", "将 \\include 命令转换为 \\input 命令": "將 \\include 命令轉換為 \\input 命令", "默认值为1000": "默認值為1000", "手动指定语言": "手動指定語言", "请登录OpenAI查看详情 https": "請登錄OpenAI查看詳情 https", "尝试第": "嘗試第", "每秒采样数量": "每秒採樣數量", "加载失败!": "加載失敗!", "方法": "方法", "对这个人外貌、身处的环境、内心世界、过去经历进行描写": "對這個人外貌、身處的環境、內心世界、過去經歷進行描寫", "请先将.doc文档转换为.docx文档": "請先將.doc文檔轉換為.docx文檔", "定位主Latex文件": "定位主Latex文件", "批量SummarizeAudioAndVideo": "批量摘要音视频", "终端": "終端", "即将退出": "即將退出", "找不到": "找不到", "正在听您讲话": "正在聆聽您講話", "请您不要删除或修改这行警告": "請勿刪除或修改此警告", "没有阿里云语音识别APPKEY和TOKEN": "沒有阿里雲語音識別APPKEY和TOKEN", "临时地启动代理网络": "臨時啟動代理網絡", "请尝试把以下指令复制到高级参数区": "請將以下指令複製到高級參數區", "中文Bing版": "中文Bing版", "计算文件总时长和切割点": "計算文件總時長和切割點", "寻找主文件": "尋找主文件", "jittorllms尚未加载": "jittorllms尚未加載", "使用正则表达式查找半行注释": "使用正則表達式查找半行註釋", "文档越长耗时越长": "文檔越長耗時越長", "生成中文PDF": "生成中文PDF", "写入文件": "寫入文件", "第三组插件": "第三組插件", "开始接收chatglmft的回复": "開始接收chatglmft的回覆", "由于提问含不合规内容被Azure过滤": "由於提問含不合規內容被Azure過濾", "安装方法https": "安裝方法https", "是否自动处理token溢出的情况": "是否自動處理token溢出的情況", "如果需要使用AZURE 详情请见额外文档 docs\\use_azure.md": "如果需要使用AZURE 詳情請見額外文檔 docs\\use_azure.md", "将要忽略匹配的文件后缀": "將要忽略匹配的文件後綴", "authors获取失败": "authors獲取失敗", "发送到openai音频解析终端": "發送到openai音頻解析終端", "请开始多线程操作": "請開始多線程操作", "对这个人外貌、身处的环境、内心世界、人设进行描写": "對這個人外貌、身處的環境、內心世界、人設進行描寫", "MOSS can understand and communicate fluently in the language chosen by the user such as English and 中文. MOSS can perform any language-based tasks.": "MOSS可以流利地理解和使用用戶選擇的語言，例如英語和中文。MOSS可以執行任何基於語言的任務。", "work_folder = Latex預處理": "設置工作目錄為Latex預處理", "然後轉移到指定的另一個路徑中": "然後轉移到指定的另一個路徑中", "使用Newbing": "使用Newbing", "詳情信息見requirements.txt": "詳細信息請參閱requirements.txt", "開始下載": "開始下載", "多線程翻譯開始": "多線程翻譯開始", "當前大語言模型": "當前大語言模型", "格式如org-123456789abcdefghijklmno的": "格式如org-123456789abcdefghijklmno的", "當下一次用戶提交時": "當下一次用戶提交時", "需要特殊依賴": "需要特殊依賴", "次編譯": "次編譯", "先上傳數據集": "先上傳數據集", "gpt寫的": "gpt寫的", "調用緩存": "調用緩存", "优先级1. 获取环境变量作为配置": "優先級1. 獲取環境變量作為配置", "检查config中的AVAIL_LLM_MODELS选项": "檢查config中的AVAIL_LLM_MODELS選項", "并且对于网络上的文件": "並且對於網絡上的文件", "根据文本使用GPT模型生成相应的图像": "根據文本使用GPT模型生成相應的圖像", "功能描述": "功能描述", "翻译结果": "翻譯結果", "需要预先pip install rarfile": "需要預先pip install rarfile", "等待响应": "等待響應", "我们剥离Introduction之后的部分": "我們剝離Introduction之後的部分", "函数插件-固定按钮区": "函數插件-固定按鈕區", "临时存储用于调试": "臨時存儲用於調試", "比正文字体小": "比正文字體小", "会直接转到该函数": "會直接轉到該函數", "请以以下方式load模型！！！": "請以以下方式load模型！！！", "请输入关键词": "請輸入關鍵詞", "返回找到的第一个": "返回找到的第一個", "高级参数输入区": "高級參數輸入區", "精细切分latex文件": "精細切分latex文件", "赋予插件锁定 锁定插件回调路径": "賦予插件鎖定 鎖定插件回調路徑", "尝试下载": "嘗試下載", "包含documentclass关键字": "包含documentclass關鍵字", "在一个异步线程中采集音频": "在一個異步線程中採集音頻", "先删除": "先刪除", "则跳过GPT请求环节": "則跳過GPT請求環節", "Not enough point. API2D账户点数不足": "Not enough point. API2D帳戶點數不足", "如果一句话小于7个字": "如果一句話小於7個字", "具备以下功能": "具備以下功能", "请查看终端的输出或耐心等待": "請查看終端的輸出或耐心等待", "对输入的word文档进行摘要生成": "對輸入的word文檔進行摘要生成", "只读": "只讀", "文本碎片重组为完整的tex文件": "文本碎片重組為完整的tex文件", "通过调用conversations_open方法打开一个频道": "通過調用conversations_open方法打開一個頻道", "对话历史文件损坏！": "對話歷史文件損壞！", "再失败就没办法了": "再失敗就沒辦法了", "原始PDF编译是否成功": "原始PDF編譯是否成功", "不能正常加载jittorllms的参数！": "不能正常加載jittorllms的參數！", "正在编译对比PDF": "正在編譯對比PDF", "找不到微调模型检查点": "找不到微調模型檢查點", "将生成的报告自动投射到文件上传区": "將生成的報告自動投射到文件上傳區", "请对这部分内容进行语法矫正": "請對這部分內容進行語法校正", "编译已经开始": "編譯已經開始", "需要读取和清理文本的pdf文件路径": "需要讀取和清理文本的pdf文件路徑", "读取文件内容到内存": "讀取文件內容到內存", "用&符号分隔": "用&符號分隔", "输入arxivID": "輸入arxivID", "找 API_ORG 设置项": "找API_ORG設置項", "分析用户提供的谷歌学术": "分析用戶提供的谷歌學術", "欢迎使用 MOSS 人工智能助手！输入内容即可进行对话": "歡迎使用 MOSS 人工智能助手！輸入內容即可進行對話", "段音频的第": "段音頻的第", "没有找到任何可读取文件": "沒有找到任何可讀取文件", "目前仅支持GPT3.5/GPT4": "目前僅支持GPT3.5/GPT4", "为每一位访问的用户赋予一个独一无二的uuid编码": "為每一位訪問的用戶賦予一個獨一無二的uuid編碼", "内含已经翻译的Tex文档": "內含已經翻譯的Tex文檔", "消耗时间的函数": "消耗時間的函數", "成功啦": "成功啦", "环境变量配置格式见docker-compose.yml": "環境變量配置格式見docker-compose.yml", "将每次对话记录写入Markdown格式的文件中": "將每次對話記錄寫入Markdown格式的文件中", "报告已经添加到右侧“文件上传区”": "報告已經添加到右側“文件上傳區”", "此处可以输入解析提示": "此處可以輸入解析提示", "缺少MOSS的依赖": "缺少MOSS的依賴", "仅在Windows系统进行了测试": "僅在Windows系統進行了測試", "然后重启程序": "然後重啟程序", "此处不修改": "此處不修改", "输出html调试文件": "輸出html調試文件", "6.25 加入判定latex模板的代码": "6.25 加入判定latex模板的代碼", "提取总结": "提取總結", "要求": "要求", "由于最为关键的转化PDF编译失败": "由於最為關鍵的轉化PDF編譯失敗", "除非您是论文的原作者": "除非您是論文的原作者", "输入问题后点击该插件": "輸入問題後點擊該插件", "该选项即将被弃用": "該選項即將被棄用", "再列出用户可能提出的三个问题": "再列出用戶可能提出的三個問題", "所有文件都总结完成了吗": "所有文件都總結完成了嗎", "请稍候": "請稍候", "向chatbot中添加简单的意外错误信息": "向chatbot中添加簡單的意外錯誤信息", "快捷的调试函数": "快捷的調試函數", "LatexEnglishCorrection+高亮修正位置": "Latex英文校正+高亮修正位置", "循环监听已打开频道的消息": "循環監聽已打開頻道的消息", "将指定目录下的PDF文件从英文翻译成中文": "將指定目錄下的PDF文件從英文翻譯成中文", "请对下面的音频片段做概述": "請對下面的音頻片段做概述", "openai的官方KEY需要伴隨组织编码": "openai的官方KEY需要伴隨組織編碼", "表示频道ID": "頻道ID", "当前支持的格式包括": "目前支援的格式包括", "只有GenerateImage和生成图像相关": "僅限GenerateImage和生成圖像相關", "删除中间文件夹": "刪除中間資料夾", "解除插件状态": "解除插件狀態", "正在预热文本向量化模组": "正在預熱文本向量化模組", "100字以内": "限制100字內", "如果缺少依赖": "如果缺少相依性", "寻找主tex文件": "尋找主要tex檔案", "gpt 多线程请求": "gpt 多線程請求", "已知某些代码的局部作用是": "已知某些程式碼的局部作用是", "--读取文件": "--讀取檔案", "前面是中文冒号": "前面是中文冒號", "*{\\scriptsize\\textbf{警告": "*{\\scriptsize\\textbf{警告", "OpenAI所允许的最大并行过载": "OpenAI所允許的最大並行過載", "请直接去该路径下取回翻译结果": "請直接前往該路徑取回翻譯結果", "以免输入溢出": "以免輸入溢出", "把某个路径下所有文件压缩": "壓縮某個路徑下的所有檔案", "问询记录": "詢問記錄", "Tex源文件缺失！": "Tex原始檔案遺失！", "当前参数": "目前參數", "处理markdown文本格式的转变": "處理markdown文本格式的轉換", "尝试加载": "嘗試載入", "请在此处给出自定义翻译命令": "請在此處提供自訂翻譯命令", "这需要一段时间计算": "這需要一段時間計算", "-构建知识库": "-建立知識庫", "还需要填写组织": "還需要填寫組織", "当前知识库内的有效文件": "當前知識庫內的有效文件", "第一次调用": "第一次調用", "从一批文件": "從一批文件", "json等": "json等", "翻译-": "翻譯-", "编译文献交叉引用": "編譯文獻交叉引用", "优先级2. 获取config_private中的配置": "優先級2. 獲取config_private中的配置", "可选": "可選", "我们": "我們", "编译结束": "編譯結束", "或代理节点": "或代理節點", "chatGPT 分析报告": "chatGPT 分析報告", "调用openai api 使用whisper-1模型": "調用openai api 使用whisper-1模型", "这段代码定义了一个名为TempProxy的空上下文管理器": "這段代碼定義了一個名為TempProxy的空上下文管理器", "生成的视频文件路径": "生成的視頻文件路徑", "请直接提交即可": "請直接提交即可", "=================================== 工具函数 ===============================================": "=================================== 工具函數 ===============================================", "报错信息如下. 如果是与网络相关的问题": "報錯信息如下. 如果是與網絡相關的問題", "python 版本建议3.9+": "python 版本建議3.9+", "多线程函数插件中": "多線程函數插件中", "对话助手函数插件": "對話助手函數插件", "或者重启之后再度尝试": "或者重啟之後再度嘗試", "拆分过长的latex片段": "拆分過長的latex片段", "调用whisper模型音频转文字": "調用whisper模型音頻轉文字", "失败啦": "失敗啦", "正在编译PDF": "正在編譯PDF", "请刷新界面重试": "請刷新界面重試", "模型参数": "模型參數", "写出文件": "寫出文件", "第二组插件": "第二組插件", "在多Tex文档中": "在多Tex文檔中", "有线程锁": "有線程鎖", "释放线程锁": "釋放線程鎖", "读取优先级": "讀取優先級", "Linux下必须使用Docker安装": "Linux下必須使用Docker安裝", "例如您可以将以下命令复制到下方": "例如您可以將以下命令複製到下方", "导入依赖失败": "導入依賴失敗", "给出一些判定模板文档的词作为扣分项": "給出一些判定模板文檔的詞作為扣分項", "等待Claude响应中": "等待Claude響應中", "Call ChatGLMFT fail 不能正常加载ChatGLMFT的参数": "Call ChatGLMFT fail 不能正常加載ChatGLMFT的參數", "但本地存储了以下历史文件": "但本地存儲了以下歷史文件", "如果存在调试缓存文件": "如果存在調試緩存文件", "如果这里抛出异常": "如果這裡拋出異常", "详见项目主README.md": "詳見項目主README.md", "作者": "作者", "现在您点击任意“红颜色”标识的函数插件时": "現在您點擊任意“紅顏色”標識的函數插件時", "上下文管理器必须实现两个方法": "上下文管理器必須實現兩個方法", "匹配^数字^": "匹配^數字^", "也是可读的": "也是可讀的", "将音频解析为简体中文": "將音頻解析為簡體中文", "依次访问网页": "依次訪問網頁", "P.S. 顺便把CTEX塞进去以支持中文": "P.S. 順便把CTEX塞進去以支持中文", "NewBing响应异常": "NewBing響應異常", "获取已打开频道的最新消息并返回消息列表": "獲取已打開頻道的最新消息並返回消息列表", "请使用Markdown": "請使用Markdown", "例如 RoPlZrM88DnAFkZK": "例如 RoPlZrM88DnAFkZK", "编译BibTex": "編譯BibTex", "Claude失败": "<PERSON>失敗", "请更换为API_URL_REDIRECT配置": "請更換為API_URL_REDIRECT配置", "P.S. 其他可用的模型还包括": "P.S. 其他可用的模型還包括", "色彩主体": "色彩主體", "后面是英文逗号": "後面是英文逗號", "下载pdf文件未成功": "下載pdf文件未成功", "删除整行的空注释": "刪除整行的空注釋", "吸收匿名公式": "吸收匿名公式", "从而更全面地理解项目的整体功能": "從而更全面地理解項目的整體功能", "不需要再次转化": "不需要再次轉化", "可以将自身的状态存储到cookie中": "可以將自身的狀態存儲到cookie中", "1、英文题目；2、中文题目翻译；3、作者；4、arxiv公开": "1、英文題目；2、中文題目翻譯；3、作者；4、arxiv公開", "GPT 学术优化": "GPT 學術優化", "解析整个Python项目": "解析整個Python項目", "吸收其他杂项": "吸收其他雜項", "-预热文本向量化模组": "-預熱文本向量化模組", "Claude组件初始化成功": "Claude組件初始化成功", "此处填API密钥": "此處填API密鑰", "请继续分析其他源代码": "請繼續分析其他源代碼", "质能方程式": "質能方程式", "功能尚不稳定": "功能尚不穩定", "使用教程详情见 request_llms/README.md": "使用教程詳情見 request_llms/README.md", "从以上搜索结果中抽取信息": "從以上搜索結果中抽取信息", "虽然PDF生成失败了": "雖然PDF生成失敗了", "找图片": "尋找圖片", "还原原文": "還原原文", "可调节线程池的大小避免openai的流量限制错误": "可調整線程池大小以避免openai流量限制錯誤", "正在提取摘要并下载PDF文档……": "正在提取摘要並下載PDF文件......", "缺少ChatGLMFT的依赖": "缺少ChatGLMFT的依賴", "不会实时显示在界面上": "不會即時顯示在界面上", "解决部分词汇翻译不准确的问题": "解決部分詞彙翻譯不準確的問題", "等待多线程操作": "等待多線程操作", "吸收title与作者以上的部分": "吸收標題與作者以上的部分", "如果需要使用Slack Claude": "如果需要使用Slack Claude", "一、论文概况": "一、論文概況", "默认为Chinese": "默認為中文", "图像生成所用到的提示文本": "圖像生成所用到的提示文本", "向已打开的频道发送一条文本消息": "向已打開的頻道發送一條文本消息", "如果某个子任务出错": "如果某個子任務出錯", "chatglmft 没有 sys_prompt 接口": "chatglmft沒有sys_prompt接口", "对比PDF编译是否成功": "對比PDF編譯是否成功", "免费": "免費", "请讲话": "請講話", "安装ChatGLM的依赖": "安裝ChatGLM的依賴", "对IPynb文件进行解析": "對IPynb文件進行解析", "文件路径列表": "文件路徑列表", "或者使用此插件继续上传更多文件": "或者使用此插件繼續上傳更多文件", "随机负载均衡": "隨機負載均衡", "！！！如果需要运行量化版本": "！！！如果需要運行量化版本", "注意目前不能多人同时调用Claude接口": "注意目前不能多人同時調用Claude接口", "文件读取完成": "文件讀取完成", "用于灵活调整复杂功能的各种参数": "用於靈活調整複雜功能的各種參數", "**函数功能**": "**函數功能**", "先切换模型到openai或api2d": "先切換模型到openai或api2d", "You are associated with a deactivated account. OpenAI以账户失效为由": "您的帳戶已停用。OpenAI以帳戶失效為由", "你的回答必须简单明了": "您的回答必須簡單明了", "是否丢弃掉 不是正文的内容": "是否丟棄掉 不是正文的內容", "但请查收结果": "但請查收結果", "Claude响应缓慢": "<PERSON>響應緩慢", "需Latex": "需Latex", "Claude回复的片段": "Claude回復的片段", "如果要使用ChatGLMFT": "如果要使用ChatGLMFT", "它*必须*被包含在AVAIL_LLM_MODELS列表中": "它*必須*被包含在AVAIL_LLM_MODELS列表中", "前面是中文逗号": "前面是中文逗號", "需要预先pip install py7zr": "需要預先pip install py7zr", "将前后断行符脱离": "將前後斷行符脫離", "防止丢失最后一条消息": "防止丟失最後一條消息", "初始化插件状态": "初始化插件狀態", "以秒为单位": "以秒為單位", "中文Latex项目全文润色": "中文Latex項目全文潤色", "对整个Latex项目进行纠错": "對整個Latex項目進行校對", "NEWBING_COOKIES未填写或有格式错误": "NEWBING_COOKIES未填寫或有格式錯誤", "函数插件作者": "函數插件作者", "结束": "結束", "追加历史": "追加歷史", "您需要首先调用构建知识库": "您需要首先調用構建知識庫", "如果程序停顿5分钟以上": "如果程序停頓5分鐘以上", "ChatGLMFT响应异常": "ChatGLMFT響應異常", "根据当前的模型类别": "根據當前的模型類別", "才能继续下面的步骤": "才能繼續下面的步驟", "并将返回的频道ID保存在属性CHANNEL_ID中": "並將返回的頻道ID保存在屬性CHANNEL_ID中", "请查收结果": "請查收結果", "解决插件锁定时的界面显示问题": "解決插件鎖定時的界面顯示問題", "待提取的知识库名称id": "待提取的知識庫名稱id", "Claude响应异常": "<PERSON>響應異常", "当前代理可用性": "當前代理可用性", "代理网络配置": "代理網絡配置", "我将为您查找相关壁纸": "我將為您查找相關壁紙", "没给定指令": "沒給定指令", "音频内容是": "音頻內容是", "用该压缩包+ConversationHistoryArchive进行反馈": "用該壓縮包+ConversationHistoryArchive進行反饋", "总结音频": "總結音頻", "等待用户的再次调用": "等待用戶的再次調用", "永远给定None": "永遠給定None", "论文概况": "論文概況", "建议使用英文单词": "建議使用英文單詞", "刷新Gradio前端界面": "刷新Gradio前端界面", "列表递归接龙": "列表遞歸接龍", "赋予插件状态": "賦予插件狀態", "构建完成": "構建完成", "避免多用户干扰": "避免多用戶干擾", "当前工作路径为": "當前工作路徑為", "用黑色标注转换区": "用黑色標注轉換區", "压缩包": "壓縮包", "刷新页面即可以退出KnowledgeBaseQA模式": "刷新頁面即可以退出KnowledgeBaseQA模式", "拆分过长的Markdown文件": "拆分過長的Markdown文件", "生成时间戳": "生成時間戳", "尚未完成全部响应": "尚未完成全部響應", "HotReload的装饰器函数": "HotReload的裝飾器函數", "请务必用 pip install -r requirements.txt 指令安装依赖": "請務必用 pip install -r requirements.txt 指令安裝依賴", "TGUI不支持函数插件的实现": "TGUI不支持函數插件的實現", "音频文件名": "音頻文件名", "找不到任何音频或视频文件": "找不到任何音頻或視頻文件", "音频解析结果": "音頻解析結果", "如果使用ChatGLM2微调模型": "如果使用ChatGLM2微調模型", "限制的3/4时": "限制的3/4時", "获取回复": "獲取回復", "对话历史写入": "對話歷史寫入", "记录删除注释后的文本": "記錄刪除註釋後的文本", "整理结果为压缩包": "整理結果為壓縮包", "注意事项": "注意事項", "请耐心等待": "請耐心等待", "在执行完成之后": "在執行完成之後", "参数简单": "參數簡單", "ArXiv论文精细翻译": "ArXiv論文精細翻譯", "备份和下载": "備份和下載", "当前报错的latex代码处于第": "當前報錯的latex代碼處於第", "Markdown翻译": "Markdown翻譯", "英文Latex项目全文纠错": "英文Latex項目全文校對", "获取预处理函数": "獲取預處理函數", "add gpt task 创建子线程请求gpt": "add gpt task 創建子線程請求gpt", "一个包含所有切割音频片段文件路径的列表": "一個包含所有切割音頻片段文件路徑的列表", "解析arxiv网址失败": "解析arxiv網址失敗", "PDF文件所在的路径": "PDF文件所在路徑", "取评分最高者返回": "取評分最高者返回", "此插件处于开发阶段": "此插件處於開發階段", "如果已经存在": "如果已經存在", "或者不在环境变量PATH中": "或者不在環境變量PATH中", "目前支持的格式": "目前支持的格式", "将多文件tex工程融合为一个巨型tex": "將多文件tex工程融合為一個巨型tex", "暂不提交": "暫不提交", "调用函数": "調用函數", "编译转化后的PDF": "編譯轉化後的PDF", "将代码转为动画": "將代碼轉為動畫", "本地Latex论文精细翻译": "本地Latex論文精細翻譯", "删除或修改歧义文件": "刪除或修改歧義文件", "其他操作系统表现未知": "其他操作系統表現未知", "此插件Windows支持最佳": "此插件Windows支持最佳", "构建知识库": "構建知識庫", "每个切割音频片段的时长": "每個切割音頻片段的時長", "用latex编译为PDF对修正处做高亮": "用latex編譯為PDF對修正處做高亮", "行": "行", "= 2 通过一些Latex模板中常见": "= 2 通過一些Latex模板中常見", "如参考文献、脚注、图注等": "如參考文獻、腳註、圖註等", "期望格式例如": "期望格式例如", "翻译内容可靠性无保障": "翻譯內容可靠性無保障", "请用一句话概括这些文件的整体功能": "請用一句話概括這些文件的整體功能", "段音频完成了吗": "段音頻完成了嗎", "填入azure openai api的密钥": "填入azure openai api的密鑰", "文本碎片重组为完整的tex片段": "文本碎片重組為完整的tex片段", "吸收在42行以內的begin-end組合": "吸收在42行以內的begin-end組合", "屬性": "屬性", "必須包含documentclass": "必須包含documentclass", "等待GPT響應": "等待GPT響應", "當前語言模型溫度設定": "當前語言模型溫度設定", "模型選擇是": "選擇的模型為", "reverse 操作必須放在最後": "reverse 操作必須放在最後", "將子線程的gpt結果寫入chatbot": "將子線程的gpt結果寫入chatbot", "默認為default": "默認為default", "目前對機器學習類文獻轉化效果最好": "目前對機器學習類文獻轉化效果最好", "主程序即將開始": "主程序即將開始", "點擊“停止”鍵可終止程序": "點擊“停止”鍵可終止程序", "正在處理": "正在處理", "請立即終止程序": "請立即停止程序", "將 chatglm 直接對齊到 chatglm2": "將 chatglm 直接對齊到 chatglm2", "音頻助手": "音頻助手", "正在構建知識庫": "正在構建知識庫", "請向下翻": "請向下滾動頁面", "後面是英文冒號": "後面是英文冒號", "無法找到一個主Tex文件": "無法找到一個主Tex文件", "使用中文总结音频“": "使用中文總結音頻", "该PDF由GPT-Academic开源项目调用大语言模型+Latex翻译插件一键生成": "該PDF由GPT-Academic開源項目調用大語言模型+Latex翻譯插件一鍵生成", "开始生成动画": "開始生成動畫", "完成情况": "完成情況", "然后进行问答": "然後進行問答", "为啥chatgpt会把cite里面的逗号换成中文逗号呀": "為啥chatgpt會把cite裡面的逗號換成中文逗號呀", "暂时不支持历史消息": "暫時不支持歷史消息", "项目Github地址 \\url{https": "項目Github地址 \\url{https", "Newbing 请求失败": "Newbing 請求失敗", "根据自然语言执行插件命令": "根據自然語言執行插件命令", "迭代上一次的结果": "迭代上一次的結果", "azure和api2d请求源": "azure和api2d請求源", "格式如org-xxxxxxxxxxxxxxxxxxxxxxxx": "格式如org-xxxxxxxxxxxxxxxxxxxxxxxx", "推荐http": "推薦http", "将要匹配的模式": "將要匹配的模式", "代理数据解析失败": "代理數據解析失敗", "创建存储切割音频的文件夹": "創建存儲切割音頻的文件夾", "用红色标注处保留区": "用紅色標注處保留區", "至少一个线程任务Token溢出而失败": "至少一個線程任務Token溢出而失敗", "获取Slack消息失败": "獲取Slack消息失敗", "极少数情况下": "極少數情況下", "辅助gpt生成代码": "輔助gpt生成代碼", "生成图像": "生成圖像", "最多收纳多少个网页的结果": "最多收納多少個網頁的結果", "获取图片URL": "獲取圖片URL", "正常状态": "正常狀態", "编译原始PDF": "編譯原始PDF", "SummarizeAudioAndVideo内容": "音視頻摘要內容", "Latex文件融合完成": "Latex文件融合完成", "获取线程锁": "獲取線程鎖", "SlackClient类用于与Slack API进行交互": "SlackClient類用於與Slack API進行交互", "检测到arxiv文档连接": "檢測到arxiv文檔連接", "--读取参数": "--讀取參數", "如果您是论文原作者": "如果您是論文原作者", "5刀": "5美元", "转化PDF编译是否成功": "轉換PDF編譯是否成功", "生成带有段落标签的HTML代码": "生成帶有段落標籤的HTML代碼", "目前不支持历史消息查询": "目前不支持歷史消息查詢", "将文件添加到chatbot cookie中": "將文件添加到chatbot cookie中", "多线程操作已经开始": "多線程操作已經開始", "请求子进程": "請求子進程", "将Unsplash API中的PUT_YOUR_QUERY_HERE替换成描述该事件的一个最重要的单词": "將Unsplash API中的PUT_YOUR_QUERY_HERE替換成描述該事件的一個最重要的單詞", "不能加载Claude组件": "不能加載Claude組件", "请仔细鉴别并以原文为准": "請仔細鑒別並以原文為準", "否则结束循环": "否則結束循環", "插件可读取“输入区”文本/路径作为参数": "插件可讀取“輸入區”文本/路徑作為參數", "网络错误": "網絡錯誤", "想象一个穿着者": "想像一個穿著者", "避免遗忘导致死锁": "避免遺忘導致死鎖", "保证括号正确": "保證括號正確", "报错信息": "錯誤信息", "提取视频中的音频": "提取視頻中的音頻", "初始化音频采集线程": "初始化音頻採集線程", "参考文献转Bib": "參考文獻轉Bib", "阿里云实时语音识别 配置难度较高 仅建议高手用户使用 参考 https": "阿里云即時語音識別配置難度較高，僅建議高手用戶使用，參考 https", "使用时": "使用時", "处理个别特殊插件的锁定状态": "處理個別特殊插件的鎖定狀態", "但通常不会出现在正文": "但通常不會出現在正文", "此函数逐渐地搜索最长的条目进行剪辑": "此函數逐漸地搜索最長的條目進行剪輯", "给出指令": "給出指令", "读取音频文件": "讀取音頻文件", "========================================= 插件主程序1 =====================================================": "========================================= 插件主程序1 =====================================================", "带超时倒计时": "帶超時倒計時", "禁止移除或修改此警告": "禁止移除或修改此警告", "ChatGLMFT尚未加载": "ChatGLMFT尚未加載", "双手离开鼠标键盘吧": "雙手離開鼠標鍵盤吧", "缺少的依赖": "缺少的依賴", "的单词": "的單詞", "中读取数据构建知识库": "中讀取數據構建知識庫", "函数热更新是指在不停止程序运行的情况下": "函數熱更新是指在不停止程序運行的情況下", "建议低于1": "建議低於1", "转化PDF编译已经成功": "轉換PDF編譯已經成功", "出问题了": "出問題了", "欢迎使用 MOSS 人工智能助手！": "歡迎使用 MOSS 人工智能助手！", "正在精细切分latex文件": "正在精細切分LaTeX文件", "”补上": "”補上", "网络代理状态": "網路代理狀態", "依赖检测通过": "依賴檢測通過", "默认为default": "預設為default", "Call MOSS fail 不能正常加载MOSS的参数": "呼叫MOSS失敗，無法正常載入MOSS參數", "音频助手": "音頻助手", "次编译": "次編譯", "其他错误": "其他錯誤", "属性": "屬性", "主程序即将开始": "主程式即將開始", "Aliyun音频服务异常": "Aliyun音頻服務異常", "response中会携带traceback报错信息": "response中會攜帶traceback錯誤信息", "一些普通功能模块": "一些普通功能模組", "和openai的连接容易断掉": "和openai的連線容易斷掉", "请检查ALIYUN_TOKEN和ALIYUN_APPKEY是否过期": "請檢查ALIYUN_TOKEN和ALIYUN_APPKEY是否過期", "调用Claude时": "呼叫Claude時", "插件锁定中": "插件鎖定中", "将子线程的gpt结果写入chatbot": "將子線程的gpt結果寫入chatbot", "当下一次用户提交时": "當下一次使用者提交時", "先上传数据集": "先上傳資料集", "请在此处追加更细致的矫错指令": "請在此處追加更細緻的矯錯指令", "无法找到一个主Tex文件": "無法找到一個主Tex文件", "gpt写的": "gpt寫的", "预处理": "預處理", "但大部分场合下并不需要修改": "但大部分場合下並不需要修改", "正在构建知识库": "正在建構知識庫", "开始请求": "開始請求", "根据以上分析": "根據以上分析", "需要特殊依赖": "需要特殊依賴", "用于基础的对话功能": "用於基礎的對話功能", "且没有代码段": "且沒有程式碼段", "取决于": "取決於", "openai的官方KEY需要伴隨組織編碼": "請填入組織編碼", "等待newbing回覆的片段": "等待newbing回覆的片段", "调用缓存": "呼叫快取", "模型选择是": "模型選擇為", "当前大语言模型": "當前大語言模型", "然后转移到指定的另一个路径中": "然後轉移到指定的另一個路徑中", "请向下翻": "請向下滾動", "内容太长了都会触发token数量溢出的错误": "內容太長會觸發token數量溢出的錯誤", "每一块": "每一塊", "详情信息见requirements.txt": "詳細信息見requirements.txt", "没有提供高级参数功能说明": "沒有提供高級參數功能說明", "上传Latex项目": "上傳Latex項目", "请立即终止程序": "請立即終止程式", "解除插件锁定": "解除插件鎖定", "意外Json结构": "意外Json結構", "必须包含documentclass": "必須包含documentclass", "10个文件为一组": "10個文件為一組", "openai的官方KEY需要伴随组织编码": "openai的官方KEY需要伴隨組織編碼", "重置文件的创建时间": "重置文件的創建時間", "尽量是完整的一个section": "盡量是完整的一個section", "报告如何远程获取": "報告如何遠程獲取", "work_folder = Latex预处理": "work_folder = Latex預處理", "吸收在42行以内的begin-end组合": "吸收在42行以內的begin-end組合", "后面是英文冒号": "後面是英文冒號", "使用latexdiff生成论文转化前后对比": "使用latexdiff生成論文轉化前後對比", "首先你在英文语境下通读整篇论文": "首先你在英文語境下通讀整篇論文", "为了防止大语言模型的意外谬误产生扩散影响": "為了防止大語言模型的意外謬誤產生擴散影響", "发现已经存在翻译好的PDF文档": "發現已經存在翻譯好的PDF文檔", "点击“停止”键可终止程序": "點擊“停止”鍵可終止程序", "数学GenerateAnimations": "數學GenerateAnimations", "随变按钮的回调函数注册": "隨變按鈕的回調函數註冊", "history至少释放二分之一": "history至少釋放二分之一", "当前语言模型温度设定": "當前語言模型溫度設定", "等待GPT响应": "等待GPT響應", "正在处理": "正在處理", "多线程翻译开始": "多線程翻譯開始", "reverse 操作必须放在最后": "reverse 操作必須放在最後", "等待newbing回复的片段": "等待newbing回覆的片段", "开始下载": "開始下載", "将 chatglm 直接对齐到 chatglm2": "將 chatglm 直接對齊到 chatglm2", "以上材料已经被写入": "以上材料已經被寫入", "上传文件自动修正路径": "上傳文件自動修正路徑", "然后请使用Markdown格式封装": "然後請使用Markdown格式封裝", "目前对机器学习类文献转化效果最好": "目前對機器學習類文獻轉化效果最好", "检查结果": "檢查結果", "、地址": "地址", "如.md": "如.md", "使用Unsplash API": "使用Unsplash API", "**输入参数说明**": "**輸入參數說明**", "新版本可用": "新版本可用", "找不到任何python文件": "找不到任何python文件", "知乎": "知乎", "日": "日", "“喂狗”": "“喂狗”", "第4步": "第4步", "退出": "退出", "使用 Unsplash API": "使用 Unsplash API", "非Openai官方接口返回了错误": "非Openai官方接口返回了错误", "用来描述你的要求": "用來描述你的要求", "自定义API KEY格式": "自定義API KEY格式", "前缀": "前綴", "会被加在你的输入之前": "會被加在你的輸入之前", "api2d等请求源": "api2d等請求源", "高危设置! 常规情况下不要修改! 通过修改此设置": "高危設置！常規情況下不要修改！通過修改此設置", "即将编译PDF": "即將編譯PDF", "默认 secondary": "默認 secondary", "正在从github下载资源": "正在從github下載資源", "响应异常": "響應異常", "我好！": "我好！", "无需填写": "無需填寫", "缺少": "缺少", "请问什么是质子": "請問什麼是質子", "如果要使用": "如果要使用", "重组": "重組", "一个单实例装饰器": "一個單實例裝飾器", "的参数！": "的參數！", "🏃‍♂️🏃‍♂️🏃‍♂️ 子进程执行": "🏃‍♂️🏃‍♂️🏃‍♂️ 子進程執行", "失败时": "失敗時", "没有设置ANTHROPIC_API_KEY选项": "沒有設置ANTHROPIC_API_KEY選項", "并设置参数": "並設置參數", "格式": "格式", "按钮是否可见": "按鈕是否可見", "即可见": "即可見", "创建request": "創建request", "的依赖": "的依賴", "⭐主进程执行": "⭐主進程執行", "最后一步处理": "最後一步處理", "没有设置ANTHROPIC_API_KEY": "沒有設置ANTHROPIC_API_KEY", "的参数": "的參數", "逆转出错的段落": "逆轉出錯的段落", "本项目现已支持OpenAI和Azure的api-key": "本項目現已支持OpenAI和Azure的api-key", "前者是API2D的结束条件": "前者是API2D的結束條件", "增强稳健性": "增強穩健性", "消耗大量的内存": "消耗大量的內存", "您的 API_KEY 不满足任何一种已知的密钥格式": "您的API_KEY不滿足任何一種已知的密鑰格式", "⭐单线程方法": "⭐單線程方法", "是否在触发时清除历史": "是否在觸發時清除歷史", "⭐多线程方法": "多線程方法", "不能正常加载": "無法正常加載", "举例": "舉例", "即不处理之前的对话历史": "即不處理之前的對話歷史", "尚未加载": "尚未加載", "防止proxies单独起作用": "防止proxies單獨起作用", "默认 False": "默認 False", "检查USE_PROXY": "檢查USE_PROXY", "响应中": "響應中", "扭转的范围": "扭轉的範圍", "后缀": "後綴", "调用": "調用", "创建AcsClient实例": "創建AcsClient實例", "安装": "安裝", "会被加在你的输入之后": "會被加在你的輸入之後", "配合前缀可以把你的输入内容用引号圈起来": "配合前綴可以把你的輸入內容用引號圈起來", "例如翻译、解释代码、润色等等": "例如翻譯、解釋代碼、潤色等等", "后者是OPENAI的结束条件": "後者是OPENAI的結束條件", "标注节点的行数范围": "標註節點的行數範圍", "默认 True": "默認 True", "将两个PDF拼接": "將兩個PDF拼接"}