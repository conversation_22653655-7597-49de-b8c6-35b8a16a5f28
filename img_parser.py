
import cv2
import easyocr
from paddleocr import PaddleOCR

class OCRManager:
    """OCR管理器，使用单例模式避免重复初始化OCR对象"""
    _instance = None
    _paddle_ocr = None
    _easy_ocr = None

    def __new__(cls):
        if cls._instance is None:
            cls._instance = super(OCRManager, cls).__new__(cls)
        return cls._instance

    def get_paddle_ocr(self):
        """获取PaddleOCR实例，懒加载"""
        if self._paddle_ocr is None:
            self._paddle_ocr = PaddleOCR(
                use_doc_orientation_classify=False,
                use_doc_unwarping=False,
                use_textline_orientation=False,
                enable_mkldnn=False
            )
        return self._paddle_ocr

    def get_easy_ocr(self):
        """获取EasyOCR实例，懒加载"""
        if self._easy_ocr is None:
            self._easy_ocr = easyocr.Reader(
                ["en", "ch_sim"],
                model_storage_directory='./asset/model'
            )
        return self._easy_ocr

# 全局OCR管理器实例
ocr_manager = OCRManager()

def parse_img_document_by_paddleocr(handwritting_path, output_text_path):
    """解析手写文档并保存到文件"""
    output_text = img_ocr_parser_by_paddleocr(handwritting_path)
    with open(output_text_path, 'w', encoding='utf-8') as text_file:
        text_file.write(output_text)

def img_ocr_parser_by_paddleocr(handwritting_path, conf_thr: float = 0.2):
    """使用PaddleOCR解析手写文档"""
    ocr = ocr_manager.get_paddle_ocr()
    result = ocr.predict(handwritting_path)
    output_str = ""
    for res in result:
        rec_texts = res['rec_texts']
        for line_text in rec_texts:
            output_str += line_text+ '\n'
    # 结果块之间，先不加空行了
    # output_str += '\n'
    print(output_str)
    return output_str.strip()

def parse_img_document_by_easyocr(img_path, output_text_path):
    """解析图片文档并保存到文件"""
    output_text = img_ocr_parser_by_easyocr(img_path)
    with open(output_text_path, 'w', encoding='utf-8') as text_file:
        text_file.write(output_text)

def img_ocr_parser_by_easyocr(img_path, conf_thr: float = 0.2):
    """使用EasyOCR解析图片文档"""
    img = cv2.imread(img_path)
    reader = ocr_manager.get_easy_ocr()
    result = reader.readtext(img)
    output_str = ""
    for _, text, conf in result:
        if conf > conf_thr:
            output_str += text + '\n'
    return output_str.strip()

if __name__ == "__main__":
    # parse_img_document('asset/报案平台资料审核素材2/报案平台资料审核素材/中联硅谷（北京）股权投资基金管理有限公司/报案书/截图20250609154213.png', 'test_output/test报案书1.txt')
    res = parse_img_document_by_paddleocr('asset/报案平台资料审核素材2/报案平台资料审核素材/中联硅谷（北京）股权投资基金管理有限公司/报案书/截图20250609154213.png', 'test_output/test报案书2.txt')
