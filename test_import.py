#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试导入是否正常
"""

def test_imports():
    try:
        print("测试导入 utils.file_utils...")
        from utils.file_utils import read_file_content
        print("✅ utils.file_utils 导入成功")
        
        print("测试导入 core.analysis_engine...")
        from core.analysis_engine import process_writing_critique, GPT_ACADEMIC_AVAILABLE
        print("✅ core.analysis_engine 导入成功")
        
        print(f"GPT Academic 状态: {'✅ 可用' if GPT_ACADEMIC_AVAILABLE else '❌ 不可用'}")
        
        print("测试读取文件功能...")
        # 创建一个测试文件
        with open('test.txt', 'w', encoding='utf-8') as f:
            f.write("这是一个测试文件")
        
        content = read_file_content('test.txt')
        print(f"✅ 文件读取成功: {content}")
        
        # 清理测试文件
        import os
        os.remove('test.txt')
        
        print("🎉 所有测试通过！")
        
    except Exception as e:
        print(f"❌ 测试失败: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_imports()
