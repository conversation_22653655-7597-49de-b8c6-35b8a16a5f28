# 结构化信息提取配置文件
# 定义需要从文档中提取的结构化数据字段

# 提取配置元信息
meta:
  version: "1.0"
  description: "默认的结构化信息提取配置"
  created_at: "2024-01-01"
  updated_at: "2024-01-01"

# 提取字段定义
fields:
  # 基本信息字段
  basic_info:
    title:
      description: "文档标题"
      type: "string"
      required: true
      example: "2024年年度报告"
    
    document_type:
      description: "文档类型"
      type: "string"
      required: false
      options: ["年报", "季报", "公告", "合同", "报告", "其他"]
      example: "年报"
    
    date:
      description: "文档日期"
      type: "date"
      required: false
      format: "YYYY-MM-DD"
      example: "2024-03-31"
    
    author:
      description: "文档作者/发布机构"
      type: "string"
      required: false
      example: "某某银行股份有限公司"

  # 财务信息字段（适用于金融文档）
  financial_info:
    revenue:
      description: "营业收入"
      type: "number"
      unit: "万元"
      required: false
      example: 1000000
    
    profit:
      description: "净利润"
      type: "number"
      unit: "万元"
      required: false
      example: 200000
    
    assets:
      description: "总资产"
      type: "number"
      unit: "万元"
      required: false
      example: 50000000
    
    liabilities:
      description: "总负债"
      type: "number"
      unit: "万元"
      required: false
      example: 45000000

  # 关键指标字段
  key_metrics:
    roe:
      description: "净资产收益率"
      type: "percentage"
      required: false
      example: 12.5
    
    roa:
      description: "资产收益率"
      type: "percentage"
      required: false
      example: 1.2
    
    capital_adequacy_ratio:
      description: "资本充足率"
      type: "percentage"
      required: false
      example: 14.8

  # 风险信息字段
  risk_info:
    credit_risk:
      description: "信用风险描述"
      type: "text"
      required: false
      example: "信用风险总体可控"
    
    market_risk:
      description: "市场风险描述"
      type: "text"
      required: false
      example: "市场风险管理有效"
    
    operational_risk:
      description: "操作风险描述"
      type: "text"
      required: false
      example: "操作风险控制良好"

  # 业务信息字段
  business_info:
    main_business:
      description: "主营业务"
      type: "text"
      required: false
      example: "商业银行业务"
    
    business_segments:
      description: "业务分部"
      type: "list"
      required: false
      example: ["公司银行", "零售银行", "金融市场"]
    
    geographic_distribution:
      description: "地理分布"
      type: "list"
      required: false
      example: ["华东地区", "华南地区", "华北地区"]

# 提取规则配置
extraction_rules:
  # 数值提取规则
  number_patterns:
    - "\\d+\\.?\\d*万元"
    - "\\d+\\.?\\d*亿元"
    - "\\d+\\.?\\d*%"
  
  # 日期提取规则
  date_patterns:
    - "\\d{4}年\\d{1,2}月\\d{1,2}日"
    - "\\d{4}-\\d{1,2}-\\d{1,2}"
    - "\\d{4}/\\d{1,2}/\\d{1,2}"
  
  # 关键词匹配规则
  keywords:
    revenue: ["营业收入", "总收入", "营收"]
    profit: ["净利润", "利润总额", "税后利润"]
    assets: ["总资产", "资产总额"]
    liabilities: ["总负债", "负债总额"]

# 输出格式配置
output_format:
  format: "yaml"  # 支持 yaml, json, csv
  include_metadata: true
  include_confidence: true
  include_source_text: false
