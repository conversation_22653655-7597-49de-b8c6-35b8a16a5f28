#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
模板驱动写作系统
包含文档对象和模板驱动写作类
"""

import os
import tempfile
import shutil
import json
import re
from datetime import datetime
from typing import List, Optional, Dict, Any, Union
from dataclasses import dataclass, field
from llm_helper import LLMAPIClient
from config import ConfigManager
from document import DocumentObject, DocumentParser


def clean_json_string(json_str: str) -> str:
    """
    清理JSON字符串中的控制字符

    Args:
        json_str: 原始JSON字符串

    Returns:
        str: 清理后的JSON字符串
    """
    # 替换常见的控制字符
    json_str = json_str.replace('\n', '\\n')  # 换行符
    json_str = json_str.replace('\r', '\\r')  # 回车符
    json_str = json_str.replace('\t', '\\t')  # 制表符
    json_str = json_str.replace('\b', '\\b')  # 退格符
    json_str = json_str.replace('\f', '\\f')  # 换页符
    json_str = json_str.replace('\v', '\\v')  # 垂直制表符

    # 处理其他控制字符 (ASCII 0-31，除了已处理的)
    for i in range(32):
        if i not in [9, 10, 13]:  # 保留 \t, \n, \r (已在上面处理)
            char = chr(i)
            if char in json_str:
                json_str = json_str.replace(char, f'\\u{i:04x}')

    return json_str


def safe_json_loads(json_str: str) -> dict:
    """
    安全的JSON解析函数

    Args:
        json_str: JSON字符串

    Returns:
        dict: 解析后的字典，如果解析失败返回空字典
    """
    try:
        # 首先尝试直接解析
        return json.loads(json_str)
    except json.JSONDecodeError as e:
        print(f"   ⚠️  JSON解析失败，尝试清理控制字符: {e}")
        try:
            # 清理控制字符后再次尝试
            cleaned_json = clean_json_string(json_str)
            return json.loads(cleaned_json)
        except json.JSONDecodeError as e2:
            print(f"   ⚠️  清理后仍然解析失败: {e2}")
            print(f"   📝 问题JSON片段: {json_str[:200]}...")
            return {}


@dataclass
class TemplateSection:
    """
    模板分析后的段落结构
    """
    section_id: int                         # 段落ID
    title: str                              # 段落标题
    original_text: str                      # 原始文本
    analysis_result: str                    # 大模型分析结果
    content_type: str                       # 内容类型（如：introduction, body, conclusion等）
    key_points: List[str] = field(default_factory=list)  # 关键要点
    writing_requirements: str = ""          # 写作要求
    generated_content: str = ""             # 生成的内容

    def to_dict(self) -> Dict[str, Any]:
        return {
            'section_id': self.section_id,
            'title': self.title,
            'original_text': self.original_text,
            'analysis_result': self.analysis_result,
            'content_type': self.content_type,
            'key_points': self.key_points,
            'writing_requirements': self.writing_requirements,
            'generated_content': self.generated_content
        }


@dataclass
class WritingResult:
    """
    写作结果
    """
    final_content: str                      # 最终内容
    sections: List[TemplateSection]         # 段落列表
    validation_issues: List[str] = field(default_factory=list)  # 验证问题
    iteration_count: int = 0                # 迭代次数
    total_time: float = 0.0                 # 总耗时
    metadata: Dict[str, Any] = field(default_factory=dict)  # 元数据

    def to_dict(self) -> Dict[str, Any]:
        return {
            'final_content': self.final_content,
            'sections': [section.to_dict() for section in self.sections],
            'validation_issues': self.validation_issues,
            'iteration_count': self.iteration_count,
            'total_time': self.total_time,
            'metadata': self.metadata
        }





class TemplateDrivenWriting:
    """
    模板驱动写作系统主类
    """

    def __init__(self, temp_base_dir: Optional[str] = None, llm_client: Optional[LLMAPIClient] = None):
        """
        初始化模板驱动写作系统

        Args:
            temp_base_dir: 临时文件基础目录
            llm_client: LLM客户端，如果不提供则从配置文件创建
        """
        self.temp_base_dir = temp_base_dir or tempfile.gettempdir()
        self.template: Optional[DocumentObject] = None
        self.resources: List[DocumentObject] = []
        self._temp_dirs: List[str] = []  # 跟踪创建的临时目录

        # 初始化LLM客户端
        if llm_client:
            self.llm_client = llm_client
        else:
            try:
                config = ConfigManager()
                api_key = config.get('apis.qwen.api_key')
                base_url = config.get('apis.qwen.base_url')
                self.llm_client = LLMAPIClient(api_key, base_url)
            except Exception as e:
                print(f"警告: 无法初始化LLM客户端: {e}")
                self.llm_client = None

    def set_template(self, file_path: str) -> DocumentObject:
        """
        设置模板文档

        Args:
            file_path: 模板文件路径

        Returns:
            DocumentObject: 解析后的模板文档对象
        """
        temp_dir = tempfile.mkdtemp(prefix="template_", dir=self.temp_base_dir)
        self._temp_dirs.append(temp_dir)

        self.template = DocumentParser.parse_document(file_path, temp_dir)
        return self.template

    def add_resource(self, file_path: str) -> DocumentObject:
        """
        添加资源文档

        Args:
            file_path: 资源文件路径

        Returns:
            DocumentObject: 解析后的资源文档对象
        """
        temp_dir = tempfile.mkdtemp(prefix="resource_", dir=self.temp_base_dir)
        self._temp_dirs.append(temp_dir)

        resource_doc = DocumentParser.parse_document(file_path, temp_dir)
        self.resources.append(resource_doc)
        return resource_doc

    def add_resources_from_directory(self, directory: str, recursive: bool = False) -> List[DocumentObject]:
        """
        从目录批量添加资源文档

        Args:
            directory: 目录路径
            recursive: 是否递归搜索子目录

        Returns:
            List[DocumentObject]: 解析后的资源文档对象列表
        """
        if not os.path.exists(directory):
            raise FileNotFoundError(f"目录不存在: {directory}")

        added_resources = []

        if recursive:
            for root, dirs, files in os.walk(directory):
                for file in files:
                    file_path = os.path.join(root, file)
                    if DocumentParser.is_supported(file_path):
                        try:
                            resource_doc = self.add_resource(file_path)
                            added_resources.append(resource_doc)
                        except Exception as e:
                            print(f"警告: 无法解析文件 {file_path}: {e}")
        else:
            for file in os.listdir(directory):
                file_path = os.path.join(directory, file)
                if os.path.isfile(file_path) and DocumentParser.is_supported(file_path):
                    try:
                        resource_doc = self.add_resource(file_path)
                        added_resources.append(resource_doc)
                    except Exception as e:
                        print(f"警告: 无法解析文件 {file_path}: {e}")

        return added_resources

    def clear_resources(self):
        """清空所有资源文档"""
        self.resources.clear()

    def clear_template(self):
        """清空模板文档"""
        self.template = None

    def get_template_info(self) -> Optional[Dict[str, Any]]:
        """获取模板信息"""
        return self.template.to_dict() if self.template else None

    def get_resources_info(self) -> List[Dict[str, Any]]:
        """获取所有资源信息"""
        return [resource.to_dict() for resource in self.resources]

    def get_summary(self) -> Dict[str, Any]:
        """获取系统摘要信息"""
        total_resources = len(self.resources)
        total_content_length = sum(resource.content_length for resource in self.resources)
        total_parse_time = sum(resource.parse_time for resource in self.resources)

        if self.template:
            total_content_length += self.template.content_length
            total_parse_time += self.template.parse_time

        resource_types = {}
        for resource in self.resources:
            resource_types[resource.file_type] = resource_types.get(resource.file_type, 0) + 1

        return {
            'has_template': self.template is not None,
            'template_info': self.get_template_info(),
            'total_resources': total_resources,
            'resource_types': resource_types,
            'total_content_length': total_content_length,
            'total_parse_time': total_parse_time,
            'resources_with_images': sum(1 for r in self.resources if r.has_images)
        }

    def cleanup(self):
        """清理临时文件"""
        for temp_dir in self._temp_dirs:
            if os.path.exists(temp_dir):
                try:
                    shutil.rmtree(temp_dir)
                except Exception as e:
                    print(f"警告: 无法删除临时目录 {temp_dir}: {e}")
        self._temp_dirs.clear()

    def __del__(self):
        """析构函数，自动清理临时文件"""
        self.cleanup()

    def __enter__(self):
        """上下文管理器入口"""
        return self

    def __exit__(self, exc_type, exc_val, exc_tb):
        """上下文管理器出口，自动清理"""
        self.cleanup()

    # ==================== 写作用到的prompt ====================

    prompt_partition = '''你的任务是分析给定文章的结构，并根据段落内容的相关性将其分割成连贯的片段。
    请遵循以下说明：
    1. 分析文章内容，识别具有相关内容的各个部分。
    2. 将文章分割成若干片段，每个片段需保持其上下文的完整性。
    3. 为每个分割后的片段提供一个摘要。
    4. 确保所有片段组合起来时，能够完全重建原始文章内容，不做任何更改。
    请将结果以 JSON 格式输出，结构如下：
    * "article_summary": 整篇文章的综合性摘要。
    * "partitions": 一个包含各分割片段的数组，其中每个片段包含：
        * "seq_no": 片段序号。
        * "slice": 该片段的文本内容。
        * "summary": 该片段内容的摘要。
    请根据指定的指南执行分析和分割工作。
    待分析的文章：
    """{article}"""
    '''

    # prompt_partition = '''你的任务是将文章按语义相关性分割成逻辑连贯的片段。请严格遵循以下步骤：
    # 1. **结构分析**
    #    - 识别文章的自然断点（如话题转换、论点转移、案例切换）
    #    - 标记每个独立语义单元的核心主题
    # 2. **片段分割准则**
    #    - 每个片段必须是原文的**连续文本块**
    #    - 保持最小完整语义单元（单段或多段组成逻辑整体）
    #    - 禁止修改/删减原文（包括标点和空格）
    # 3. **摘要要求**
    #    - 片段摘要：用1-2句话提炼核心内容（20-50字）
    #    - 全文摘要：综合各片段要点，保持客观（50-100字）
    # 4. **输出规范**
    # ```json
    # {
    #   "article_summary": "全文摘要",
    #   "partitions": [
    #     {
    #       "seq_no": 1,
    #       "slice": "原文连续文本",
    #       "summary": "片段摘要"
    #     }
    #   ]
    # }'''

    # prompt_object_replace = '''你的任务是生成写作指导，将新主题深入整合到提供的文章中，超越简单的主题替换，运用先进的语义提取技术。
    # 需要分析和转换的文章：
    # """{article}"""
    # 文章中的原始主题是："{A}"
    # 要整合的新主题是："{B}"
    # 请将结果以 JSON 格式输出，结构如下：
    # {
    #   "instruction": "提供详细的写作指导，说明文章内容应该如何调整以聚焦于 '{B}'，考虑叙事结构、语调以及与新主题相关的关键元素。避免简单的替换；而是重新构建文章以与 '{B}' 保持一致。",
    #   "warning": "描述注意事项，说明如何在引入新主题 '{B}' 时保持原文的连贯性、背景和意图。确保所有观点仍然相关，并且叙事流程支持这种转变而不丢失原意。"
    # }
    # '''

    prompt_object_replace = '''你的任务是生成写作指导，将新主题深入整合到提供的文章段落中，超越简单的主题替换，运用先进的语义提取技术。
    需要分析和转换的文章段落：
    """{article}"""
    文章中的原始主题是："{A}"
    要整合的新主题是："{B}"
    请将结果以 JSON 格式输出，结构如下：
    * "instruction": 提供详细的写作指导，说明文章内容应该如何调整以聚焦于 '{B}'，考虑叙事结构、语调以及与新主题相关的关键元素。避免简单的替换；而是重新构建文章以与 '{B}' 保持一致。
    * "warning": 描述注意事项，说明如何在引入新主题'{B}'时保持原文的连贯性、背景和意图。应提示检查原有观点背后的数据和事实支撑有哪些，转换主题后是否依然成立。
    '''

    prompt_search = '''你的任务是根据以下素材和写作指导，提取相关的片段，这些片段在素材中可以是不连续的。如果没有相关信息，请返回空，而不要生成幻觉信息。返回的结构为JSON格式。
    素材："""{resource}"""
    写作指导："""{instruction}"""
    请将结果以 JSON 格式输出，结构如下：
    * "slices": 一个包含多个相关片段的数组，其中每个片段包含：
        * "seq_no": 片段序号。
        * "text": 该片段的文本内容。
    如果没有找到相关信息，请返回空JSON结构。
    '''

    prompt_instruction = '''你的任务是结合写作素材，优化写作指导，剔除或替换写作素材中缺失的写作指导意见，并引入写作素材中高价值内容。写作指导的目的是将新主题替换原始主题，深入整合到提供的文章段落中，超越简单的主题替换，运用先进的语义提取技术。请仔细阅读新主题的写作素材和注意事项，新主题中所有观点背后的数据和事实支撑应来自于新主题写作素材或常识，避免直接使用原主题段落内容而产生模型幻觉。
    需要分析和转换的文章段落：
    """{article}"""
    文章段落中的原始主题是："{A}"
    要整合的新主题是："{B}"
    新主题写作素材是："{reference}"
    注意事项是："{warning}"
    待优化的写作指导是："{instruction}"
    请将结果以 JSON 格式输出，结构如下：
    * "rewrite_instruction": 提供详细的写作指导，说明文章内容应该如何调整以聚焦于 '{B}'，考虑叙事结构、语调以及与新主题相关的关键元素。避免简单的替换；而是重新构建文章以与 '{B}' 保持一致。所有观点背后的数据和事实支撑应来自于新主题写作素材或常识，避免直接使用原主题文章内容而产生模型幻觉。
    '''

    prompt_rewrite = '''你的任务是根据以下写作指导，将给出的片段按照写作指导重写，同时保持行文风格和片段保持一致，并确保内容与素材保持一致。特别需要注意，所有观点内容背后的数据和事实支撑应来自于写作素材或常识，避免直接使用片段中的数据和事实支撑。
    写作指导：
    """
    {instruction}
    """
    片段：
    """
    {slice}
    """
    素材：
    """
    {resource}
    """
    请重写上述片段，并确保行文风格和片段保持一致，内容和素材保持一致。特别需要注意，所有观点内容背后的数据和事实支撑应来自于写作素材或常识，避免直接使用片段中的数据和事实支撑。
    '''

    prompt_merge = '''你的任务是将以下多个段落合并成一篇连贯的文章。请确保前后连贯，必要的时候可以调整段落的顺序，添加过渡句，并优化表达。
    输入段落：
    {paragraphs}
    请合并上述段落，生成一篇完整的文章。
    '''

    prompt_polish = '''请扮演一位资深编辑，对以下提供的文本进行深度润色与重写。 核心目标是解决文本中的重复内容和逻辑不一致问题，并显著提升文章的表述结构和整体流畅度。在优化过程中，请务必参考提供的范文的行文结构和格式规范。
具体要求如下：
1. 消除重复：
  - 仔细识别并删除冗余的句子、短语或观点。
  - 将分散在不同地方表达的相同或相似意思的内容进行整合、精炼。
  - 避免不必要的同义词堆砌或反复说明。
2. 确保一致性：
  - 检查并修正全文在术语使用（如人名、地名、专有名词、关键概念）、数据、观点立场、时态、人称（如“我们”/“本文”/“笔者”）上的前后矛盾。
  - 确保论点、论据和结论之间逻辑自洽，没有相互冲突的陈述。
3. 优化结构与逻辑（重点参考范文结构）：
  - 重点审查段落划分： 评估现有段落是否合理。必要时：
    - 拆分过于冗长、包含多个核心意思的段落。
    - 合并内容过于零碎、逻辑联系紧密的小段落。
    - 调整段落顺序：重新组织段落，使文章的整体论证逻辑（如：问题->分析->解决方案；现象->原因->影响->建议；总->分->总）更清晰、更符合认知习惯，增强行文的递进性或因果关联。【要求：在调整时，需借鉴范文的逻辑推进方式和段落间的衔接技巧。】
  - 强化段落内部结构： 确保每个段落有明确的中心句，段落内部句子之间逻辑连贯、过渡自然。【要求：参考范文如何构建段落内部逻辑（如：观点句->解释/例证->小结句）和使用过渡词。】
  - 优化整体布局： 审视引言、主体、结论（如果适用）是否功能明确、比例协调。确保文章有清晰的起承转合。【要求：参考范文的开篇引入、主体展开层次、结尾总结/升华的结构特点。】
4. 提升表述清晰度与精炼度：
  - 用词准确、专业、简洁，避免模糊不清或过于口语化的表达。【要求：参考范文的措辞风格和术语使用习惯。】
  - 句式力求多样、流畅，避免冗长复杂的句子结构。
  - 确保信息传递清晰、无歧义。
 5. 严格遵循范文格式规范：
  - 【结构框架模仿】： 润色后的文章应严格遵循范文呈现的核心行文结构（例如：引言提出问题 -> 主体分点分析原因/影响/对策 -> 结论总结展望）。如果范文使用了特定的论证逻辑（如总分总、层层递进、对比论证等），需在润色稿中体现相同的逻辑脉络。
  - 【段落组织规范】： 模仿范文的段落长度、段落间过渡方式（如使用特定连接词、承上启下句）以及段落内部展开逻辑（如：观点句 -> 解释 -> 例证 -> 小结）。
  - 【格式元素应用】： 如范文使用了标题层级（H1/H2/H3等）、列表（有序/无序）、加粗强调、引用块、特定分隔线等格式元素，润色稿应在相应位置同规格应用这些元素，以保持格式统一性和视觉层次感。
  - 【语言风格靠近】： 保持与范文相似的语言正式程度、学术严谨性（或通俗易懂性，视范文风格而定）和整体语调（客观中立、积极建设性等）。

输出要求：
- 直接输出润色重写后的完整文章。
- 保留原文的核心信息、观点和主旨。
- 重点展示你如何通过调整段落顺序、合并/拆分段落、删除重复内容、修正不一致表述、以及应用范文的结构与格式规范来优化文章。   目标是使文章逻辑更严密、结构更清晰、表达更精炼、阅读体验更流畅，并在结构与格式上与范文高度一致。   除非原文风格有特殊要求（如诗意、幽默），否则采用清晰、专业、客观的书面语风格。若范文有特定风格（如特定领域的学术风格、行业报告风格），应优先靠近范文风格。

请润色的文本：
{article}

请参考的范文：
{example}
    '''

    # ==================== 核心写作功能 ====================

    def partitions(self):
        if not self.template:
            raise ValueError("请先设置模板文档")

        if not self.llm_client:
            raise ValueError("LLM客户端未初始化")

        template_content = self.template.content
        prompt = self.prompt_partition.format(article=template_content)
        response = self.llm_client.chat(prompt)
        response_content = response['choices'][0]['message']['content'].strip()
        artile_summary = ''
        cleaned_partitions = []
        try:
            json_match = re.search(r'\{.*\}', response_content, re.DOTALL)
            if json_match:
                json_str = json_match.group()
                analysis_data = safe_json_loads(json_str)
            else:
                analysis_data = safe_json_loads(response_content)
            artile_summary = analysis_data.get('article_summary', '')
            partitions = analysis_data.get('partitions',[])
            for partition in partitions:
                if isinstance(partition, dict):
                    cleaned_partition = {
                        'seq_no': partition.get('seq_no', 0),
                        'slice': partition.get('slice', ''),
                        'summary': partition.get('summary', '')
                    }
                    cleaned_partitions.append(cleaned_partition)
        except Exception as e:
            print(f"   ⚠️  分析过程失败: {e}")

        return artile_summary, cleaned_partitions

    def object_replace(self, article, original_title, new_title):
        prompt = self.prompt_object_replace.format(article=article, A=original_title, B=new_title)
        response = self.llm_client.chat(prompt)
        response_content = response['choices'][0]['message']['content'].strip()
        try:
            json_match = re.search(r'\{.*\}', response_content, re.DOTALL)
            if json_match:
                json_str = json_match.group()
                analysis_data = safe_json_loads(json_str)
            else:
                analysis_data = safe_json_loads(response_content)
            return analysis_data.get('instruction', ''), analysis_data.get('warning', '')
        except Exception as e:
            print(f"   ⚠️  分析过程失败: {e}")
            return '', ''

    def search(self, instruction):
        cleaned_slices = []
        for resource in self.resources:
            prompt = self.prompt_search.format(resource=resource.content, instruction=instruction)
            response = self.llm_client.chat(prompt)
            response_content = response['choices'][0]['message']['content'].strip()
            try:
                json_match = re.search(r'\{.*\}', response_content, re.DOTALL)
                if json_match:
                    json_str = json_match.group()
                    analysis_data = safe_json_loads(json_str)
                else:
                    analysis_data = safe_json_loads(response_content)
                slices = analysis_data.get('slices', [])
                for s in slices:
                    if isinstance(s, dict):
                        cleaned_slice = {
                            'seq_no': s.get('seq_no', 0),
                            'text': s.get('text', ''),
                        }
                        cleaned_slices.append(cleaned_slice)

            except Exception as e:
                print(f"   ⚠️  分析过程失败: {e}")
                return []
        return cleaned_slices

    def rewrite_instruction(self, article, original_title, new_title, reference, instruction, warning):
        prompt = self.prompt_instruction.format(article=article, A=original_title, B=new_title,reference=reference,instruction=instruction,warning=warning)
        response = self.llm_client.chat(prompt)
        response_content = response['choices'][0]['message']['content'].strip()
        try:
            json_match = re.search(r'\{.*\}', response_content, re.DOTALL)
            if json_match:
                json_str = json_match.group()
                analysis_data = safe_json_loads(json_str)
            else:
                analysis_data = safe_json_loads(response_content)
            return analysis_data.get('rewrite_instruction', '')
        except Exception as e:
            print(f"   ⚠️  分析过程失败: {e}")
            return ''

    def rewrite(self, ori_paragraph, resource, instruction):
        prompt = self.prompt_rewrite.format(slice=ori_paragraph, resource=resource, instruction=instruction)
        response = self.llm_client.chat(prompt)
        response_content = response['choices'][0]['message']['content'].strip()
        return response_content

    def para_merge(self, new_paragraphs):
        prompt = self.prompt_merge.format(paragraphs=new_paragraphs)
        response = self.llm_client.chat(prompt)
        response_content = response['choices'][0]['message']['content'].strip()
        return response_content


    def polish(self, article):
        prompt = self.prompt_polish.format(article=article,example=self.template.content)
        response = self.llm_client.chat(prompt)
        response_content = response['choices'][0]['message']['content'].strip()
        return response_content

# 便利函数
def create_document_from_file(file_path: str) -> DocumentObject:
    """
    便利函数：从文件创建文档对象

    Args:
        file_path: 文件路径

    Returns:
        DocumentObject: 文档对象
    """
    return DocumentParser.parse_document(file_path)


def create_writing_system_from_directory(template_file: str, resources_dir: str) -> TemplateDrivenWriting:
    """
    便利函数：从目录创建写作系统

    Args:
        template_file: 模板文件路径
        resources_dir: 资源目录路径

    Returns:
        TemplateDrivenWriting: 写作系统实例
    """
    system = TemplateDrivenWriting()
    system.set_template(template_file)
    system.add_resources_from_directory(resources_dir)
    return system


def create_writing_system_from_files(template_file: str, resource_files: List[str]) -> TemplateDrivenWriting:
    """
    便利函数：从文件列表创建写作系统

    Args:
        template_file: 模板文件路径
        resource_files: 资源文件路径列表

    Returns:
        TemplateDrivenWriting: 写作系统实例
    """
    system = TemplateDrivenWriting()
    system.set_template(template_file)

    for resource_file in resource_files:
        try:
            system.add_resource(resource_file)
        except Exception as e:
            print(f"警告: 无法添加资源文件 {resource_file}: {e}")

    return system


def save_writing_result(result: WritingResult, output_file: str):
    """
    便利函数：保存写作结果到文件

    Args:
        result: 写作结果
        output_file: 输出文件路径
    """
    with open(output_file, 'w', encoding='utf-8') as f:
        f.write(f"# 模板驱动写作结果\n")
        f.write(f"\n生成时间: {result.metadata.get('completed_at', 'Unknown')}\n")
        f.write(f"模板文件: {result.metadata.get('template_file', 'Unknown')}\n")
        f.write(f"资源数量: {result.metadata.get('resource_count', 0)}\n")
        f.write(f"迭代次数: {result.iteration_count}\n")
        f.write(f"总耗时: {result.total_time:.2f}秒\n")

        if result.validation_issues:
            f.write(f"\n验证问题: {len(result.validation_issues)}个\n")
            for i, issue in enumerate(result.validation_issues, 1):
                f.write(f"  {i}. {issue}\n")

        f.write(f"\n{'='*60}\n")
        f.write(f"最终内容\n")
        f.write(f"{'='*60}\n\n")
        f.write(result.final_content)


if __name__ == "__main__":
    # 示例用法
    print("🚀 模板驱动写作系统测试")
    print("=" * 50)

    # 测试单个文档解析
    try:
        asset_dir = "asset"
        if os.path.exists(asset_dir):
            files = [f for f in os.listdir(asset_dir) if DocumentParser.is_supported(os.path.join(asset_dir, f))]

            if files:
                print(f"📄 测试解析文档: {files[0]}")
                doc = create_document_from_file(os.path.join(asset_dir, files[0]))
                print(f"✅ 解析成功: {doc}")
                print(f"📝 内容预览: {doc.get_content_preview()}")
                print()

                # 测试写作系统
                print("📚 测试模板驱动写作系统")
                with TemplateDrivenWriting() as system:
                    # 设置模板（使用第一个文件）
                    if len(files) > 0:
                        template_doc = system.set_template(os.path.join(asset_dir, files[0]))
                        print(f"📋 设置模板: {template_doc.file_name}")

                    # 添加资源（使用其他文件）
                    for file in files[1:]:
                        resource_doc = system.add_resource(os.path.join(asset_dir, file))
                        print(f"📄 添加资源: {resource_doc.file_name}")

                    # 显示摘要
                    summary = system.get_summary()
                    print(f"\n📊 系统摘要:")
                    print(f"   模板: {'是' if summary['has_template'] else '否'}")
                    print(f"   资源数量: {summary['total_resources']}")
                    print(f"   资源类型: {summary['resource_types']}")
                    print(f"   总内容长度: {summary['total_content_length']} 字符")
                    print(f"   总解析时间: {summary['total_parse_time']:.2f} 秒")
            else:
                print("❌ asset 目录中没有支持的文档文件")
        else:
            print("❌ asset 目录不存在")

    except Exception as e:
        print(f"❌ 测试失败: {e}")
