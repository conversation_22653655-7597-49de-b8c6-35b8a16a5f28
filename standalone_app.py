#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
独立的五角色方案点评系统 - 不依赖任何外部模型
"""

import gradio as gr
import re
from collections import Counter

def read_file_simple(file_path):
    """简单的文件读取"""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            return f.read()
    except:
        try:
            with open(file_path, 'r', encoding='gbk') as f:
                return f.read()
        except:
            return ""

def extract_sentences_simple(content):
    """简单的句子提取"""
    if not content:
        return []
    
    # 多种分割方式
    sentences = re.split(r'[。！？；]', content)
    sentences = [s.strip() for s in sentences if len(s.strip()) > 3]
    
    if not sentences:
        sentences = [line.strip() for line in content.split('\n') if len(line.strip()) > 5]
    
    if not sentences and content.strip():
        sentences = [content.strip()]
    
    return sentences

def extract_paragraphs_simple(content):
    """简单的段落提取"""
    if not content:
        return []
    
    paragraphs = [p.strip() for p in content.split('\n') if len(p.strip()) > 10]
    
    if not paragraphs and len(content.strip()) > 10:
        paragraphs = [content.strip()]
    
    return paragraphs

def analyze_language_quality_simple(content, sentences):
    """简单的语言质量分析"""
    issues = []
    score = 100
    
    # 检查装腔作势词汇
    pretentious_words = ['众所周知', '毋庸置疑', '显而易见', '不言而喻', '理所当然']
    for i, sentence in enumerate(sentences):
        for word in pretentious_words:
            if word in sentence:
                issues.append({
                    'type': '装腔作势',
                    'sentence_num': i + 1,
                    'sentence': sentence,
                    'word': word
                })
                score -= 15
    
    # 检查句子长度
    for i, sentence in enumerate(sentences):
        if len(sentence) > 80:
            issues.append({
                'type': '句子过长',
                'sentence_num': i + 1,
                'sentence': sentence,
                'length': len(sentence)
            })
            score -= 10
    
    return {'issues': issues, 'score': max(0, score)}

def analyze_practicality_simple(content, sentences):
    """简单的实用性分析"""
    gaps = []
    score = 100
    
    # 检查实施相关内容
    implementation_words = ['实施', '执行', '落地', '推进', '开展', '部署', '启动', '建设']
    impl_count = sum(1 for word in implementation_words if word in content)
    
    if impl_count == 0:
        gaps.append({
            'type': '缺少实施计划',
            'gap': '没有具体的执行方案',
            'suggestion': '增加详细的实施步骤和方法'
        })
        score -= 30
    
    # 检查时间安排
    time_indicators = ['时间', '期限', '阶段', '步骤', '年', '月', '日', '周期']
    time_count = sum(1 for indicator in time_indicators if indicator in content)
    
    if time_count == 0:
        gaps.append({
            'type': '缺少时间规划',
            'gap': '没有明确的时间表',
            'suggestion': '增加具体的时间安排和里程碑'
        })
        score -= 25
    
    # 检查资源配置
    resource_words = ['人员', '资金', '预算', '成本', '设备', '技术', '资源', '投入']
    resource_count = sum(1 for word in resource_words if word in content)
    
    if resource_count == 0:
        gaps.append({
            'type': '缺少资源配置',
            'gap': '没有明确的资源需求',
            'suggestion': '增加人员、资金等资源配置说明'
        })
        score -= 20
    
    return {'gaps': gaps, 'score': max(0, score)}

def simple_angry_brother_analysis(content, sentences, paragraphs):
    """暴躁老哥分析"""
    language_analysis = analyze_language_quality_simple(content, sentences)
    
    result = "🔥 **暴躁老哥** (语言质量专家):\n\n"
    result += f"**语言质量评分**: {language_analysis['score']}/100\n\n"
    result += f"我仔细看了这篇文章，{len(sentences)}句话，{len(paragraphs)}段。\n\n"
    
    if language_analysis['issues']:
        result += f"**发现 {len(language_analysis['issues'])} 个问题**:\n\n"
        for i, issue in enumerate(language_analysis['issues'][:3], 1):
            result += f"{i}. **{issue['type']}** (第{issue['sentence_num']}句)\n"
            if issue['type'] == '装腔作势':
                result += f"   - 原句: 「{issue['sentence'][:50]}...」\n"
                result += f"   - 问题: 使用了'{issue['word']}'这种装腔作势的表达\n"
                result += f"   - 建议: 删掉装逼词，直接说事实\n\n"
            elif issue['type'] == '句子过长':
                result += f"   - 原句长度: {issue['length']}字符\n"
                result += f"   - 问题: 句子太长，读者累\n"
                result += f"   - 建议: 拆分成2-3个短句\n\n"
    else:
        result += "**意外发现**: 虽然我很想挑毛病，但这次语言质量确实不错！\n\n"
    
    result += f"**总结**: 语言质量{language_analysis['score']}分，"
    if language_analysis['issues']:
        result += f"改掉这{len(language_analysis['issues'])}个问题立马提升！"
    else:
        result += "继续保持这个水平！"
    
    return result

def simple_worker_brother_analysis(content, sentences, paragraphs):
    """牛马小弟分析"""
    practical_analysis = analyze_practicality_simple(content, sentences)
    
    result = "🐂 **牛马小弟** (实用性专家):\n\n"
    result += f"**实用性评分**: {practical_analysis['score']}/100\n\n"
    result += f"老板，我逐句检查了{len(sentences)}句话的可执行性。\n\n"
    
    if practical_analysis['gaps']:
        result += f"**发现 {len(practical_analysis['gaps'])} 个执行缺失**:\n\n"
        for i, gap in enumerate(practical_analysis['gaps'], 1):
            result += f"{i}. **{gap['type']}**\n"
            result += f"   - 问题: {gap['gap']}\n"
            result += f"   - 建议: {gap['suggestion']}\n"
            result += f"   - 重要性: 这是项目成功的关键要素\n\n"
    else:
        result += "**执行诊断**: 各项执行要素基本齐全，可操作性良好！\n\n"
    
    result += f"**总结**: 实用性{practical_analysis['score']}分，"
    if practical_analysis['gaps']:
        result += f"补全这{len(practical_analysis['gaps'])}个要素后方案就能真正落地！"
    else:
        result += "方案具备可操作性，可以开始实施！"
    
    return result

def simple_god_analysis(content, sentences, paragraphs, language_score, practical_score):
    """上帝整合分析"""
    overall_score = (language_score + practical_score + 75 + 80) / 4  # 假设其他维度75和80分
    
    result = "👑 **上帝** (综合整合专家):\n\n"
    result += f"**综合评分**: {overall_score:.1f}/100\n\n"
    result += f"基于多维度分析，对{len(paragraphs)}段{len(sentences)}句的文档进行综合评估:\n\n"
    result += f"📊 **各维度评分**:\n"
    result += f"- 语言质量: {language_score}/100\n"
    result += f"- 逻辑连贯: 75/100\n"
    result += f"- 内容丰富: 80/100\n"
    result += f"- 实用程度: {practical_score}/100\n\n"
    
    if overall_score >= 85:
        result += "**综合评价**: 文档质量优秀，各方面表现均衡！\n\n"
    elif overall_score >= 70:
        result += "**综合评价**: 文档质量良好，有针对性改进空间。\n\n"
    else:
        result += "**综合评价**: 文档需要全面改进，重点关注评分较低的维度。\n\n"
    
    result += "**改进建议**:\n"
    if language_score < 80:
        result += "- 优化语言表达，删除装腔作势词汇，控制句子长度\n"
    if practical_score < 80:
        result += "- 增强实用性，补充实施计划、时间安排和资源配置\n"
    result += "- 保持现有优势，继续完善细节表达\n\n"
    
    result += f"**最终决策**: 综合评分{overall_score:.1f}分，建议按优先级进行针对性改进。"
    
    return result

def analyze_document(file):
    """文档分析主函数"""
    if file is None:
        return "请上传文档文件"
    
    try:
        # 读取文件
        content = read_file_simple(file.name)
        
        if not content or len(content.strip()) == 0:
            return "❌ 文档内容为空，请检查文件"
        
        # 基础分析
        sentences = extract_sentences_simple(content)
        paragraphs = extract_paragraphs_simple(content)
        
        if len(sentences) == 0:
            return "❌ 无法解析文档句子，请检查文档格式"
        
        # 分析结果
        results = []
        results.append("# 🎭 五角色协作分析结果\n")
        results.append(f"**文档信息**: {len(content)}字符，{len(paragraphs)}段，{len(sentences)}句\n")
        results.append("---\n")
        
        # 暴躁老哥分析
        angry_result = simple_angry_brother_analysis(content, sentences, paragraphs)
        results.append(angry_result)
        results.append("\n---\n")
        
        # 牛马小弟分析
        worker_result = simple_worker_brother_analysis(content, sentences, paragraphs)
        results.append(worker_result)
        results.append("\n---\n")
        
        # 获取评分用于上帝分析
        language_analysis = analyze_language_quality_simple(content, sentences)
        practical_analysis = analyze_practicality_simple(content, sentences)
        
        # 上帝整合分析
        god_result = simple_god_analysis(content, sentences, paragraphs, 
                                       language_analysis['score'], 
                                       practical_analysis['score'])
        results.append(god_result)
        
        return "\n".join(results)
        
    except Exception as e:
        return f"❌ 分析出错: {str(e)}"

def create_app():
    """创建应用"""
    with gr.Blocks(title="五角色方案点评系统", theme=gr.themes.Soft()) as app:
        gr.Markdown("# 🎭 五角色方案点评系统")
        gr.Markdown("**专业的文档分析工具** - 暴躁老哥 + 牛马小弟 + 上帝整合")
        
        with gr.Row():
            with gr.Column(scale=1):
                gr.Markdown("### 📁 文档上传")
                file_input = gr.File(
                    label="选择文档文件",
                    file_types=[".txt", ".docx", ".pdf"],
                    type="filepath"
                )
                analyze_btn = gr.Button("🚀 开始分析", variant="primary", size="lg")
                
                gr.Markdown("### 📖 功能说明")
                gr.Markdown("""
                **🔥 暴躁老哥**: 语言质量专家，挑刺表达问题
                
                **🐂 牛马小弟**: 实用性专家，检查可执行性
                
                **👑 上帝**: 综合整合专家，给出最终评价
                """)
            
            with gr.Column(scale=2):
                output = gr.Markdown(
                    label="分析结果",
                    value="### 👋 欢迎使用五角色方案点评系统\n\n请上传文档并点击分析按钮开始使用。\n\n支持格式：.txt, .docx, .pdf"
                )
        
        analyze_btn.click(
            fn=analyze_document,
            inputs=[file_input],
            outputs=[output]
        )
    
    return app

if __name__ == "__main__":
    print("🚀 启动独立版五角色方案点评系统...")
    print("📍 访问地址: http://localhost:7864")
    print("🎭 功能: 暴躁老哥 + 牛马小弟 + 上帝整合")
    print("-" * 50)
    
    app = create_app()
    app.launch(
        server_name="0.0.0.0",
        server_port=7864,
        share=False,
        show_error=True,
        inbrowser=True
    )
