#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
结构化信息提取模块
从文档中提取结构化数据，支持自定义提取字段配置
"""

import os
import yaml
import json
import tempfile
import shutil
from datetime import datetime
from typing import List, Dict, Any, Optional, Union
from dataclasses import dataclass, field

from document import DocumentObject, DocumentParser, parse_documents_batch
from llm_helper import LLMAPIClient
from config import ConfigManager


@dataclass
class ExtractionField:
    """
    提取字段定义
    """
    name: str                           # 字段名称
    description: str                    # 字段描述
    field_type: str                     # 字段类型 (string, number, date, percentage, text, list)
    required: bool = False              # 是否必需
    unit: Optional[str] = None          # 单位
    format: Optional[str] = None        # 格式
    options: List[str] = field(default_factory=list)  # 可选值
    example: Optional[Any] = None       # 示例值


@dataclass
class ExtractionResult:
    """
    提取结果
    """
    field_name: str                     # 字段名称
    value: Any                          # 提取的值
    confidence: float = 0.0             # 置信度 (0-1)
    source_text: Optional[str] = None   # 来源文本片段
    metadata: Dict[str, Any] = field(default_factory=dict)  # 额外元数据


@dataclass
class DocumentExtractionResult:
    """
    单个文档的提取结果
    """
    document: DocumentObject            # 文档对象
    extracted_data: Dict[str, ExtractionResult]  # 提取的数据
    extraction_time: float = 0.0        # 提取耗时
    extracted_at: datetime = field(default_factory=datetime.now)  # 提取时间
    metadata: Dict[str, Any] = field(default_factory=dict)  # 元数据

    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        return {
            'document_info': {
                'file_name': self.document.file_name,
                'file_path': self.document.file_path,
                'file_type': self.document.file_type,
                'file_size_mb': self.document.file_size_mb,
                'content_length': self.document.content_length
            },
            'extracted_data': {
                name: {
                    'value': result.value,
                    'confidence': result.confidence,
                    'source_text': result.source_text,
                    'metadata': result.metadata
                }
                for name, result in self.extracted_data.items()
            },
            'extraction_metadata': {
                'extraction_time': self.extraction_time,
                'extracted_at': self.extracted_at.isoformat(),
                'metadata': self.metadata
            }
        }


class StructuralInfoExtractor:
    """
    结构化信息提取器
    """

    def __init__(self, config_file: str = "structural.yaml", llm_client: Optional[LLMAPIClient] = None):
        """
        初始化提取器

        Args:
            config_file: 配置文件路径
            llm_client: LLM客户端，如果为None则自动创建
        """
        self.config_file = config_file
        self.config = self._load_config()
        self.fields = self._parse_fields()
        
        # 初始化LLM客户端
        if llm_client is None:
            self.llm_client = self._create_default_llm_client()
        else:
            self.llm_client = llm_client
        
        # 临时目录管理
        self.temp_base_dir = tempfile.mkdtemp(prefix="structural_extraction_")
        self._temp_dirs = []

    def __enter__(self):
        return self

    def __exit__(self, exc_type, exc_val, exc_tb):
        self.cleanup()

    def cleanup(self):
        """清理临时文件"""
        for temp_dir in self._temp_dirs:
            if os.path.exists(temp_dir):
                shutil.rmtree(temp_dir, ignore_errors=True)
        
        if os.path.exists(self.temp_base_dir):
            shutil.rmtree(self.temp_base_dir, ignore_errors=True)

    def _create_default_llm_client(self) -> LLMAPIClient:
        """创建默认的LLM客户端"""
        config_manager = ConfigManager()
        llm_source = 'glm'  # 默认使用GLM
        
        api_key = config_manager.get(f'apis.{llm_source}.api_key')
        base_url = config_manager.get(f'apis.{llm_source}.base_url')
        model_name = config_manager.get(f'apis.{llm_source}.model_name')
        
        return LLMAPIClient(api_key, model_name=model_name, base_url=base_url)

    def _load_config(self) -> Dict[str, Any]:
        """加载配置文件"""
        if not os.path.exists(self.config_file):
            raise FileNotFoundError(f"配置文件不存在: {self.config_file}")
        
        with open(self.config_file, 'r', encoding='utf-8') as f:
            return yaml.safe_load(f)

    def _parse_fields(self) -> Dict[str, Dict[str, ExtractionField]]:
        """解析字段配置"""
        fields = {}
        
        for category_name, category_fields in self.config.get('fields', {}).items():
            fields[category_name] = {}
            
            for field_name, field_config in category_fields.items():
                field = ExtractionField(
                    name=field_name,
                    description=field_config.get('description', ''),
                    field_type=field_config.get('type', 'string'),
                    required=field_config.get('required', False),
                    unit=field_config.get('unit'),
                    format=field_config.get('format'),
                    options=field_config.get('options', []),
                    example=field_config.get('example')
                )
                fields[category_name][field_name] = field
        
        return fields

    def get_field_summary(self) -> Dict[str, Any]:
        """获取字段配置摘要"""
        summary = {
            'total_categories': len(self.fields),
            'total_fields': sum(len(category) for category in self.fields.values()),
            'categories': {}
        }
        
        for category_name, category_fields in self.fields.items():
            summary['categories'][category_name] = {
                'field_count': len(category_fields),
                'required_fields': [
                    name for name, field in category_fields.items() 
                    if field.required
                ],
                'field_types': list(set(
                    field.field_type for field in category_fields.values()
                ))
            }
        
        return summary

    def update_config(self, new_config: Dict[str, Any]) -> None:
        """
        更新配置文件

        Args:
            new_config: 新的配置字典
        """
        # 备份原配置文件
        backup_file = f"{self.config_file}.backup.{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        shutil.copy2(self.config_file, backup_file)
        
        # 写入新配置
        with open(self.config_file, 'w', encoding='utf-8') as f:
            yaml.dump(new_config, f, default_flow_style=False, allow_unicode=True, indent=2)
        
        # 重新加载配置
        self.config = new_config
        self.fields = self._parse_fields()
        
        print(f"✅ 配置已更新，原配置备份至: {backup_file}")

    def add_field(self, category: str, field_name: str, field_config: Dict[str, Any]) -> None:
        """
        添加新字段

        Args:
            category: 字段分类
            field_name: 字段名称
            field_config: 字段配置
        """
        if 'fields' not in self.config:
            self.config['fields'] = {}
        
        if category not in self.config['fields']:
            self.config['fields'][category] = {}
        
        self.config['fields'][category][field_name] = field_config
        
        # 保存配置
        with open(self.config_file, 'w', encoding='utf-8') as f:
            yaml.dump(self.config, f, default_flow_style=False, allow_unicode=True, indent=2)
        
        # 重新解析字段
        self.fields = self._parse_fields()
        
        print(f"✅ 已添加字段: {category}.{field_name}")

    def remove_field(self, category: str, field_name: str) -> None:
        """
        删除字段

        Args:
            category: 字段分类
            field_name: 字段名称
        """
        if (category in self.config.get('fields', {}) and 
            field_name in self.config['fields'][category]):
            
            del self.config['fields'][category][field_name]
            
            # 如果分类为空，删除分类
            if not self.config['fields'][category]:
                del self.config['fields'][category]
            
            # 保存配置
            with open(self.config_file, 'w', encoding='utf-8') as f:
                yaml.dump(self.config, f, default_flow_style=False, allow_unicode=True, indent=2)
            
            # 重新解析字段
            self.fields = self._parse_fields()
            
            print(f"✅ 已删除字段: {category}.{field_name}")
        else:
            print(f"⚠️ 字段不存在: {category}.{field_name}")

    def extract_from_files(self, file_paths: List[str]) -> List[DocumentExtractionResult]:
        """
        从文件列表中提取结构化信息

        Args:
            file_paths: 文件路径列表

        Returns:
            List[DocumentExtractionResult]: 提取结果列表
        """
        print(f"🚀 开始处理 {len(file_paths)} 个文件...")
        
        # 解析文档
        documents = parse_documents_batch(file_paths, self.temp_base_dir)
        print(f"📄 成功解析 {len(documents)} 个文档")
        
        # 提取结构化信息
        results = []
        for i, doc in enumerate(documents, 1):
            print(f"🔍 正在提取第 {i}/{len(documents)} 个文档: {doc.file_name}")
            
            try:
                result = self.extract_from_document(doc)
                results.append(result)
                print(f"✅ 完成提取: {doc.file_name}")
            except Exception as e:
                print(f"❌ 提取失败 {doc.file_name}: {e}")
                continue
        
        print(f"🎉 提取完成，成功处理 {len(results)}/{len(documents)} 个文档")
        return results

    def extract_from_document(self, document: DocumentObject) -> DocumentExtractionResult:
        """
        从单个文档中提取结构化信息

        Args:
            document: 文档对象

        Returns:
            DocumentExtractionResult: 提取结果
        """
        start_time = datetime.now()
        extracted_data = {}
        
        # 遍历所有字段进行提取
        for category_name, category_fields in self.fields.items():
            print(f"  📋 处理分类: {category_name}")
            
            for field_name, field in category_fields.items():
                try:
                    # 调用LLM进行提取（这里留空，等待手工实现）
                    result = self._extract_field_value(document, field)
                    extracted_data[f"{category_name}.{field_name}"] = result
                    
                except Exception as e:
                    print(f"    ⚠️ 字段提取失败 {field_name}: {e}")
                    # 创建空结果
                    extracted_data[f"{category_name}.{field_name}"] = ExtractionResult(
                        field_name=field_name,
                        value=None,
                        confidence=0.0,
                        metadata={'error': str(e)}
                    )
        
        end_time = datetime.now()
        extraction_time = (end_time - start_time).total_seconds()
        
        return DocumentExtractionResult(
            document=document,
            extracted_data=extracted_data,
            extraction_time=extraction_time,
            extracted_at=start_time,
            metadata={
                'config_file': self.config_file,
                'total_fields': len(extracted_data),
                'successful_extractions': len([
                    r for r in extracted_data.values() 
                    if r.value is not None
                ])
            }
        )

    def _extract_field_value(self, document: DocumentObject, field: ExtractionField) -> ExtractionResult:
        """
        提取单个字段的值（使用LLM）
        
        Args:
            document: 文档对象
            field: 字段定义
            
        Returns:
            ExtractionResult: 提取结果
            
        Note:
            这个方法留空，等待手工实现LLM调用逻辑
        """
        # TODO: 实现LLM调用逻辑
        # 1. 构建提取prompt
        # 2. 调用LLM API
        # 3. 解析LLM响应
        # 4. 返回提取结果
        
        # 临时返回空结果
        return ExtractionResult(
            field_name=field.name,
            value=None,
            confidence=0.0,
            source_text=None,
            metadata={
                'status': 'not_implemented',
                'field_type': field.field_type,
                'description': field.description
            }
        )

    def save_results(self, results: List[DocumentExtractionResult], output_file: str, format: str = 'yaml') -> None:
        """
        保存提取结果到文件

        Args:
            results: 提取结果列表
            output_file: 输出文件路径
            format: 输出格式 (yaml, json, csv)
        """
        # 准备输出数据
        output_data = {
            'extraction_summary': {
                'total_documents': len(results),
                'extraction_time': datetime.now().isoformat(),
                'config_file': self.config_file,
                'field_summary': self.get_field_summary()
            },
            'results': [result.to_dict() for result in results]
        }
        
        # 根据格式保存
        if format.lower() == 'json':
            with open(output_file, 'w', encoding='utf-8') as f:
                json.dump(output_data, f, ensure_ascii=False, indent=2)
        
        elif format.lower() == 'yaml':
            with open(output_file, 'w', encoding='utf-8') as f:
                yaml.dump(output_data, f, default_flow_style=False, allow_unicode=True, indent=2)
        
        elif format.lower() == 'csv':
            # CSV格式需要展平数据
            import pandas as pd
            
            # 展平提取结果
            flat_data = []
            for result in results:
                row = {
                    'document_name': result.document.file_name,
                    'document_path': result.document.file_path,
                    'document_type': result.document.file_type,
                    'extraction_time': result.extraction_time
                }
                
                # 添加提取的字段值
                for field_name, extraction_result in result.extracted_data.items():
                    row[f"{field_name}_value"] = extraction_result.value
                    row[f"{field_name}_confidence"] = extraction_result.confidence
                
                flat_data.append(row)
            
            df = pd.DataFrame(flat_data)
            df.to_csv(output_file, index=False, encoding='utf-8')
        
        else:
            raise ValueError(f"不支持的输出格式: {format}")
        
        print(f"✅ 结果已保存到: {output_file}")


def create_extractor_from_config(config_file: str = "structural.yaml") -> StructuralInfoExtractor:
    """
    便利函数：从配置文件创建提取器

    Args:
        config_file: 配置文件路径

    Returns:
        StructuralInfoExtractor: 提取器实例
    """
    return StructuralInfoExtractor(config_file)


if __name__ == "__main__":
    # 测试代码
    print("🚀 结构化信息提取模块测试")
    print("=" * 50)
    test_files = ['./asset/页面提取自－2021年审计报告.pdf']
    print(f"🧪 测试提取前 {len(test_files)} 个文件...")
    # 创建提取器
    with StructuralInfoExtractor() as extractor:
        results = extractor.extract_from_files(test_files)
        # 保存结果
        output_file = "00_test_extraction_results.yaml"
        extractor.save_results(results, output_file)
        print(f"\n✅ 测试完成，结果保存到: {output_file}")

    # try:
    #     # 创建提取器
    #     with StructuralInfoExtractor() as extractor:
    #         # 显示字段摘要
    #         summary = extractor.get_field_summary()
    #         print(f"📊 字段配置摘要:")
    #         print(f"   总分类数: {summary['total_categories']}")
    #         print(f"   总字段数: {summary['total_fields']}")
    #
    #         for category, info in summary['categories'].items():
    #             print(f"   📋 {category}: {info['field_count']} 个字段")
    #             if info['required_fields']:
    #                 print(f"      必需字段: {', '.join(info['required_fields'])}")
    #             print(f"      字段类型: {', '.join(info['field_types'])}")
    #
    #         # 检查测试文件
    #         asset_dir = "./asset"
    #         if os.path.exists(asset_dir):
    #             # 获取支持的文件
    #             from document import filter_supported_files
    #
    #             all_files = []
    #             for root, dirs, files in os.walk(asset_dir):
    #                 for file in files:
    #                     all_files.append(os.path.join(root, file))
    #
    #             supported_files = filter_supported_files(all_files)
    #
    #             if supported_files:
    #                 print(f"\n📄 找到 {len(supported_files)} 个支持的文档文件")
    #
    #                 # 测试提取（只处理前2个文件）
    #                 test_files = supported_files[:2]
    #                 print(f"🧪 测试提取前 {len(test_files)} 个文件...")
    #
    #                 results = extractor.extract_from_files(test_files)
    #
    #                 # 保存结果
    #                 output_file = "00_test_extraction_results.yaml"
    #                 extractor.save_results(results, output_file)
    #
    #                 print(f"\n✅ 测试完成，结果保存到: {output_file}")
    #             else:
    #                 print("❌ 没有找到支持的文档文件")
    #         else:
    #             print(f"❌ {asset_dir} 目录不存在")
    #
    # except Exception as e:
    #     print(f"❌ 测试失败: {e}")
    #     import traceback
    #     traceback.print_exc()
