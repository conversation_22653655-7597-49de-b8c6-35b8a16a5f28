# FinPOC v3.0.0 - 精简依赖包
# 只保留核心功能所需的依赖

# Web界面
gradio==5.32.1
gradio_client==1.10.2

# HTTP请求
requests==2.32.3

# AI模型API
openai==1.63.2          # GPT系列和通用OpenAI兼容接口
zhipuai==2.0.1          # 智谱AI (GLM系列)

# 文档处理
pandas==1.5.3          # Excel/CSV处理
PyPDF2==2.12.1         # PDF文档读取
python-docx==1.1.2     # Word文档处理

# 基础依赖
pathlib                 # 文件路径处理 (Python内置)
os                      # 操作系统接口 (Python内置)
sys                     # 系统相关 (Python内置)
json                    # JSON处理 (Python内置)
datetime                # 日期时间 (Python内置)
threading               # 多线程 (Python内置)
queue                   # 队列 (Python内置)
tempfile                # 临时文件 (Python内置)
shutil                  # 文件操作 (Python内置)
re                      # 正则表达式 (Python内置)
io                      # 输入输出 (Python内置)
contextlib              # 上下文管理 (Python内置)

# 可选依赖 (根据需要安装)
# anthropic             # Claude API (如果使用Claude)
# dashscope             # 阿里云通义千问 (如果使用Qwen)

# 说明:
# 1. 删除了所有llama-index相关依赖 (不需要)
# 2. 删除了azure相关依赖 (不使用Azure)
# 3. 删除了其他AI框架依赖 (langchain等)
# 4. 保留了核心的Web界面、文档处理、AI API依赖
# 5. DeepSeek使用OpenAI兼容接口，不需要额外依赖
# 6. Qwen可以通过dashscope或OpenAI兼容接口调用
