#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试写作点评Tab功能
"""

import os
import tempfile
from gradio_app import process_writing_critique


def create_test_files():
    """创建测试文件"""
    # 创建临时模板文件
    template_content = """
天津市债务融资工具发展概况

天津市作为重要的经济中心，其债务融资工具市场发展迅速。
近年来，天津市政府积极推动金融创新，通过发行各类债券来支持基础设施建设和产业发展。

主要特点：
1. 发行规模稳步增长
2. 品种结构不断优化
3. 市场化程度持续提升
4. 风险管控能力增强

数据显示，2023年天津市债务融资工具发行规模达到1500亿元，同比增长15%。
其中，企业债券占比60%，公司债券占比25%，中期票据占比15%。

未来展望：
天津市将继续完善债务融资工具市场体系，提高直接融资比重，
为实体经济发展提供更加有力的金融支持。
    """.strip()
    
    # 创建临时资源文件
    resource_content = """
北京市债务融资工具发展报告

北京市作为首都，拥有更加完善的金融市场体系。
北京市债务融资工具市场规模更大，2023年发行规模超过3000亿元。

发展特色：
- 绿色债券发行量全国领先
- 创新型债券品种丰富
- 国际化程度较高
- 监管体系完善

市场数据：
2023年北京市各类债务融资工具发行情况：
- 企业债券：1800亿元
- 公司债券：900亿元
- 中期票据：300亿元

北京市在绿色债券和创新型债券方面走在全国前列，
为其他地区提供了宝贵的经验和借鉴。
    """.strip()
    
    # 创建临时文件
    template_file = tempfile.NamedTemporaryFile(mode='w', suffix='.txt', delete=False, encoding='utf-8')
    template_file.write(template_content)
    template_file.close()
    
    resource_file = tempfile.NamedTemporaryFile(mode='w', suffix='.txt', delete=False, encoding='utf-8')
    resource_file.write(resource_content)
    resource_file.close()
    
    return template_file.name, resource_file.name


def mock_file_object(file_path):
    """模拟Gradio文件对象"""
    class MockFile:
        def __init__(self, path):
            self.name = path
    
    return MockFile(file_path)


def test_writing_critique_function():
    """测试写作点评功能"""
    print("🧪 开始测试写作点评功能...")
    
    try:
        # 创建测试文件
        template_path, resource_path = create_test_files()
        print(f"📁 创建测试文件:")
        print(f"   模板文件: {template_path}")
        print(f"   资源文件: {resource_path}")
        
        # 模拟Gradio文件对象
        template_file = mock_file_object(template_path)
        resource_files = [mock_file_object(resource_path)]
        
        # 测试参数
        original_title = "天津市"
        new_title = "北京市"
        
        print(f"\n🔄 测试参数:")
        print(f"   原标题: {original_title}")
        print(f"   新标题: {new_title}")
        
        print(f"\n🎭 开始多人格写作点评...")
        print("=" * 60)
        
        # 调用写作点评函数
        for log_output, final_result in process_writing_critique(
            original_title=original_title,
            new_title=new_title,
            template_file=template_file,
            resource_files=resource_files
        ):
            # 显示日志输出
            if log_output:
                print("📋 处理日志:")
                print(log_output)
                print("-" * 40)
            
            # 显示最终结果
            if final_result:
                print("🎯 最终点评结果:")
                print(final_result)
                print("=" * 60)
                break
        
        print("\n✅ 写作点评功能测试完成!")
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False
    
    finally:
        # 清理测试文件
        try:
            if 'template_path' in locals():
                os.unlink(template_path)
            if 'resource_path' in locals():
                os.unlink(resource_path)
            print("🧹 测试文件已清理")
        except Exception as e:
            print(f"⚠️ 清理文件时出错: {e}")


def test_ui_components():
    """测试UI组件"""
    print("\n🧪 测试UI组件...")
    
    try:
        from gradio_app import create_writing_critique_interface
        
        print("✅ 写作点评界面组件导入成功")
        
        # 这里可以添加更多UI组件测试
        print("✅ UI组件测试通过")
        return True
        
    except Exception as e:
        print(f"❌ UI组件测试失败: {e}")
        return False


def test_integration():
    """集成测试"""
    print("\n🧪 集成测试...")
    
    try:
        # 测试导入
        from backend_apis_streaming import multi_persona_writing_api_streaming
        print("✅ 多人格API导入成功")
        
        # 测试多人格决策系统
        from multi_persona_decision import MultiPersonaDecisionSystem
        print("✅ 多人格决策系统导入成功")
        
        # 创建系统实例
        mpd_system = MultiPersonaDecisionSystem()
        print("✅ 多人格决策系统实例化成功")
        
        # 检查人格定义
        expected_personas = ["暴躁老哥", "自省姐", "粉丝妹", "牛马小弟"]
        actual_personas = list(mpd_system.personas.keys())
        
        if set(expected_personas) == set(actual_personas):
            print("✅ 人格定义检查通过")
            for persona in expected_personas:
                print(f"   🎭 {persona}: {mpd_system.personas[persona]['description'][:50]}...")
        else:
            print(f"❌ 人格定义不匹配: 期望 {expected_personas}, 实际 {actual_personas}")
            return False
        
        print("✅ 集成测试通过")
        return True
        
    except Exception as e:
        print(f"❌ 集成测试失败: {e}")
        return False


def main():
    """主测试函数"""
    print("🚀 写作点评Tab功能测试")
    print("=" * 60)
    
    # 运行各项测试
    test_results = []
    
    # 1. UI组件测试
    test_results.append(("UI组件测试", test_ui_components()))
    
    # 2. 集成测试
    test_results.append(("集成测试", test_integration()))
    
    # 3. 功能测试（需要LLM API，可能会失败）
    print("\n⚠️ 注意: 功能测试需要有效的LLM API配置")
    user_input = input("是否运行功能测试？(y/N): ").strip().lower()
    if user_input == 'y':
        test_results.append(("功能测试", test_writing_critique_function()))
    else:
        print("⏭️ 跳过功能测试")
        test_results.append(("功能测试", "跳过"))
    
    # 显示测试结果
    print("\n" + "=" * 60)
    print("📊 测试结果汇总:")
    for test_name, result in test_results:
        if result == "跳过":
            status = "⏭️ 跳过"
        elif result:
            status = "✅ 通过"
        else:
            status = "❌ 失败"
        print(f"   {test_name}: {status}")
    
    # 总结
    passed_tests = sum(1 for _, result in test_results if result is True)
    total_tests = len([r for _, r in test_results if r != "跳过"])
    
    if total_tests > 0:
        print(f"\n📈 测试通过率: {passed_tests}/{total_tests} ({passed_tests/total_tests*100:.1f}%)")
    
    if passed_tests == total_tests:
        print("\n🎉 所有测试通过！写作点评Tab功能正常。")
    else:
        print("\n⚠️ 部分测试失败，请检查相关配置。")
    
    print("\n💡 使用说明:")
    print("1. 启动Gradio应用: python gradio_app.py")
    print("2. 访问 http://localhost:7860")
    print("3. 点击 '🎭 写作点评' tab")
    print("4. 上传文件并体验多人格协作点评功能")


if __name__ == "__main__":
    main()
