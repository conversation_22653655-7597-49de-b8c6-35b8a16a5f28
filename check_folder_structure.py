#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
检查文件夹结构工具
帮助用户了解报案接待功能所需的文件夹结构
"""

import os
import sys
from source_struct_loader import SourceStructLoader

def check_expected_structure():
    """检查并显示预期的文件夹结构"""
    print("🔍 报案接待功能预期的文件夹结构")
    print("=" * 50)
    
    try:
        # 加载配置
        loader = SourceStructLoader()
        source_struct_dict = loader.load_config()
        
        print("📋 您上传的zip文件应包含以下文件夹结构:")
        print()
        
        for folder_key, folder_config in source_struct_dict.sub_folders.items():
            print(f"📁 {folder_config.folder_name}/")
            print(f"   用途: 提取 {len(folder_config.field_list)} 个字段")
            print(f"   字段列表:")
            for field in folder_config.field_list:
                print(f"     • {field}")
            print()
        
        print("💡 使用说明:")
        print("1. 创建一个包含上述文件夹的zip文件")
        print("2. 将相关文档放入对应的文件夹中")
        print("3. 系统会自动进行OCR识别，生成txt文件")
        print("4. 然后从txt文件中提取结构化字段信息")
        print()
        print("📝 注意事项:")
        print("• 支持的图片格式: png, jpg, jpeg, gif, bmp, tiff, webp")
        print("• 银行流水文件夹需要图片和对应的txt文件配对")
        print("• 其他文件夹的txt文件会合并处理")
        
    except Exception as e:
        print(f"❌ 加载配置失败: {e}")
        return False
    
    return True

def check_actual_structure(zip_or_folder_path):
    """检查实际的文件夹结构"""
    print(f"\n🔍 检查实际结构: {zip_or_folder_path}")
    print("=" * 50)
    
    if not os.path.exists(zip_or_folder_path):
        print(f"❌ 路径不存在: {zip_or_folder_path}")
        return False
    
    if os.path.isfile(zip_or_folder_path) and zip_or_folder_path.endswith('.zip'):
        # 处理zip文件
        import zipfile
        import tempfile
        
        try:
            temp_dir = tempfile.mkdtemp()
            with zipfile.ZipFile(zip_or_folder_path, 'r') as zip_ref:
                zip_ref.extractall(temp_dir)
            
            print(f"📦 已解压到临时目录: {temp_dir}")
            _list_directory_structure(temp_dir)
            
            # 清理临时目录
            import shutil
            shutil.rmtree(temp_dir)
            
        except Exception as e:
            print(f"❌ 处理zip文件失败: {e}")
            return False
    
    elif os.path.isdir(zip_or_folder_path):
        # 处理文件夹
        _list_directory_structure(zip_or_folder_path)
    
    else:
        print(f"❌ 不支持的文件类型: {zip_or_folder_path}")
        return False
    
    return True

def _list_directory_structure(directory, level=0, max_level=3):
    """递归列出目录结构"""
    if level > max_level:
        return
    
    try:
        items = sorted(os.listdir(directory))
        for item in items:
            item_path = os.path.join(directory, item)
            indent = "  " * level
            
            if os.path.isdir(item_path):
                print(f"{indent}📁 {item}/")
                _list_directory_structure(item_path, level + 1, max_level)
            else:
                # 显示文件大小
                try:
                    size = os.path.getsize(item_path)
                    if size > 1024 * 1024:
                        size_str = f"{size / (1024 * 1024):.1f}MB"
                    elif size > 1024:
                        size_str = f"{size / 1024:.1f}KB"
                    else:
                        size_str = f"{size}B"
                    print(f"{indent}📄 {item} ({size_str})")
                except:
                    print(f"{indent}📄 {item}")
    
    except Exception as e:
        print(f"❌ 列出目录内容失败: {e}")

def main():
    """主函数"""
    print("🚀 文件夹结构检查工具")
    print("=" * 50)
    
    # 显示预期结构
    if not check_expected_structure():
        return
    
    # 如果提供了参数，检查实际结构
    if len(sys.argv) > 1:
        actual_path = sys.argv[1]
        check_actual_structure(actual_path)
    else:
        print("\n💡 使用方法:")
        print(f"python {os.path.basename(__file__)} <zip文件路径或文件夹路径>")
        print("例如:")
        print(f"python {os.path.basename(__file__)} /path/to/your/case_files.zip")
        print(f"python {os.path.basename(__file__)} /path/to/your/case_folder/")

if __name__ == "__main__":
    main()
