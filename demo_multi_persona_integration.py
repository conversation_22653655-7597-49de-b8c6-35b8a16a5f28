#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
多人格决策系统集成演示
展示如何在现有项目中集成多人格决策功能
"""

import os
import json
from typing import Generator, Tuple, Optional, Any
from backend_apis_streaming import multi_persona_writing_api_streaming


class MultiPersonaIntegrationDemo:
    """多人格决策系统集成演示类"""
    
    def __init__(self, output_base_dir: str = "demo_output"):
        """
        初始化演示系统
        
        Args:
            output_base_dir: 输出基础目录
        """
        self.output_base_dir = output_base_dir
        os.makedirs(output_base_dir, exist_ok=True)
    
    def demo_basic_integration(self) -> None:
        """演示基本集成方式"""
        print("🚀 多人格决策系统基本集成演示")
        print("=" * 50)
        
        # 模拟文件路径（实际使用时需要真实文件）
        template_file = "demo_template.txt"
        resource_files = ["demo_resource1.txt", "demo_resource2.txt"]
        
        # 创建演示文件
        self._create_demo_files(template_file, resource_files)
        
        print("📁 演示文件创建完成")
        print(f"   模板文件: {template_file}")
        print(f"   资源文件: {', '.join(resource_files)}")
        
        # 执行多人格决策
        original_title = "传统金融"
        new_title = "数字金融"
        
        print(f"\n🔄 主题转换: {original_title} → {new_title}")
        print("\n🧠 开始多人格协作决策...")
        print("   🔥 暴躁老哥：会犀利批评现有问题，不留情面")
        print("   🤔 自省姐：会深度分析逻辑结构，查漏补缺")
        print("   ✨ 粉丝妹：会发现内容亮点，积极鼓励")
        print("   🐂 牛马小弟：会补全实施细节，尽职尽责")

        try:
            results = self._run_multi_persona_decision(
                template_file, resource_files, original_title, new_title
            )
            
            if results:
                self._display_results(results)
                self._save_results(results, "demo_basic_results.json")
            else:
                print("❌ 决策过程失败")
                
        except Exception as e:
            print(f"❌ 演示过程中发生错误: {e}")
        
        finally:
            # 清理演示文件
            self._cleanup_demo_files([template_file] + resource_files)
    
    def demo_advanced_integration(self) -> None:
        """演示高级集成方式"""
        print("\n🚀 多人格决策系统高级集成演示")
        print("=" * 50)
        
        # 模拟多个场景
        scenarios = [
            {
                "name": "金融报告转换",
                "original": "银行业务",
                "new": "金融科技",
                "template_content": "银行业务在传统金融体系中占据重要地位...",
                "resource_content": "金融科技正在重塑整个金融行业..."
            },
            {
                "name": "市场分析更新",
                "original": "股票市场",
                "new": "数字货币市场",
                "template_content": "股票市场的波动性分析显示...",
                "resource_content": "数字货币市场具有更高的波动性..."
            }
        ]
        
        for i, scenario in enumerate(scenarios, 1):
            print(f"\n📋 场景 {i}: {scenario['name']}")
            print(f"   转换: {scenario['original']} → {scenario['new']}")
            
            # 创建场景文件
            template_file = f"scenario_{i}_template.txt"
            resource_file = f"scenario_{i}_resource.txt"
            
            self._create_scenario_files(
                template_file, resource_file,
                scenario['template_content'], scenario['resource_content']
            )
            
            try:
                results = self._run_multi_persona_decision(
                    template_file, [resource_file],
                    scenario['original'], scenario['new']
                )
                
                if results:
                    print(f"   ✅ 场景 {i} 处理完成")
                    self._save_results(results, f"scenario_{i}_results.json")
                else:
                    print(f"   ❌ 场景 {i} 处理失败")
                    
            except Exception as e:
                print(f"   ❌ 场景 {i} 错误: {e}")
            
            finally:
                # 清理场景文件
                self._cleanup_demo_files([template_file, resource_file])
    
    def _create_demo_files(self, template_file: str, resource_files: list) -> None:
        """创建演示文件"""
        # 创建模板文件
        template_content = """
传统金融体系概述

传统金融体系是现代经济的重要组成部分，主要包括银行、保险、证券等金融机构。
这些机构通过提供存款、贷款、投资等服务，为经济发展提供资金支持。

传统金融的特点：
1. 依赖物理网点和人工服务
2. 严格的监管体系
3. 相对稳定的业务模式
4. 较高的运营成本

随着科技的发展，传统金融面临着数字化转型的挑战和机遇。
        """.strip()
        
        with open(template_file, 'w', encoding='utf-8') as f:
            f.write(template_content)
        
        # 创建资源文件
        resource_contents = [
            """
数字金融发展报告

数字金融是利用数字技术提供金融服务的新模式，包括移动支付、网络银行、数字货币等。
数字金融的优势在于便捷性、效率性和普惠性。

主要特征：
- 24/7全天候服务
- 低成本高效率
- 个性化服务
- 数据驱动决策
            """.strip(),
            """
金融科技创新趋势

人工智能、区块链、大数据等技术正在重塑金融行业。
这些技术不仅提高了服务效率，还创造了全新的商业模式。

技术应用：
- AI风控系统
- 区块链支付
- 大数据征信
- 云计算基础设施
            """.strip()
        ]
        
        for i, content in enumerate(resource_contents):
            if i < len(resource_files):
                with open(resource_files[i], 'w', encoding='utf-8') as f:
                    f.write(content)
    
    def _create_scenario_files(self, template_file: str, resource_file: str,
                             template_content: str, resource_content: str) -> None:
        """创建场景文件"""
        with open(template_file, 'w', encoding='utf-8') as f:
            f.write(template_content)
        
        with open(resource_file, 'w', encoding='utf-8') as f:
            f.write(resource_content)
    
    def _run_multi_persona_decision(self, template_file: str, resource_files: list,
                                  original_title: str, new_title: str) -> Optional[dict]:
        """运行多人格决策"""
        results = None
        
        try:
            for log_msg, result in multi_persona_writing_api_streaming(
                input_template=template_file,
                input_resources_list=resource_files,
                output_file=self.output_base_dir,
                original_title=original_title,
                new_title=new_title
            ):
                # 显示处理日志（简化版）
                if any(icon in log_msg for icon in ["🔥", "🤔", "✨", "🐂", "🎯", "✅"]):
                    print(f"   {log_msg.strip()}")
                
                if result is not None:
                    results = result
                    break
                    
        except Exception as e:
            print(f"   ⚠️ 处理过程中出现异常: {e}")
        
        return results
    
    def _display_results(self, results: dict) -> None:
        """显示结果摘要"""
        print("\n📊 决策结果摘要:")
        
        if 'summary' in results:
            summary = results['summary']
            print(f"   整体置信度: {summary.get('overall_confidence', 0):.2f}")
            print(f"   处理时间: {summary.get('processing_timestamp', 'N/A')}")
        
        # 显示各阶段决策
        stages = ['structure_analysis', 'writing_decision', 'optimization_decision']
        stage_names = ['结构分析', '写作决策', '优化决策']
        
        for stage, name in zip(stages, stage_names):
            if stage in results:
                stage_data = results[stage]
                confidence = stage_data.get('confidence_level', 0)
                decision = stage_data.get('final_decision', '')[:100]
                print(f"   {name}: {confidence:.2f} - {decision}...")
    
    def _save_results(self, results: dict, filename: str) -> None:
        """保存结果到文件"""
        output_path = os.path.join(self.output_base_dir, filename)
        with open(output_path, 'w', encoding='utf-8') as f:
            json.dump(results, f, ensure_ascii=False, indent=2)
        print(f"   💾 结果已保存到: {output_path}")
    
    def _cleanup_demo_files(self, files: list) -> None:
        """清理演示文件"""
        for file in files:
            if os.path.exists(file):
                try:
                    os.remove(file)
                except Exception as e:
                    print(f"   ⚠️ 无法删除文件 {file}: {e}")


def main():
    """主演示函数"""
    print("🎭 多人格决策系统集成演示")
    print("这个演示展示了如何在实际项目中集成多人格决策功能")
    print()
    
    # 创建演示实例
    demo = MultiPersonaIntegrationDemo()
    
    try:
        # 基本集成演示
        demo.demo_basic_integration()
        
        # 高级集成演示
        demo.demo_advanced_integration()
        
        print("\n🎉 演示完成！")
        print("请查看输出目录中的结果文件以了解详细信息。")
        
    except Exception as e:
        print(f"\n❌ 演示过程中发生错误: {e}")
        print("请检查系统配置和依赖项。")


if __name__ == "__main__":
    main()
