#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
字典转换工具
用于将字典格式的数据转换为可读的字符串格式
"""

import json
from typing import Any, Dict, Union


def convert_dict_to_readable_string(data: Union[Dict, Any], title: str = "数据结果") -> str:
    """
    将字典转换为可读的字符串格式
    
    Args:
        data: 要转换的字典数据
        title: 标题
    
    Returns:
        str: 格式化后的字符串
    """
    if isinstance(data, dict):
        result = [f"=== {title} ==="]
        
        for key, value in data.items():
            # 格式化键名（将下划线替换为空格，首字母大写）
            formatted_key = key.replace('_', ' ').title()
            result.append(f"\n【{formatted_key}】")
            
            if isinstance(value, dict):
                for sub_key, sub_value in value.items():
                    formatted_sub_key = sub_key.replace('_', ' ').title()
                    result.append(f"  {formatted_sub_key}: {sub_value}")
            elif isinstance(value, list):
                for i, item in enumerate(value):
                    if isinstance(item, dict):
                        result.append(f"  项目 {i+1}:")
                        for sub_key, sub_value in item.items():
                            formatted_sub_key = sub_key.replace('_', ' ').title()
                            result.append(f"    {formatted_sub_key}: {sub_value}")
                    else:
                        result.append(f"  - {item}")
            else:
                result.append(f"  {value}")
        
        return "\n".join(result)
    else:
        return str(data)


def convert_dict_to_json_string(data: Union[Dict, Any]) -> str:
    """
    将字典转换为格式化的JSON字符串
    
    Args:
        data: 要转换的字典数据
    
    Returns:
        str: JSON格式的字符串
    """
    return json.dumps(data, ensure_ascii=False, indent=2)


def convert_dict_to_markdown(data: Union[Dict, Any], title: str = "数据结果") -> str:
    """
    将字典转换为Markdown格式的字符串
    
    Args:
        data: 要转换的字典数据
        title: 标题
    
    Returns:
        str: Markdown格式的字符串
    """
    if isinstance(data, dict):
        result = [f"# {title}\n"]
        
        for key, value in data.items():
            # 格式化键名
            formatted_key = key.replace('_', ' ').title()
            result.append(f"## {formatted_key}\n")
            
            if isinstance(value, dict):
                for sub_key, sub_value in value.items():
                    formatted_sub_key = sub_key.replace('_', ' ').title()
                    result.append(f"**{formatted_sub_key}**: {sub_value}\n")
            elif isinstance(value, list):
                for i, item in enumerate(value):
                    if isinstance(item, dict):
                        result.append(f"### 项目 {i+1}\n")
                        for sub_key, sub_value in item.items():
                            formatted_sub_key = sub_key.replace('_', ' ').title()
                            result.append(f"- **{formatted_sub_key}**: {sub_value}")
                    else:
                        result.append(f"- {item}")
                result.append("")
            else:
                result.append(f"{value}\n")
        
        return "\n".join(result)
    else:
        return str(data)


def save_dict_to_file(data: Union[Dict, Any], filename: str, format_type: str = "readable") -> None:
    """
    将字典保存到文件
    
    Args:
        data: 要保存的字典数据
        filename: 文件名
        format_type: 格式类型 ('readable', 'json', 'markdown')
    """
    if format_type == "json":
        content = convert_dict_to_json_string(data)
    elif format_type == "markdown":
        content = convert_dict_to_markdown(data)
    else:  # readable
        content = convert_dict_to_readable_string(data)
    
    with open(filename, 'w', encoding='utf-8') as f:
        f.write(content)
    
    print(f"数据已保存到文件: {filename}")


# 示例用法
if __name__ == "__main__":
    # 示例数据
    sample_data = {
        'introductory_paragraph': {
            'contextual_adjustment': '开篇应介绍雅安市商业银行的背景信息，包括其地理位置、成立时间、主要业务领域等',
            'example_text': '关于雅安市商业银行股份有限公司，作为扎根于四川省雅安市的地方性金融机构...'
        },
        'annual_report_discussion': {
            'data_focus': '将莱商银行的财务数据替换为雅安市商业银行的最新年报数据',
            'example_text': '2024年年报显示，雅安市商业银行股份有限公司...'
        }
    }
    
    print("=== 可读格式 ===")
    print(convert_dict_to_readable_string(sample_data, "写作指导"))
    
    print("\n=== JSON格式 ===")
    print(convert_dict_to_json_string(sample_data))
    
    print("\n=== Markdown格式 ===")
    print(convert_dict_to_markdown(sample_data, "写作指导"))
