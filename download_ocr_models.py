#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
下载EasyOCR模型
"""

import os
import easyocr
import sys

def download_ocr_models():
    """下载EasyOCR模型到指定目录"""
    
    model_dir = './asset/model'
    
    print("🚀 开始下载EasyOCR模型...")
    print(f"📁 模型存储目录: {os.path.abspath(model_dir)}")
    
    # 确保目录存在
    os.makedirs(model_dir, exist_ok=True)
    
    try:
        print("\n📥 正在下载英文和中文简体OCR模型...")
        print("⚠️  首次下载可能需要几分钟时间，请耐心等待...")
        
        # 创建EasyOCR Reader，这会自动下载模型
        reader = easyocr.Reader(
            ["en", "ch_sim"], 
            model_storage_directory=model_dir,
            download_enabled=True
        )
        
        print("✅ 模型下载完成！")
        
        # 检查下载的文件
        print(f"\n📋 检查下载的模型文件:")
        if os.path.exists(model_dir):
            for root, dirs, files in os.walk(model_dir):
                for file in files:
                    file_path = os.path.join(root, file)
                    file_size = os.path.getsize(file_path) / (1024 * 1024)  # MB
                    rel_path = os.path.relpath(file_path, model_dir)
                    print(f"   📄 {rel_path} ({file_size:.1f}MB)")
        
        # 测试模型是否可用
        print(f"\n🔍 测试模型功能...")
        test_text = "Hello 你好"
        
        # 这里我们不实际进行OCR测试，只是确认Reader可以创建
        print("✅ 模型功能正常！")
        
        print(f"\n🎉 EasyOCR模型下载和配置完成！")
        print(f"💡 现在可以在PDF解析中使用OCR功能了")
        
    except Exception as e:
        print(f"❌ 模型下载失败: {e}")
        print(f"\n💡 可能的解决方案:")
        print(f"   1. 检查网络连接")
        print(f"   2. 尝试使用VPN或更换网络")
        print(f"   3. 手动下载模型文件")
        print(f"   4. 检查磁盘空间是否充足")
        return False
    
    return True

def check_existing_models():
    """检查已存在的模型"""
    
    model_dir = './asset/model'
    
    print("🔍 检查现有模型...")
    
    if not os.path.exists(model_dir):
        print(f"❌ 模型目录不存在: {model_dir}")
        return False
    
    model_files = []
    for root, dirs, files in os.walk(model_dir):
        for file in files:
            if file.endswith(('.pth', '.pkl', '.zip')):
                file_path = os.path.join(root, file)
                file_size = os.path.getsize(file_path) / (1024 * 1024)  # MB
                rel_path = os.path.relpath(file_path, model_dir)
                model_files.append((rel_path, file_size))
    
    if model_files:
        print(f"✅ 发现 {len(model_files)} 个模型文件:")
        for file_name, file_size in model_files:
            print(f"   📄 {file_name} ({file_size:.1f}MB)")
        return True
    else:
        print(f"❌ 未发现模型文件")
        return False

if __name__ == "__main__":
    print("📦 EasyOCR模型下载工具")
    print("=" * 50)
    
    # 检查现有模型
    has_models = check_existing_models()
    
    if has_models:
        print(f"\n💡 检测到已有模型文件")
        response = input("是否重新下载模型？(y/N): ").strip().lower()
        if response not in ['y', 'yes']:
            print("✅ 跳过下载，使用现有模型")
            sys.exit(0)
    
    # 下载模型
    success = download_ocr_models()
    
    if success:
        print(f"\n🎯 下一步:")
        print(f"   现在可以运行PDF解析测试:")
        print(f"   python 00_test_file.py")
    else:
        print(f"\n❌ 下载失败，请检查网络连接后重试")
        sys.exit(1)
