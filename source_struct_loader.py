#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
源文件结构配置加载器
读取source_file_struct_fanzha.yaml文件并结构化存储
"""

import yaml
import os
from typing import Dict, List, Any, Optional
from dataclasses import dataclass, field
from datetime import datetime


@dataclass
class FolderConfig:
    """文件夹配置类"""
    folder_name: str
    field_list: List[str] = field(default_factory=list)
    
    def __post_init__(self):
        """后处理，确保field_list不为空"""
        if not self.field_list:
            self.field_list = []


@dataclass
class SourceStructConfig:
    """源文件结构配置类"""
    base_folder: FolderConfig
    sub_folders: Dict[str, FolderConfig] = field(default_factory=dict)
    
    def get_all_fields(self) -> Dict[str, List[str]]:
        """获取所有字段的字典映射"""
        all_fields = {}
        
        # 添加基础文件夹字段
        if self.base_folder.field_list:
            all_fields['base'] = self.base_folder.field_list
        
        # 添加子文件夹字段
        for folder_key, folder_config in self.sub_folders.items():
            if folder_config.field_list:
                all_fields[folder_key] = folder_config.field_list
        
        return all_fields
    
    def get_folder_by_name(self, folder_name: str) -> Optional[FolderConfig]:
        """根据文件夹名称查找配置"""
        # 检查基础文件夹
        if self.base_folder.folder_name == folder_name:
            return self.base_folder
        
        # 检查子文件夹
        for folder_config in self.sub_folders.values():
            if folder_config.folder_name == folder_name:
                return folder_config
        
        return None
    
    def get_total_field_count(self) -> int:
        """获取总字段数量"""
        total = len(self.base_folder.field_list)
        for folder_config in self.sub_folders.values():
            total += len(folder_config.field_list)
        return total


class SourceStructLoader:
    """源文件结构配置加载器"""
    
    def __init__(self, config_file: str = "source_file_struct_fanzha.yaml"):
        """
        初始化加载器
        
        Args:
            config_file: 配置文件路径
        """
        self.config_file = config_file
        self.raw_config = None
        self.source_struct_dict = None
        self.loaded_at = None
    
    def load_config(self) -> SourceStructConfig:
        """
        加载配置文件并返回结构化对象
        
        Returns:
            SourceStructConfig: 结构化配置对象
        """
        if not os.path.exists(self.config_file):
            raise FileNotFoundError(f"配置文件不存在: {self.config_file}")
        
        # 读取YAML文件
        with open(self.config_file, 'r', encoding='utf-8') as f:
            self.raw_config = yaml.safe_load(f)
        
        # 解析配置
        source_config = self.raw_config.get('source', {})
        
        # 解析基础文件夹配置
        base_folder_config = source_config.get('base_folder', {})
        base_folder = FolderConfig(
            folder_name=base_folder_config.get('folder_name', ''),
            field_list=base_folder_config.get('filed_list', [])  # 注意原文件中是'filed_list'
        )
        
        # 解析子文件夹配置
        sub_folders = {}
        sub_folder_configs = source_config.get('sub_folder', {})
        
        for folder_key, folder_data in sub_folder_configs.items():
            sub_folders[folder_key] = FolderConfig(
                folder_name=folder_data.get('folder_name', ''),
                field_list=folder_data.get('field_list', [])
            )
        
        # 创建结构化配置对象
        self.source_struct_dict = SourceStructConfig(
            base_folder=base_folder,
            sub_folders=sub_folders
        )
        
        self.loaded_at = datetime.now()
        
        return self.source_struct_dict
    
    def get_config_summary(self) -> Dict[str, Any]:
        """
        获取配置摘要信息
        
        Returns:
            Dict: 配置摘要
        """
        if not self.source_struct_dict:
            raise RuntimeError("配置尚未加载，请先调用load_config()")
        
        summary = {
            'config_file': self.config_file,
            'loaded_at': self.loaded_at.isoformat() if self.loaded_at else None,
            'base_folder': {
                'name': self.source_struct_dict.base_folder.folder_name,
                'field_count': len(self.source_struct_dict.base_folder.field_list),
                'fields': self.source_struct_dict.base_folder.field_list
            },
            'sub_folders': {},
            'total_folders': 1 + len(self.source_struct_dict.sub_folders),
            'total_fields': self.source_struct_dict.get_total_field_count()
        }
        
        # 添加子文件夹信息
        for folder_key, folder_config in self.source_struct_dict.sub_folders.items():
            summary['sub_folders'][folder_key] = {
                'name': folder_config.folder_name,
                'field_count': len(folder_config.field_list),
                'fields': folder_config.field_list
            }
        
        return summary
    
    def print_config_summary(self):
        """打印配置摘要"""
        if not self.source_struct_dict:
            print("❌ 配置尚未加载")
            return
        
        summary = self.get_config_summary()
        
        print("=" * 60)
        print("📋 源文件结构配置摘要")
        print("=" * 60)
        print(f"📁 配置文件: {summary['config_file']}")
        print(f"⏰ 加载时间: {summary['loaded_at']}")
        print(f"📊 总文件夹数: {summary['total_folders']}")
        print(f"📊 总字段数: {summary['total_fields']}")
        print()
        
        # 基础文件夹信息
        base_info = summary['base_folder']
        print(f"🏠 基础文件夹: {base_info['name']}")
        print(f"   字段数量: {base_info['field_count']}")
        if base_info['fields']:
            print(f"   字段列表: {', '.join(base_info['fields'])}")
        print()
        
        # 子文件夹信息
        print("📂 子文件夹配置:")
        for folder_key, folder_info in summary['sub_folders'].items():
            print(f"   🔹 {folder_key} ({folder_info['name']})")
            print(f"      字段数量: {folder_info['field_count']}")
            if folder_info['fields']:
                print(f"      字段列表: {', '.join(folder_info['fields'])}")
            print()
    
    def get_fields_by_folder_name(self, folder_name: str) -> Optional[List[str]]:
        """
        根据文件夹名称获取字段列表
        
        Args:
            folder_name: 文件夹名称
            
        Returns:
            Optional[List[str]]: 字段列表，如果未找到返回None
        """
        if not self.source_struct_dict:
            raise RuntimeError("配置尚未加载，请先调用load_config()")
        
        folder_config = self.source_struct_dict.get_folder_by_name(folder_name)
        return folder_config.field_list if folder_config else None
    
    def export_to_dict(self) -> Dict[str, Any]:
        """
        导出为字典格式
        
        Returns:
            Dict: 配置字典
        """
        if not self.source_struct_dict:
            raise RuntimeError("配置尚未加载，请先调用load_config()")
        
        return {
            'base_folder': {
                'folder_name': self.source_struct_dict.base_folder.folder_name,
                'field_list': self.source_struct_dict.base_folder.field_list
            },
            'sub_folders': {
                key: {
                    'folder_name': config.folder_name,
                    'field_list': config.field_list
                }
                for key, config in self.source_struct_dict.sub_folders.items()
            }
        }


def load_source_struct_config(config_file: str = "source_file_struct_fanzha.yaml") -> SourceStructConfig:
    """
    便利函数：加载源文件结构配置
    
    Args:
        config_file: 配置文件路径
        
    Returns:
        SourceStructConfig: 结构化配置对象
    """
    loader = SourceStructLoader(config_file)
    return loader.load_config()


if __name__ == "__main__":
    # 测试代码
    print("🚀 源文件结构配置加载器测试")
    
    try:
        # 创建加载器
        loader = SourceStructLoader()
        
        # 加载配置
        source_struct_dict = loader.load_config()
        
        # 打印摘要
        loader.print_config_summary()
        
        # 测试字段查询
        print("🔍 字段查询测试:")
        test_folders = ["报案书", "投资协议复印件", "付款凭证复印件"]
        for folder_name in test_folders:
            fields = loader.get_fields_by_folder_name(folder_name)
            if fields:
                print(f"   📁 {folder_name}: {len(fields)} 个字段")
                print(f"      {', '.join(fields)}")
            else:
                print(f"   📁 {folder_name}: 未找到配置")
        
        # 导出字典
        config_dict = loader.export_to_dict()
        print(f"\n✅ 配置已成功加载并结构化存储")
        print(f"📊 全局变量 source_struct_dict 类型: {type(source_struct_dict)}")
        
    except Exception as e:
        print(f"❌ 加载失败: {e}")
