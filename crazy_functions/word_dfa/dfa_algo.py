text = """
Large language models (LLMs) have demonstrated remarkable potential in solving complex tasks across diverse domains, typically by employing agentic workflows that follow detailed instructions and operational sequences. However, constructing these workflows requires significant human effort, limiting scalability and generalizability. Recent research has sought to automate the generation and optimization of these workflows, but existing methods still rely on initial manual setup and fall short of achieving fully automated and effective workflow generation. To address this challenge, we reformulate workflow optimization as a search problem over code-represented workflows, where LLM-invoking nodes are connected by edges. We introduce \modelbold, an automated framework that efficiently explores this space using Monte Carlo Tree Search, iteratively refining workflows through code modification, tree-structured experience, and execution feedback. Empirical evaluations across six benchmark datasets demonstrate \model's efficacy, yielding a 5.7\% average improvement over state-of-the-art baselines. Furthermore, \model enables smaller models to outperform GPT-4o on specific tasks at 4.55\% of its inference cost in dollars. The code will be available at \href{https://github.com/geekan/MetaGPT}{https://github.com/geekan/MetaGPT}.
"""



ai_terms_from_web = """

# Letter All
[*Return*](https://github.com/SyncedAI00/Artificial-Intelligence-Terminology/blob/master/README.md)
索引编号|英文术语|中文翻译|常用缩写|来源&扩展|备注
---|---|---|---|---|---
AITD-00000|0-1 Loss Function|0-1损失函数||[1]||
AITD-00001|Absolute Loss Function|绝对损失函数||[1]||
AITD-00002|Absolute Value Rectification|绝对值整流||[1]||
AITD-00003|Accept-Reject Sampling Method|接受-拒绝抽样法/接受-拒绝采样法||[1]||
AITD-00004|Acceptance Distribution|接受分布||[1]||
AITD-00005|Access Parameters|访问参数||[1]||
AITD-00006|Accumulated Error Backpropagation|累积误差反向传播||[1]||
AITD-00007|Accuracy|准确率||[[1]](https://www.nature.com/articles/s41557-021-00716-z)||
AITD-00008|Acoustic|声学||[1]||
AITD-00009|Acoustic Modeling|声学建模||[1]||
AITD-00010|Acquisition Function|采集函数||[[1]](https://www.jiqizhixin.com/articles/2017-08-18-5)||
AITD-00011|Action|动作||[1]||
AITD-00012|Action Value Function|动作价值函数||[1]||
AITD-00013|Actionism|行为主义||[1]||
AITD-00014|Activation|活性值||[1]||
AITD-00015|Activation Function|激活函数||[[1]](https://www.jiqizhixin.com/articles/2017-06-11-4)[[2]](https://www.jiqizhixin.com/articles/2017-06-18-3)[[3]](https://pubs.rsc.org/en/content/chapter/bk9781788017893-00109/978-1-78801-789-3)[[4]](https://pubs.rsc.org/en/content/chapter/bk9781788017893-00016/978-1-78801-789-3)|机器学习|
AITD-00016|Active Learning|主动学习||[[1]](https://www.nature.com/articles/s41557-021-00716-z)|机器学习|
AITD-00017|Actor|演员||[1]||
AITD-00018|Actor-Critic Algorithm|演员-评论家算法||[1]||
AITD-00019|Actor-Critic Method|演员-评论家法||[[1]](https://www.jiqizhixin.com/articles/2017-08-14)||
AITD-00020|Adaptive Bitrate Algorithm|自适应比特率算法|ABR|[1]||
AITD-00021|Adaptive Boosting|AdaBoost||[1]||
AITD-00022|Adaptive Gradient Algorithm|AdaGrad||[1]||
AITD-00023|Adaptive Moment Estimation Algorithm|Adam算法|Adam|[1]||
AITD-00024|Adaptive Resonance Theory|自适应谐振理论|ART|[1]||
AITD-00025|Additive Model|加性模型||[1]||
AITD-00026|Adversarial|对抗||[1]||
AITD-00027|Adversarial Example|对抗样本||[[1]](https://www.jiqizhixin.com/articles/2018-01-06-6)||
AITD-00028|Adversarial Networks|对抗网络||[[1]](https://www.jiqizhixin.com/articles/2018-01-08-5)||
AITD-00029|Adversarial Training|对抗训练||[1]||
AITD-00030|Affine Layer|仿射层||[1]||
AITD-00031|Affine Transformation|仿射变换||[1]||
AITD-00032|Affinity Matrix|亲和矩阵||[1]||
AITD-00033|Agent|智能体||[[1]](https://www.jiqizhixin.com/articles/2017-04-06-6)[[2]](https://www.jiqizhixin.com/articles/2017-06-15-6)[[3]](https://www.jiqizhixin.com/articles/2017-06-10-2)[[4]](https://www.jiqizhixin.com/articles/2017-06-29-5)||
AITD-00034|Agglomerative|聚合||[1]||
AITD-00035|Agnostic PAC Learnable|不可知PAC可学习||[1]||
AITD-00036|Algorithm|算法||[[1]](https://jiqizhixin.github.io/AI-Terminology-page/)[[2]](https://www.jiqizhixin.com/articles/2017-05-23-4)[[3]](https://www.jiqizhixin.com/articles/2017-06-04-2)||
AITD-00037|Almost Everywhere|几乎处处||[1]||
AITD-00038|Almost Sure|几乎必然||[1]||
AITD-00039|Almost Sure Convergence|几乎必然收敛||[1]||
AITD-00040|Alpha-Beta Pruning|α-β修剪法||[1]||
AITD-00041|Alternative Splicing Dataset|选择性剪接数据集||[1]||
AITD-00042|Ambiguity|分歧||[1]||
AITD-00043|Analytic Gradient|解析梯度||[1]||
AITD-00044|Ancestral Sampling|原始采样||[1]||
AITD-00045|Annealed Importance Sampling|退火重要采样||[1]||
AITD-00046|Anomaly Detection|异常检测||[1]||
AITD-00047|Aperiodic|非周期的||[1]||
AITD-00048|Aperiodic Graph|非周期性图||[1]||
AITD-00049|Application-Specific Integrated Circuit|专用集成电路||[1]||
AITD-00050|Approximate Bayesian Computation|近似贝叶斯计算||[1]||
AITD-00051|Approximate Dynamic Programming|近似动态规划||[1]||
AITD-00052|Approximate Inference|近似推断||[1]||
AITD-00053|Approximation|近似||[1]||
AITD-00054|Approximation Error|近似误差||[1]||
AITD-00055|Architecture|架构||[[1]](https://www.jiqizhixin.com/articles/2018-01-12)||
AITD-00056|Area Under ROC Curve|AUC（ROC曲线下方面积，度量分类模型好坏的标准）|AUC|[[1]](https://pubs.rsc.org/en/content/chapter/bk9781788017893-00016/978-1-78801-789-3)|机器学习|
AITD-00057|Arithmetic Coding|算术编码||[1]||
AITD-00058|Artificial General Intelligence|通用人工智能|AGI|[[1]](https://www.jiqizhixin.com/articles/2018-01-06-2)||
AITD-00059|Artificial Intelligence|人工智能|AI|[[1]](https://www.jiqizhixin.com/articles/2017-05-21-4)[[2]](https://www.jiqizhixin.com/articles/2017-05-21-7)[[3]](https://www.jiqizhixin.com/articles/2017-05-17-16)[[4]](https://pubs.rsc.org/en/content/chapter/bk9781788017893-00001/978-1-78801-789-3)[[5]](https://pubs.rsc.org/en/content/chapter/bk9781788017893-00037/978-1-78801-789-3)|机器学习|
AITD-00060|Artificial Neural Network|人工神经网络|ANN|[[1]](https://pubs.rsc.org/en/content/chapter/bk9781788017893-00169/978-1-78801-789-3)[[2]](https://pubs.rsc.org/en/content/chapter/bk9781788017893-00076/978-1-78801-789-3)|机器学习|
AITD-00061|Artificial Neuron|人工神经元||[1]||
AITD-00062|Association Analysis|关联分析||[1]||
AITD-00063|Associative Memory|联想记忆||[1]||
AITD-00064|Associative Memory Model|联想记忆模型||[1]||
AITD-00065|Asymptotically Unbiased|渐近无偏||[1]||
AITD-00066|Asynchronous Stochastic Gradient Descent|异步随机梯度下降||[1]||
AITD-00067|Asynchronous|异步||[1]||
AITD-00068|Atrous Convolution|空洞卷积||[1]||
AITD-00069|Attention|注意力||[[1]](https://pubs.rsc.org/en/content/chapter/bk9781788017893-00136/978-1-78801-789-3)[[2]](https://pubs.rsc.org/en/content/chapter/bk9781788017893-00372/978-1-78801-789-3)|机器学习|
AITD-00070|Attention Cue|注意力提示||[1]||
AITD-00071|Attention Distribution|注意力分布||[1]||
AITD-00072|Attention Mechanism|注意力机制||[[1]](https://www.jiqizhixin.com/articles/2017-06-19-4)[[2]](https://www.jiqizhixin.com/articles/2017-06-14-6)[[3]](https://www.jiqizhixin.com/articles/2017-06-28-5)||
AITD-00073|Attention Model|注意力模型||[1]||
AITD-00074|Attractor|吸引点||[1]||
AITD-00075|Attribute|属性||[1]||
AITD-00076|Attribute Conditional Independence Assumption|属性条件独立性假设||[1]||
AITD-00077|Attribute Space|属性空间||[1]||
AITD-00078|Attribute Value|属性值||[1]||
AITD-00079|Augmented Lagrangian|增广拉格朗日法||[1]||
AITD-00080|Auto-Regressive Network|自回归网络||[1]||
AITD-00081|Autoencoder|自编码器|AE|[[1]](https://www.jiqizhixin.com/articles/2017-04-26-5)||
AITD-00082|Automatic Differentiation|自动微分|AD|[[1]](https://www.jiqizhixin.com/articles/2017-11-07)||
AITD-00083|Automatic Speech Recognition|自动语音识别|ASR|[1]||
AITD-00084|Automatic Summarization|自动摘要||[1]||
AITD-00085|Autoregressive Generative Model|自回归生成模型||[1]||
AITD-00086|Autoregressive Model|自回归模型|AR|[1]||
AITD-00087|Autoregressive Process|自回归过程||[1]||
AITD-00088|Average Gradient|平均梯度||[1]||
AITD-00089|Average Pooling Layer|平均汇聚层||[1]||
AITD-00090|Average-Pooling|平均汇聚||[1]||
AITD-00091|Averaged Perceptron|平均感知器||[1]||
AITD-00092|Back Propagation|反向传播|BP|[[1]](https://www.jiqizhixin.com/articles/2016-11-25-3)[[2]](https://pubs.rsc.org/en/content/chapter/bk9781788017893-00109/978-1-78801-789-3)[[3]](https://pubs.rsc.org/en/content/chapter/bk9781788017893-00169/978-1-78801-789-3)|机器学习|
AITD-00093|Back Propagation Algorithm|反向传播算法||[1]||
AITD-00094|Back Propagation Through Time|随时间反向传播|BPTT|[1]||
AITD-00095|Back-Off|回退||[1]||
AITD-00096|Backward|后向||[1]||
AITD-00097|Backward Induction|反向归纳||[1]||
AITD-00098|Backward Search|反向搜索||[1]||
AITD-00099|Bag of Words|词袋|BOW|[1]||
AITD-00100|Bagging|袋装| |[[1]](https://pubs.rsc.org/en/content/chapter/bk9781788017893-00037/978-1-78801-789-3)|机器学习|
AITD-00101|Bandit|赌博机/老虎机||[1]||
AITD-00102|Bandpass Filter|带通滤波器||[1]||
AITD-00103|Base|基||[1]||
AITD-00104|Base Classifier|基分类器||[1]||
AITD-00105|Base Learner|基学习器||[1]||
AITD-00106|Base Learning Algorithm|基学习算法||[1]||
AITD-00107|Base Vector|基向量||[1]||
AITD-00108|Baseline|基准||[[1]](https://www.nature.com/articles/s41557-021-00716-z)|机器学习|
AITD-00109|Basin of Attraction|吸引域||[1]||
AITD-00110|Batch|批量||[1]||
AITD-00111|Batch Gradient Descent|批量梯度下降法|BGD|[1]||
AITD-00112|Batch Learning|批量学习||[1]||
AITD-00113|Batch Normalization|批量规范化|BN|[1]||
AITD-00114|Batch Size|批量大小||[1]||
AITD-00115|Baum-Welch Algorithm|Baum-Welch算法||[1]||
AITD-00116|Bayes Classifier|贝叶斯分类器||[1]||
AITD-00117|Bayes Decision Rule|贝叶斯决策准则||[1]||
AITD-00118|Bayes Error|贝叶斯误差||[1]||
AITD-00119|Bayes Model Averaging|贝叶斯模型平均|BMA|[1]||
AITD-00120|Bayes Optimal Classifier|贝叶斯最优分类器||[1]||
AITD-00121|Bayes Risk|贝叶斯风险||[1]||
AITD-00122|Bayes' Rule|贝叶斯规则||[1]||
AITD-00123|Bayes' Theorem|贝叶斯定理||[1]||
AITD-00124|Bayesian Decision Theory|贝叶斯决策理论||[1]||
AITD-00125|Bayesian Estimation|贝叶斯估计||[1]||
AITD-00126|Bayesian Inference|贝叶斯推断||[[1]](https://pubs.rsc.org/en/content/chapter/bk9781788017893-00372/978-1-78801-789-3)|统计，机器学习|
AITD-00127|Bayesian Learning|贝叶斯学习||[1]||
AITD-00128|Bayesian Linear Regression|贝叶斯线性回归||[1]||
AITD-00129|Bayesian Network|贝叶斯网/贝叶斯网络||[[1]](https://pubs.rsc.org/en/content/chapter/bk9781788017893-00109/978-1-78801-789-3)|Network翻译为网或网络皆可，只要统一翻译成网或者统一翻译成网络即可；统计，机器学习|
AITD-00130|Bayesian Optimization|贝叶斯优化||[[1]](https://www.jiqizhixin.com/articles/2017-11-28)||
AITD-00131|Bayesian Probability|贝叶斯概率||[1]||
AITD-00132|Bayesian Statistics|贝叶斯统计||[1]||
AITD-00133|Beam Search|束搜索||[[1]](https://www.jiqizhixin.com/articles/2018-01-31-3)||
AITD-00134|Benchmark|基准||[1]||
AITD-00135|Belief Network|信念网/信念网络|BN|[1]|Network翻译为网或网络皆可，只要统一翻译成网或者统一翻译成网络即可|
AITD-00136|Belief Propagation|信念传播|BP|[1]||
AITD-00137|Bellman Equation|贝尔曼方程||[1]||
AITD-00138|Bellman Optimality Equation|贝尔曼最优方程||[1]||
AITD-00139|Bernoulli Distribution|伯努利分布||[[1]](https://pubs.rsc.org/en/content/chapter/bk9781788017893-00311/978-1-78801-789-3)|统计|
AITD-00140|Bernoulli Output Distribution|伯努利输出分布||[1]||
AITD-00141|Best-Arm Problem|最优臂问题||[1]||
AITD-00142|Beta Distribution|贝塔分布||[1]||
AITD-00143|Between-Class Scatter Matrix|类间散度矩阵||[1]||
AITD-00144|BFGS|BFGS||[1]||
AITD-00145|Bi-Directional Long-Short Term Memory|双向长短期记忆|Bi-LSTM|[1]||
AITD-00146|Bi-Partition|二分法||[1]||
AITD-00147|Bias|偏差/偏置||[[1]](https://pubs.rsc.org/en/content/chapter/bk9781788017893-00037/978-1-78801-789-3)[[2]](https://www.nature.com/articles/s41557-021-00716-z)[[3]](https://pubs.rsc.org/en/content/chapter/bk9781788017893-00016/978-1-78801-789-3)[[4]](https://pubs.rsc.org/en/content/chapter/bk9781788017893-00311/978-1-78801-789-3)|看上下语境；机器学习|
AITD-00148|Bias In Affine Function|偏置||[1]|看上下语境|
AITD-00149|Bias In Statistics|偏差||[1]|看上下语境|
AITD-00150|Bias Shift|偏置偏移||[1]||
AITD-00151|Bias-Variance Decomposition|偏差 - 方差分解||[1]||
AITD-00152|Bias-Variance Dilemma|偏差 - 方差困境||[1]||
AITD-00153|Biased|有偏||[[1]](https://www.nature.com/articles/s41557-021-00716-z)|机器学习|
AITD-00154|Biased Importance Sampling|有偏重要采样||[1]||
AITD-00155|Bidirectional Language Model|双向语言模型||[1]||
AITD-00156|Bidirectional Recurrent Neural Network|双向循环神经网络|Bi-RNN|[1]||
AITD-00157|Bigram|二元语法||[1]||
AITD-00158|Bilingual Evaluation Understudy|BLEU||[1]||
AITD-00159|Binary Classification|二分类||[1]||
AITD-00160|Binary Relation|二元关系||[1]||
AITD-00161|Binary Sparse Coding|二值稀疏编码||[1]||
AITD-00162|Binomial Distribution|二项分布||[1]||
AITD-00163|Binomial Logistic Regression Model|二项对数几率回归||[1]||
AITD-00164|Binomial Test|二项检验||[1]||
AITD-00165|Biological Plausibility|生物学合理性||[1]||
AITD-00166|Bit|比特||[1]||
AITD-00167|Block|块||[1]||
AITD-00168|Block Coordinate Descent|块坐标下降||[1]||
AITD-00169|Block Gibbs Sampling|块吉布斯采样||[1]||
AITD-00170|Boilerplate Code|样板代码||[1]||
AITD-00171|Boltzmann|玻尔兹曼||[1]||
AITD-00172|Boltzmann Distribution|玻尔兹曼分布||[1]||
AITD-00173|Boltzmann Factor|玻尔兹曼因子||[1]||
AITD-00174|Boltzmann Machine|玻尔兹曼机||[[1]](https://www.jiqizhixin.com/articles/2017-10-08-4)||
AITD-00175|Boosting|Boosting（一种模型训练加速方式）||[[1]](https://pubs.rsc.org/en/content/chapter/bk9781788017893-00037/978-1-78801-789-3)||
AITD-00176|Boosting Tree|提升树||[1]||
AITD-00177|Bootstrap Aggregating|Bagging||[1]||
AITD-00178|Bootstrap Sampling|自助采样法||[1]||
AITD-00179|Bootstrapping|自助法/自举法||[1]||
AITD-00180|Bottleneck Layer|瓶颈层||[1]||
AITD-00181|Bottom-Up|自下而上| |[[1]](https://pubs.rsc.org/en/content/chapter/bk9781788017893-00311/978-1-78801-789-3)| |
AITD-00182|Bounding Boxes|边界框||[1]||
AITD-00183|Break-Event Point|平衡点|BEP|[1]||
AITD-00184|Bridge Sampling|桥式采样||[1]||
AITD-00185|Broadcasting|广播||[1]||
AITD-00186|Broyden's Algorithm|Broyden类算法||[1]||
AITD-00187|Bucketing|分桶||[1]||
AITD-00188|Burn-In Period|预烧期||[1]||
AITD-00189|Burning-In|磨合||[1]||
AITD-00190|Calculus|微积分||[1]||
AITD-00191|Calculus of Variations|变分法||[1]||
AITD-00192|Calibration|校准||[1]||
AITD-00193|Canonical|正则的||[1]||
AITD-00194|Canonical Correlation Analysis|典型相关分析|CCA|[1]||
AITD-00195|Capacity|容量||[1]||
AITD-00196|Cartesian Coordinate|笛卡尔坐标||[1]||
AITD-00197|Cascade|级联||[1]||
AITD-00198|Cascade-Correlation|级联相关||[1]||
AITD-00199|Catastrophic Forgetting|灾难性遗忘 ||[1]||
AITD-00200|Categorical Attribute|分类属性||[1]||
AITD-00201|Categorical Distribution|类别分布||[1]||
AITD-00202|Causal Factor|因果因子||[1]||
AITD-00203|Causal Modeling|因果模型||[1]||
AITD-00204|Cell|单元||[1]||
AITD-00205|Centered Difference|中心差分||[1]||
AITD-00206|Central Limit Theorem|中心极限定理||[1]||
AITD-00207|Chain Rule|链式法则||[1]||
AITD-00208|Channel|通道||[1]||
AITD-00209|Chaos|混沌||[1]||
AITD-00210|Chebyshev Distance|切比雪夫距离||[1]||
AITD-00211|Chord|弦||[1]||
AITD-00212|Chordal Graph|弦图||[1]||
AITD-00213|City Block Distance|街区距离||[1]||
AITD-00214|Class|类别||[1]||
AITD-00215|Class Label|类标记||[1]||
AITD-00216|Class-Conditional Probability|类条件概率||[1]||
AITD-00217|Class-Imbalance|类别不平衡||[1]||
AITD-00218|Classification|分类||[[1]](https://www.nature.com/articles/s41557-021-00716-z)[[2]](https://pubs.rsc.org/en/content/chapter/bk9781788017893-00016/978-1-78801-789-3)||
AITD-00219|Classification And Regression Tree|分类与回归树|CART|[[1]](https://pubs.rsc.org/en/content/chapter/bk9781788017893-00424/978-1-78801-789-3)||
AITD-00220|Classifier|分类器||[1]||
AITD-00221|Clip Gradient|梯度截断||[1]||
AITD-00222|Clipping The Gradient|截断梯度||[1]||
AITD-00223|Clique|团||[1]||
AITD-00224|Clique Potential|团势能||[1]||
AITD-00225|Clockwork RNN|时钟循环神经网络||[1]||
AITD-00226|Closed Form Solution|闭式解||[1]||
AITD-00227|Closed-Form|闭式||[1]||
AITD-00228|Cluster|簇||[[1]](https://www.nature.com/articles/s41557-021-00716-z)||
AITD-00229|Cluster Analysis|聚类分析||[1]||
AITD-00230|Cluster Assumption|聚类假设||[1]||
AITD-00231|Clustering|聚类||[[1]](https://www.jiqizhixin.com/articles/2018-01-09)||
AITD-00232|Clustering Ensemble|聚类集成||[1]||
AITD-00233|Co-Adapting|共适应||[1]||
AITD-00234|Co-Occurrence|共现||[1]||
AITD-00235|Co-Occurrence Frequency|共现词频||[1]||
AITD-00236|Co-Training|协同训练||[1]||
AITD-00237|Code|编码||[1]||
AITD-00238|Codebook Learning|码书学习||[1]||
AITD-00239|Coding Matrix|编码矩阵||[1]||
AITD-00240|Collaborative Filtering|协同过滤||[[1]](https://www.jiqizhixin.com/articles/2017-12-23-2)||
AITD-00241|Collapsed Gibbs Sampling|收缩的吉布斯抽样||[1]||
AITD-00242|Collinearity|共线性||[1]||
AITD-00243|COLT|国际学习理论会议||[1]||
AITD-00244|Column|列||[1]||
AITD-00245|Column Space|列空间||[1]||
AITD-00246|Combinatorial Optimization|组合优化||[1]||
AITD-00247|Committee-Based Learning|基于委员会的学习||[1]||
AITD-00248|Common Cause|共因||[1]||
AITD-00249|Common Parent|同父||[1]||
AITD-00250|Compact Singular Value Decomposition|紧奇异值分解||[1]||
AITD-00251|Competitive Learning|竞争型学习||[1]||
AITD-00252|Complementary Slackness|互补松弛||[1]||
AITD-00253|Complete Graph|完全图||[1]||
AITD-00254|Complete Linkage|完全连接||[1]||
AITD-00255|Complete-Data|完全数据||[1]||
AITD-00256|Complex Cell|复杂细胞||[1]||
AITD-00257|Component Learner|组件学习器||[1]||
AITD-00258|Compositionality|组合性||[1]||
AITD-00259|Comprehensibility|可解释性||[1]||
AITD-00260|Computation Cost|计算代价||[1]||
AITD-00261|Computation Graph|计算图||[1]||
AITD-00262|Computational Learning Theory|计算学习理论||[1]||
AITD-00263|Computational Linguistics|计算语言学||[1]||
AITD-00264|Computer Vision|计算机视觉| |[[1]](https://pubs.rsc.org/en/content/chapter/bk9781788017893-00001/978-1-78801-789-3)| |
AITD-00265|Concatenate|连结||[1]||
AITD-00266|Concept Class|概念类||[1]||
AITD-00267|Concept Drift|概念漂移||[1]||
AITD-00268|Concept Learning System|概念学习系统|CLS|[1]||
AITD-00269|Concept Shift|概念偏移||[1]||
AITD-00270|Conditional Computation|条件计算||[1]||
AITD-00271|Conditional Entropy|条件熵||[1]||
AITD-00272|Conditional Independence|条件独立||[1]||
AITD-00273|Conditional Language Model|条件语言模型||[1]||
AITD-00274|Conditional Mutual Information|条件互信息||[1]||
AITD-00275|Conditional Probability|条件概率||[1]||
AITD-00276|Conditional Probability Density Function|条件概率密度函数||[1]||
AITD-00277|Conditional Probability Distribution|条件概率分布||[1]||
AITD-00278|Conditional Probability Table|条件概率表|CPT|[1]||
AITD-00279|Conditional Random Field|条件随机场|CRF|[1]||
AITD-00280|Conditional Risk|条件风险||[1]||
AITD-00281|Conditionally Independent|条件独立的||[1]||
AITD-00282|Conference On Neural Information Processing Systems|国际神经信息处理系统会议|NeurIPS|[[1]](https://www.jiqizhixin.com/articles/2017-12-18-9)||
AITD-00283|Confidence|置信度||[1]||
AITD-00284|Conflict Resolution|冲突消解||[1]||
AITD-00285|Confusion Matrix|混淆矩阵||[[1]](https://pubs.rsc.org/en/content/chapter/bk9781788017893-00016/978-1-78801-789-3)|机器学习|
AITD-00286|Conjugate|共轭||[1]||
AITD-00287|Conjugate Directions|共轭方向||[1]||
AITD-00288|Conjugate Distribution|共轭分布||[1]||
AITD-00289|Conjugate Gradient|共轭梯度| |[[1]](https://pubs.rsc.org/en/content/chapter/bk9781788017893-00488/978-1-78801-789-3)|优化，数学|
AITD-00290|Conjugate Prior|共轭先验||[1]||
AITD-00291|Connection Weight|连接权||[1]||
AITD-00292|Connectionism|连接主义||[1]||
AITD-00293|Consistency|一致性||[1]||
AITD-00294|Consistency Convergence|一致性收敛||[1]||
AITD-00295|Constrained Optimization|约束优化||[1]||
AITD-00296|Content-Addressable Memory|基于内容寻址的存储|CAM|[1]||
AITD-00297|Context Variable|上下文变量||[1]||
AITD-00298|Context Vector|上下文向量||[1]||
AITD-00299|Context Window|上下文窗口||[1]||
AITD-00300|Context Word|上下文词||[1]||
AITD-00301|Context-Specific Independences|特定上下文独立||[1]||
AITD-00302|Contextual Bandit|上下文赌博机/上下文老虎机||[1]||
AITD-00303|Contextualized Representation|基于上下文的表示||[1]||
AITD-00304|Contingency Table|列联表||[1]||
AITD-00305|Continous Bag-Of-Words Model|连续词袋模型|CBOW|[1]||
AITD-00306|Continuation Method|延拓法||[1]||
AITD-00307|Continuing Task|持续式任务||[1]||
AITD-00308|Continuous Attribute|连续属性||[1]||
AITD-00309|Continuous Learning|持续学习||[1]||
AITD-00310|Continuous Optimization|连续优化||[1]||
AITD-00311|Contractive|收缩||[1]||
AITD-00312|Contractive Autoencoder|收缩自编码器||[1]||
AITD-00313|Contractive Neural Network|收缩神经网络||[1]||
AITD-00314|Contrastive Divergence|对比散度||[1]||
AITD-00315|Controller|控制器||[1]||
AITD-00316|Convergence|收敛||[1]||
AITD-00317|Conversational Agent|会话智能体||[1]||
AITD-00318|Convex Optimization|凸优化||[[1]](https://www.jiqizhixin.com/articles/2017-12-29-4)||
AITD-00319|Convex Quadratic Programming|凸二次规划||[1]||
AITD-00320|Convex Set|凸集||[1]||
AITD-00321|Convexity|凸性||[1]||
AITD-00322|Convolution|卷积||[1]||
AITD-00323|Convolutional Boltzmann Machine|卷积玻尔兹曼机||[1]||
AITD-00324|Convolutional Deep Belief Network|卷积深度信念网络|CDBN|[1]||
AITD-00325|Convolutional Kernel|卷积核||[1]||
AITD-00326|Convolutional Network|卷积网络||[1]||
AITD-00327|Convolutional Neural Network|卷积神经网络|CNN|[[1]](https://www.jiqizhixin.com/articles/2017-12-19-8)[[2]](https://www.jiqizhixin.com/articles/2018-01-08-6)[[3]](https://www.jiqizhixin.com/articles/2017-12-18-2)||
AITD-00328|Coordinate|坐标||[1]||
AITD-00329|Coordinate Ascent|坐标上升||[1]||
AITD-00330|Coordinate Descent|坐标下降||[1]||
AITD-00331|Coparent|共父||[1]||
AITD-00332|Corpus|语料库||[1]||
AITD-00333|Correlation|相关系数| |[[1]](https://pubs.rsc.org/en/content/chapter/bk9781788017893-00109/978-1-78801-789-3)| |
AITD-00334|Correlation Coefficient|相关系数||[1]||
AITD-00335|Cosine|余弦||[1]||
AITD-00336|Cosine Decay|余弦衰减||[1]||
AITD-00337|Cosine Similarity|余弦相似度||[1]||
AITD-00338|Cost|代价||[1]||
AITD-00339|Cost Curve|代价曲线||[1]||
AITD-00340|Cost Function|代价函数||[[1]](https://pubs.rsc.org/en/content/chapter/bk9781788017893-00109/978-1-78801-789-3)[[2]](https://pubs.rsc.org/en/content/chapter/bk9781788017893-00016/978-1-78801-789-3)[[3]](https://pubs.rsc.org/en/content/chapter/bk9781788017893-00169/978-1-78801-789-3)||
AITD-00341|Cost Matrix|代价矩阵||[1]||
AITD-00342|Cost-Sensitive|代价敏感||[1]||
AITD-00343|Covariance|协方差||[[1]](https://pubs.rsc.org/en/content/chapter/bk9781788017893-00037/978-1-78801-789-3)||
AITD-00344|Covariance Matrix|协方差矩阵||[[1]](https://pubs.rsc.org/en/content/chapter/bk9781788017893-00037/978-1-78801-789-3)||
AITD-00345|Covariance RBM|协方差RBM||[1]||
AITD-00346|Covariate Shift|协变量偏移||[1]||
AITD-00347|Coverage|覆盖||[1]||
AITD-00348|Credit Assignment Problem|贡献度分配问题|CAP|[1]||
AITD-00349|Criterion|准则||[1]||
AITD-00350|Critic|评论家||[1]||
AITD-00351|Critic Network|评价网络||[1]||
AITD-00352|Critical Point|临界点||[1]||
AITD-00353|Critical Temperatures|临界温度||[1]||
AITD-00354|Cross Correlation|互相关||[1]||
AITD-00355|Cross Entropy|交叉熵||[1]||
AITD-00356|Cross Validation|交叉验证||[[1]](https://www.jiqizhixin.com/articles/2017-10-16-4)||
AITD-00357|Cross-Entropy Loss Function|交叉熵损失函数||[1]||
AITD-00358|Crowdsourcing|众包||[[1]](https://www.jiqizhixin.com/articles/2017-12-28-2)||
AITD-00359|Cumulative Distribution Function|累积分布函数|CDF|[1]||
AITD-00360|Cumulative Function|累积函数||[1]||
AITD-00361|Curriculum Learning|课程学习||[1]||
AITD-00362|Curse of Dimensionality|维数灾难||[1]||
AITD-00363|Curvature|曲率||[1]||
AITD-00364|Curve-Fitting|曲线拟合||[1]||
AITD-00365|Cut Point|截断点||[1]||
AITD-00366|Cutting Plane Algorithm|割平面法||[1]||
AITD-00367|Cybernetics|控制论||[1]||
AITD-00368|Cyclic Learning Rate|循环学习率||[1]||
AITD-00369|Damping|衰减||[1]||
AITD-00370|Damping Factor|阻尼因子||[1]||
AITD-00371|Data|数据||[1]||
AITD-00372|Data Augmentation|数据增强| |[[1]](https://pubs.rsc.org/en/content/chapter/bk9781788017893-00424/978-1-78801-789-3)| |
AITD-00373|Data Generating Distribution|数据生成分布||[1]||
AITD-00374|Data Generating Process|数据生成过程||[1]||
AITD-00375|Data Instance|数据样本||[1]||
AITD-00376|Data Mining|数据挖掘||[[1]](https://pubs.rsc.org/en/content/chapter/bk9781788017893-00037/978-1-78801-789-3)||
AITD-00377|Data Parallelism|数据并行||[1]||
AITD-00378|Data Point|数据点||[1]||
AITD-00379|Data Preprocessing|数据预处理||[1]||
AITD-00380|Data Set|数据集||[[1]](https://www.jiqizhixin.com/articles/2018-01-04-6)[[2]](https://www.nature.com/articles/s41557-021-00716-z)||
AITD-00381|Data Wrangling|数据整理||[[1]](https://www.jiqizhixin.com/articles/2017-08-25-5)||
AITD-00382|Dataset Augmentation|数据集增强||[1]||
AITD-00383|Davidon-Fletcher-Powell|DFP||[1]||
AITD-00384|Debugging Strategy|调试策略||[1]||
AITD-00385|Decision Boundary|决策边界||[1]||
AITD-00386|Decision Function|决策函数||[1]||
AITD-00387|Decision Stump|决策树桩||[1]||
AITD-00388|Decision Surface|决策平面||[1]||
AITD-00389|Decision Tree|决策树|DT|[[1]](https://www.jiqizhixin.com/articles/2018-01-10)[[2]](https://www.jiqizhixin.com/articles/2017-11-29-5)[[3]](https://pubs.rsc.org/en/content/chapter/bk9781788017893-00311/978-1-78801-789-3)[[4]](https://pubs.rsc.org/en/content/chapter/bk9781788017893-00037/978-1-78801-789-3)||
AITD-00390|Decoder|解码器||[1]||
AITD-00391|Decoding|解码||[1]||
AITD-00392|Decompose|分解||[1]||
AITD-00393|Deconvolution|反卷积||[1]||
AITD-00394|Deconvolutional Network|反卷积网络||[[1]](https://www.jiqizhixin.com/articles/2017-09-14)||
AITD-00395|Deduction|演绎||[1]||
AITD-00396|Deep Belief Network|深度信念网络|DBN|[1]||
AITD-00397|Deep Boltzmann Machine|深度玻尔兹曼机|DBM|[1]||
AITD-00398|Deep Circuit|深度回路||[1]||
AITD-00399|Deep Convolutional Generative Adversarial Network|深度卷积生成对抗网络|DCGAN|[1]||
AITD-00400|Deep Feedforward Network|深度前馈网络||[1]||
AITD-00401|Deep Generative Model|深度生成模型||[1]||
AITD-00402|Deep Learning|深度学习|DL|[[1]](https://www.jiqizhixin.com/articles/2018-01-17-2)[[2]](https://www.jiqizhixin.com/articles/2018-01-15-4)[[3]](https://www.jiqizhixin.com/articles/2018-01-15-2)[[4]](https://pubs.rsc.org/en/content/chapter/bk9781788017893-00037/978-1-78801-789-3)[[5]](https://www.nature.com/articles/s41557-021-00716-z)||
AITD-00403|Deep Model|深度模型||[1]||
AITD-00404|Deep Network|深度网络||[1]||
AITD-00405|Deep Neural Network|深度神经网络|DNN|[[1]](https://www.jiqizhixin.com/articles/2018-01-15-2)[[2]](https://www.jiqizhixin.com/articles/2018-01-10)[[3]](https://www.jiqizhixin.com/articles/2018-01-07-2)[[4]](https://pubs.rsc.org/en/content/chapter/bk9781788017893-00109/978-1-78801-789-3)[[5]](https://pubs.rsc.org/en/content/chapter/bk9781788017893-00372/978-1-78801-789-3)[[6]](https://pubs.rsc.org/en/content/chapter/bk9781788017893-00037/978-1-78801-789-3)[[7]](https://pubs.rsc.org/en/content/chapter/bk9781788017893-00280/978-1-78801-789-3)||
AITD-00406|Deep Q-Learning|深度 Q 学习||[[1]](https://www.jiqizhixin.com/articles/2017-10-10-2)[[2]](https://www.jiqizhixin.com/articles/2017-08-22-8)||
AITD-00407|Deep Q-Network|深度Q网络|DQN|[1]||
AITD-00408|Deep Reinforcement Learning|深度强化学习| |[[1]](https://pubs.rsc.org/en/content/chapter/bk9781788017893-00340/978-1-78801-789-3)| |
AITD-00409|Deep Sequence Model|深度序列模型||[1]||
AITD-00410|Default Rule|默认规则||[1]||
AITD-00411|Definite Integral|定积分||[1]||
AITD-00412|Degree Of Belief|信任度||[1]||
AITD-00413|Delta-Bar-Delta|Delta-Bar-Delta||[1]||
AITD-00414|Denoising|去噪||[1]||
AITD-00415|Denoising Autoencoder|去噪自编码器||[1]||
AITD-00416|Denoising Score Matching|去躁分数匹配||[1]||
AITD-00417|Denominator Layout|分母布局||[1]||
AITD-00418|Dense|稠密||[1]||
AITD-00419|Density Estimation|密度估计||[1]||
AITD-00420|Density-Based Clustering|密度聚类||[1]||
AITD-00421|Dependency|依赖||[1]||
AITD-00422|Depth|深度||[1]||
AITD-00423|Derivative|导数||[1]||
AITD-00424|Description|描述||[1]||
AITD-00425|Design Matrix|设计矩阵||[1]||
AITD-00426|Detailed Balance|细致平衡||[1]||
AITD-00427|Detailed Balance Equation|细致平衡方程||[1]||
AITD-00428|Detector Stage|探测级||[1]||
AITD-00429|Determinant|行列式||[1]||
AITD-00430|Deterministic|确定性||[1]||
AITD-00431|Deterministic Model|确定性模型||[1]||
AITD-00432|Deterministic Policy|确定性策略||[1]||
AITD-00433|Development Set|开发集||[1]||
AITD-00434|Diagonal Matrix|对角矩阵||[1]||
AITD-00435|Diameter|直径||[1]||
AITD-00436|Dictionary|字典||[1]||
AITD-00437|Dictionary Learning|字典学习||[1]||
AITD-00438|Differentiable Function|可微函数||[1]||
AITD-00439|Differentiable Neural Computer|可微分神经计算机||[[1]](https://www.jiqizhixin.com/articles/2017-04-11-7)||
AITD-00440|Differential Entropy|微分熵||[1]||
AITD-00441|Differential Equation|微分方程||[1]||
AITD-00442|Differentiation|微分||[1]||
AITD-00443|Dilated Convolution|膨胀卷积||[1]||
AITD-00444|Dimension|维度||[1]||
AITD-00445|Dimension Reduction|降维||[[1]](https://pubs.rsc.org/en/content/chapter/bk9781788017893-00037/978-1-78801-789-3)||
AITD-00446|Dimensionality Reduction Algorithm|降维算法| |[[1]](https://pubs.rsc.org/en/content/chapter/bk9781788017893-00169/978-1-78801-789-3)[[2]](https://www.jiqizhixin.com/articles/2017-08-31-2)| |
AITD-00447|Dirac Delta Function|Dirac Delta函数||[1]||
AITD-00448|Dirac Distribution|Dirac分布||[1]||
AITD-00449|Directed|有向||[1]||
AITD-00450|Directed Acyclic Graph|有向非循环图|DAG|[1]||
AITD-00451|Directed Edge|有向边||[1]||
AITD-00452|Directed Graph|有向图||[1]||
AITD-00453|Directed Graphical Model|有向图模型||[1]||
AITD-00454|Directed Model|有向模型||[1]||
AITD-00455|Directed Separation|有向分离||[1]||
AITD-00456|Directional Derivative|方向导数||[1]||
AITD-00457|Dirichlet Distribution|狄利克雷分布||[1]||
AITD-00458|Disagreement Measure|不合度量||[1]||
AITD-00459|Disagreement-Based Methods|基于分歧的方法||[1]||
AITD-00460|Discount Factor|衰减系数||[1]||
AITD-00461|Discounted Return|折扣回报||[1]||
AITD-00462|Discrete Optimization|离散优化||[1]||
AITD-00463|Discriminant Function|判别函数||[1]||
AITD-00464|Discriminative Approach|判别方法||[1]||
AITD-00465|Discriminative Model|判别式模型||[1]||
AITD-00466|Discriminative RBM|判别RBM||[1]||
AITD-00467|Discriminator|判别器||[1]||
AITD-00468|Discriminator Network|判别网络||[1]||
AITD-00469|Distance|距离||[1]||
AITD-00470|Distance Measure|距离度量||[1]||
AITD-00471|Distance Metric Learning|距离度量学习||[1]||
AITD-00472|Distributed Representation|分布式表示||[1]||
AITD-00473|Distribution|分布||[[1]](https://www.jiqizhixin.com/articles/2018-01-09)||
AITD-00474|Diverge|发散||[1]||
AITD-00475|Divergence|散度||[1]||
AITD-00476|Diversity|多样性||[1]||
AITD-00477|Diversity Measure|多样性度量/差异性度量||[1]||
AITD-00478|Divide-And-Conquer|分而治之||[1]||
AITD-00479|Divisive|分裂||[1]||
AITD-00480|Domain|领域||[1]||
AITD-00481|Domain Adaptation|领域自适应||[1]||
AITD-00482|Dominant Eigenvalue|主特征值||[1]||
AITD-00483|Dominant Eigenvector|主特征向量||[1]||
AITD-00485|Dominant Strategy|占优策略||[1]||
AITD-00486|Dot Product|点积||[1]||
AITD-00487|Double Backprop|双反向传播||[1]||
AITD-00488|Doubly Block Circulant Matrix|双重分块循环矩阵||[1]||
AITD-00489|Down Sampling|下采样||[1]||
AITD-00490|Downstream Task|下游任务||[1]||
AITD-00491|Dropout|暂退法||[1]||
AITD-00492|Dropout Boosting|暂退Boosting||[1]||
AITD-00493|Dropout Mask|暂退掩码||[1]||
AITD-00494|Dropout Method|暂退法||[1]||
AITD-00495|Dual Algorithm|对偶算法||[1]||
AITD-00496|Dual Problem|对偶问题||[1]||
AITD-00497|Dummy Node|哑结点||[1]||
AITD-00498|Dying ReLU Problem|死亡ReLU问题||[1]||
AITD-00499|Dynamic Bayesian Network|动态贝叶斯网络||[1]||
AITD-00500|Dynamic Computational Graph|动态计算图||[1]||
AITD-00501|Dynamic Fusion|动态融合||[1]||
AITD-00502|Dynamic Programming|动态规划||[1]||
AITD-00503|Dynamic Structure|动态结构||[1]||
AITD-00504|Dynamical System|动力系统||[1]||
AITD-00505|Eager Learning|急切学习||[1]||
AITD-00506|Early Stopping|早停||[1]||
AITD-00507|Earth-Mover's Distance|推土机距离|EMD|[1]||
AITD-00508|Echo State Network|回声状态网络||[1]||
AITD-00509|Edge|边||[1]||
AITD-00510|Edge Device|边缘设备||[[1]](https://www.jiqizhixin.com/articles/2017-09-24-8)||
AITD-00511|Effective Capacity|有效容量||[1]||
AITD-00512|Eigendecomposition|特征分解||[[1]](https://www.jiqizhixin.com/articles/2017-07-05-2)||
AITD-00513|Eigenvalue|特征值||[1]||
AITD-00514|Eigenvalue Decomposition|特征值分解||[1]||
AITD-00515|Elastic Net Regularization|弹性网络正则化||[1]||
AITD-00516|Elastic Weight Consolidation|弹性权重巩固||[1]||
AITD-00517|Element-Wise Product|逐元素积||[1]||
AITD-00518|Elementary Basis Vectors|基本单位向量||[1]||
AITD-00519|Ellipsoid Method|椭球法||[1]||
AITD-00520|Embedding|嵌入||[[1]](https://www.jiqizhixin.com/articles/2018-01-02-5)||
AITD-00521|Embedding Lookup Table|嵌入表||[1]||
AITD-00522|Emotional Analysis|情绪分析||[1]||
AITD-00523|Empirical Conditional Entropy|经验条件熵||[1]||
AITD-00524|Empirical Distribution|经验分布||[1]||
AITD-00525|Empirical Entropy|经验熵||[1]||
AITD-00526|Empirical Error|经验误差||[1]||
AITD-00527|Empirical Frequency|经验频率||[1]||
AITD-00528|Empirical Loss|经验损失||[1]||
AITD-00529|Empirical Risk|经验风险||[1]||
AITD-00530|Empirical Risk Minimization|经验风险最小化|ERM|[1]||
AITD-00531|Encoder|编码器||[1]||
AITD-00532|Encoder-Decoder|编码器-解码器（模型）| |[[1]](https://pubs.rsc.org/en/content/chapter/bk9781788017893-00169/978-1-78801-789-3)[[2]](https://pubs.rsc.org/en/content/chapter/bk9781788017893-00136/978-1-78801-789-3)| |
AITD-00533|Encoding|编码||[1]||
AITD-00534|End-To-End|端到端||[[1]](https://www.jiqizhixin.com/articles/2017-12-15)||
AITD-00535|End-To-End Learning|端到端学习||[1]||
AITD-00536|End-To-End Memory Network|端到端记忆网络|Memn2N|[1]||
AITD-00537|Energy Function|能量函数||[1]||
AITD-00538|Energy Gap|能量差异||[1]||
AITD-00539|Energy-Based Model|基于能量的模型||[1]||
AITD-00540|Ensemble|集成||[1]||
AITD-00541|Ensemble Learning|集成学习||[[1]](https://www.jiqizhixin.com/articles/2018-01-14-8)||
AITD-00542|Ensemble Pruning|集成修剪||[1]||
AITD-00543|Entropy|熵||[1]||
AITD-00544|Entropy Encoding|熵编码||[1]||
AITD-00545|Environment|环境||[1]||
AITD-00546|Episode|回合||[1]||
AITD-00547|Episodic Task|回合式任务||[1]||
AITD-00548|Epoch|轮||[1]||
AITD-00549|Equal-Width Convolution|等宽卷积||[1]||
AITD-00550|Equality Constraint|等式约束||[1]||
AITD-00551|Equilibrium Distribution|均衡分布||[1]||
AITD-00552|Equivariance|等变||[1]||
AITD-00553|Equivariant Representations|等变表示||[1]||
AITD-00554|Error|误差||[1]||
AITD-00555|Error Backpropagation Algorithm|误差反向传播算法||[1]||
AITD-00556|Error Backpropagation|误差反向传播||[1]||
AITD-00557|Error Bar|误差条||[1]||
AITD-00558|Error Correcting Output Codes|纠错输出编码|ECOC|[1]||
AITD-00559|Error Function|误差函数| |[[1]](https://pubs.rsc.org/en/content/chapter/bk9781788017893-00076/978-1-78801-789-3)| |
AITD-00560|Error Metric|误差度量||[1]||
AITD-00561|Error Rate|错误率||[1]||
AITD-00562|Error-Ambiguity Decomposition|误差－分歧分解||[1]||
AITD-00563|Estimation Error|估计误差||[1]||
AITD-00564|Estimation Of Mathematical Expectation|数学期望估计||[1]||
AITD-00565|Estimator|估计/估计量||[[1]](https://pubs.rsc.org/en/content/chapter/bk9781788017893-00340/978-1-78801-789-3)||
AITD-00566|Euclidean Distance|欧氏距离||[1]||
AITD-00567|Euclidean Norm|欧几里得范数||[1]||
AITD-00568|Euclidean Space|欧氏空间||[1]||
AITD-00569|Euler-Lagrange Equation|欧拉-拉格朗日方程||[1]||
AITD-00570|Evaluation Criterion|评价准则||[1]||
AITD-00571|Evidence|证据||[1]||
AITD-00572|Evidence Lower Bound|证据下界|ELBO|[1]||
AITD-00573|Evolution|演化||[1]||
AITD-00574|Evolutionary Computation|演化计算||[1]||
AITD-00575|Exact|确切的||[1]||
AITD-00576|Exact Inference|精确推断||[1]||
AITD-00577|Example|样例||[1]||
AITD-00578|Excess Error|额外误差||[1]||
AITD-00579|Exchangeable|可交换的||[1]||
AITD-00580|Expectation|期望||[1]||
AITD-00581|Expectation Maximization Algorithm|期望极大算法||[1]||
AITD-00582|Expectation Maximization|期望最大化|EM|[1]||
AITD-00583|Expectation Step|E步||[1]||
AITD-00584|Expected Error|期望错误||[1]||
AITD-00585|Expected Loss|期望损失||[1]||
AITD-00586|Expected Return|期望回报||[1]||
AITD-00587|Expected Risk|期望风险||[1]||
AITD-00588|Expected Value|期望值||[1]||
AITD-00589|Experience|经验||[1]||
AITD-00590|Experience Replay|经验回放||[1]||
AITD-00591|Expert Network|专家网络||[1]||
AITD-00592|Expert System|专家系统||[1]||
AITD-00593|Explaining Away|相消解释||[1]||
AITD-00594|Explaining Away Effect|相消解释作用||[1]||
AITD-00595|Explanatory Factort|解释因子||[1]||
AITD-00596|Explicit Density Model|显式密度模型||[1]||
AITD-00597|Exploding Gradient|梯度爆炸||[1]||
AITD-00598|Exploitation|利用||[1]||
AITD-00599|Exploration|探索||[1]||
AITD-00600|Exploration-Exploitation Dilemma|探索-利用窘境||[1]||
AITD-00601|Exponential Decay|指数衰减||[1]||
AITD-00602|Exponential Distribution|指数分布||[1]||
AITD-00603|Exponential Linear Unit|指数线性单元|ELU|[1]||
AITD-00604|Exponential Loss|指数损失||[1]||
AITD-00605|Exponential Loss Function|指数损失函数||[1]||
AITD-00606|Exponentially Weighted Moving Average|指数加权移动平均||[1]||
AITD-00607|Exposure Bias|曝光偏差||[1]||
AITD-00608|External Memory|外部记忆||[1]||
AITD-00609|Extreme Learning Machine|超限学习机|ELM|[[1]](https://www.jiqizhixin.com/articles/2016-09-30-3)||
AITD-00610|F Measure|F值||[1]||
AITD-00611|F-Score|F分数||[1]||
AITD-00612|Factor|因子||[1]||
AITD-00613|Factor Analysis|因子分析||[1]||
AITD-00614|Factor Graph|因子图||[1]||
AITD-00615|Factor Loading|因子负荷量||[1]||
AITD-00616|Factorization|因子分解||[1]||
AITD-00617|Factorized|分解的||[1]||
AITD-00618|Factors of Variation|变差因素||[1]||
AITD-00619|False Negative|假负例||[1]||
AITD-00620|False Positive|假正例||[1]||
AITD-00621|False Positive Rate|假正例率|FPR|[1]||
AITD-00622|Fast Dropout|快速暂退法||[1]||
AITD-00623|Fast Persistent Contrastive Divergence|快速持续性对比散度||[1]||
AITD-00624|Fault-Tolerant Asynchronous Training|容错异步训练||[1]||
AITD-00625|Feasible|可行||[1]||
AITD-00626|Feature|特征||[1]||
AITD-00627|Feature Engineering|特征工程||[[1]](https://pubs.rsc.org/en/content/chapter/bk9781788017893-00136/978-1-78801-789-3)||
AITD-00628|Feature Extraction|特征抽取| |[[1]](https://pubs.rsc.org/en/content/chapter/bk9781788017893-00037/978-1-78801-789-3)| |
AITD-00629|Feature Extractor|特征提取器||[1]||
AITD-00630|Feature Function|特征函数||[1]||
AITD-00631|Feature Map|特征图||[1]||
AITD-00632|Feature Scaling Transform|特征尺度变换||[1]||
AITD-00633|Feature Selection|特征选择||[[1]](https://pubs.rsc.org/en/content/chapter/bk9781788017893-00037/978-1-78801-789-3)[[2]](https://pubs.rsc.org/en/content/chapter/bk9781788017893-00136/978-1-78801-789-3)[[3]](https://pubs.rsc.org/en/content/chapter/bk9781788017893-00206/978-1-83916-023-3)||
AITD-00634|Feature Space|特征空间||[1]||
AITD-00635|Feature Vector|特征向量||[1]||
AITD-00636|Featured Learning|特征学习||[1]||
AITD-00637|Feedback|反馈||[1]||
AITD-00638|Feedforward|前馈||[1]||
AITD-00639|Feedforward Classifier|前馈分类器||[1]||
AITD-00640|Feedforward Network|前馈网络||[1]||
AITD-00641|Feedforward Neural Network|前馈神经网络|FNN|[[1]](https://www.jiqizhixin.com/articles/2017-09-07-9)[[2]](https://pubs.rsc.org/en/content/chapter/bk9781788017893-00424/978-1-78801-789-3)[[3]](https://pubs.rsc.org/en/content/chapter/bk9781788017893-00227/978-1-78801-789-3)||
AITD-00642|Few-Shot Learning|少试学习||[1]||
AITD-00643|Fidelity|逼真度||[1]||
AITD-00644|Field Programmable Gated Array|现场可编程门阵列||[1]||
AITD-00645|Filter|滤波器||[1]||
AITD-00646|Filter Method|过滤式方法||[1]||
AITD-00647|Fine-Tuning|微调||[1]||
AITD-00648|Finite Difference|有限差分||[1]||
AITD-00649|First Layer|第一层||[1]||
AITD-00650|First-Order Method|一阶方法||[1]||
AITD-00651|First-Order Rule|一阶规则||[1]||
AITD-00652|Fisher Information Matrix|Fisher信息矩阵||[1]||
AITD-00653|Fixed Point Equation|不动点方程||[1]||
AITD-00654|Fixed-Point Arithmetic|不动点运算||[1]||
AITD-00655|Flat Minima|平坦最小值||[1]||
AITD-00656|Flip|翻转||[1]||
AITD-00657|Flipping Output|翻转法||[1]||
AITD-00658|Float-Point Arithmetic|浮点运算||[1]||
AITD-00659|Fluctuation|振荡||[1]||
AITD-00660|Focus Attention|聚焦式注意力||[1]||
AITD-00661|Folk Theorem|无名氏定理||[1]||
AITD-00662|Forget Gate|遗忘门||[1]||
AITD-00663|Forward|前向||[1]||
AITD-00664|Forward KL Divergence|前向KL散度||[1]||
AITD-00665|Forward Mode Accumulation|前向模式累加||[1]||
AITD-00666|Forward Propagation|前向传播/正向传播||[1]||
AITD-00667|Forward Search|前向搜索||[1]||
AITD-00668|Forward Stagewise Algorithm|前向分步算法||[1]||
AITD-00669|Forward-Backward Algorithm|前向-后向算法||[1]||
AITD-00670|Fourier Transform|傅立叶变换||[1]||
AITD-00671|Fovea|中央凹||[1]||
AITD-00672|Fractionally Strided Convolution|微步卷积||[1]||
AITD-00673|Free Energy|自由能||[1]||
AITD-00674|Frequentist|频率主义学派||[1]||
AITD-00675|Frequentist Probability|频率派概率||[1]||
AITD-00676|Frequentist Statistics|频率派统计||[1]||
AITD-00677|Frobenius Norm|Frobenius 范数||[1]||
AITD-00678|Full|全||[1]||
AITD-00679|Full Conditional Distribution|满条件分布||[1]||
AITD-00680|Full Conditional Probability|全条件概率||[1]||
AITD-00681|Full Padding|全填充||[1]||
AITD-00682|Full Singular Value Decomposition|完全奇异值分解||[1]||
AITD-00683|Full-Rank Matrix|满秩矩阵||[1]||
AITD-00684|Fully Connected Layer|全连接层||[1]||
AITD-00685|Fully Connected Neural Network|全连接神经网络|FCNN|[1]||
AITD-00686|Fully Convolutional Network|全卷积网络|FCN|[1]||
AITD-00687|Function|函数||[1]||
AITD-00688|Functional|泛函||[1]||
AITD-00689|Functional Derivative|泛函导数||[1]||
AITD-00690|Functional Margin|函数间隔||[1]||
AITD-00691|Functional Neuron|功能神经元||[1]||
AITD-00692|Gabor Function|Gabor函数||[1]||
AITD-00693|Gain Ratio|増益率||[1]||
AITD-00694|Game Payoff|博弈效用||[1]||
AITD-00695|Game Theory|博弈论||[1]||
AITD-00696|Gamma Distribution|Gamma分布||[1]||
AITD-00697|Gate|门||[1]||
AITD-00698|Gate Controlled RNN|门控循环神经网络||[1]||
AITD-00699|Gated|门控||[1]||
AITD-00700|Gated Control|门控||[1]||
AITD-00701|Gated Recurrent Net|门控循环网络|GRN|[[1]](https://www.jiqizhixin.com/articles/2017-12-24)||
AITD-00702|Gated Recurrent Unit|门控循环单元|GRU|[1]||
AITD-00703|Gated RNN|门控RNN||[1]||
AITD-00704|Gater|选通器||[1]||
AITD-00705|Gating Mechanism|门控机制||[1]||
AITD-00706|Gaussian Distribution|高斯分布||[[1]](https://pubs.rsc.org/en/content/chapter/bk9781788017893-00311/978-1-78801-789-3)||
AITD-00707|Gaussian Error Linear Unit|高斯误差线性单元|GELU|[1]||
AITD-00708|Gaussian Kernel|高斯核||[1]||
AITD-00709|Gaussian Kernel Function|高斯核函数||[[1]](https://pubs.rsc.org/en/content/chapter/bk9781788017893-00076/978-1-78801-789-3)||
AITD-00710|Gaussian Mixture Model|高斯混合模型|GMM|[1]||
AITD-00711|Gaussian Mixtures|高斯混合（模型）| |[[1]](https://pubs.rsc.org/en/content/chapter/bk9781788017893-00311/978-1-78801-789-3)| |
AITD-00712|Gaussian Output Distribution|高斯输出分布||[1]||
AITD-00713|Gaussian Process|高斯过程|GP|[[1]](https://pubs.rsc.org/en/content/chapter/bk9781788017893-00311/978-1-78801-789-3)[[2]](https://www.nature.com/articles/s41557-021-00716-z)||
AITD-00714|Gaussian Process Regression|高斯过程回归|GPR|[[1]](https://pubs.rsc.org/en/content/chapter/bk9781788017893-00280/978-1-78801-789-3)| |
AITD-00715|Gaussian RBM|高斯RBM||[1]||
AITD-00716|Gaussian-Bernoulli RBM|高斯-伯努利RBM||[1]||
AITD-00717|General Problem Solving|通用问题求解||[1]||
AITD-00718|General Purpose GPU|通用GPU||[1]||
AITD-00719|Generalization Ability|泛化能力||[1]||
AITD-00720|Generalization Error|泛化误差||[1]||
AITD-00721|Generalization Error Bound|泛化误差上界||[1]||
AITD-00722|Generalize|泛化||[[1]](https://www.jiqizhixin.com/articles/2017-12-25-10)||
AITD-00723|Generalized Bregman Divergence|一般化 Bregman 散度||[1]||
AITD-00724|Generalized Expectation Maximization|广义期望极大|GEM|[1]||
AITD-00725|Generalized Function|广义函数||[1]||
AITD-00726|Generalized Lagrange Function|广义拉格朗日函数||[1]||
AITD-00727|Generalized Lagrangian|广义拉格朗日||[1]||
AITD-00728|Generalized Linear Model|广义线性模型||[1]||
AITD-00729|Generalized Pseudolikelihood|广义伪似然||[1]||
AITD-00730|Generalized Pseudolikelihood Estimator|广义伪似然估计||[1]||
AITD-00731|Generalized Rayleigh Quotient|广义瑞利商||[1]||
AITD-00732|Generalized Score Matching|广义得分匹配||[1]||
AITD-00733|Generative Adversarial Framework|生成式对抗框架||[1]||
AITD-00734|Generative Adversarial Network|生成对抗网络||[[1]](https://www.jiqizhixin.com/articles/2017-12-26-4)[[2]](https://www.jiqizhixin.com/articles/2018-01-08-5)[[3]](https://www.jiqizhixin.com/articles/2017-12-13-2)||
AITD-00735|Generative Approach|生成方法||[1]||
AITD-00736|Generative Model|生成式模型||[[1]](https://www.jiqizhixin.com/articles/2017-12-19-7)[[2]](https://www.jiqizhixin.com/articles/2017-12-11-6)[[3]](https://www.jiqizhixin.com/articles/2017-12-04-5)||
AITD-00737|Generative Modeling|生成式建模| |[[1]](https://pubs.rsc.org/en/content/chapter/bk9781788017893-00372/978-1-78801-789-3)|机器学习|
AITD-00738|Generative Moment Matching Network|生成矩匹配网络||[1]||
AITD-00739|Generative Pre-Training|生成式预训练|GPT|[1]||
AITD-00740|Generative Stochastic Network|生成随机网络||[1]||
AITD-00741|Generative Weight|生成权重||[1]||
AITD-00742|Generator|生成器||[1]||
AITD-00743|Generator Network|生成器网络||[1]||
AITD-00744|Genetic Algorithm|遗传算法|GA|[[1]](https://www.jiqizhixin.com/articles/2018-01-17-3)[[2]](https://www.jiqizhixin.com/articles/2017-12-22)[[3]](https://www.jiqizhixin.com/articles/2017-11-12-2)[[4]](https://pubs.rsc.org/en/content/chapter/bk9781788017893-00109/978-1-78801-789-3)[[5]](https://pubs.rsc.org/en/content/chapter/bk9781788017893-00488/978-1-78801-789-3)[[6]](https://pubs.rsc.org/en/content/chapter/bk9781788017893-00280/978-1-78801-789-3)|机器学习|
AITD-00745|Geometric Margin|几何间隔||[1]||
AITD-00746|Giant Magnetoresistance|巨磁阻||[1]||
AITD-00747|Gibbs Distribution|吉布斯分布||[1]||
AITD-00748|Gibbs Sampling|吉布斯采样/吉布斯抽样||[1]||
AITD-00749|Gibbs Steps|吉布斯步数||[1]||
AITD-00750|Gini Index|基尼指数||[1]||
AITD-00751|Global Contrast Normalization|全局对比度规范化||[1]||
AITD-00752|Global Markov Property|全局马尔可夫性||[1]||
AITD-00753|Global Minima|全局极小值||[1]||
AITD-00754|Global Minimizer|全局极小解||[1]||
AITD-00755|Global Minimum|全局最小||[1]||
AITD-00756|Global Optimization|全局优化||[[1]](https://www.jiqizhixin.com/articles/2018-01-03-3)||
AITD-00757|Gradient|梯度||[1]||
AITD-00758|Gradient Ascent|梯度上升||[1]||
AITD-00759|Gradient Ascent Method|梯度上升法||[1]||
AITD-00760|Gradient Boosting|梯度提升||[1]||
AITD-00761|Gradient Boosting Tree|梯度提升树||[1]||
AITD-00762|Gradient Clipping|梯度截断||[1]||
AITD-00763|Gradient Descent|梯度下降||[[1]](https://pubs.rsc.org/en/content/chapter/bk9781788017893-00016/978-1-78801-789-3)[[2]](https://pubs.rsc.org/en/content/chapter/bk9781788017893-00076/978-1-78801-789-3)[[3]](https://pubs.rsc.org/en/content/chapter/bk9781788017893-00169/978-1-78801-789-3)|机器学习|
AITD-00764|Gradient Descent In One-Dimensional Space|一维梯度下降||[1]||
AITD-00765|Gradient Descent Method|梯度下降法||[1]||
AITD-00766|Gradient Energy Distribution|梯度能量分布||[1]||
AITD-00767|Gradient Estimation|梯度估计||[1]||
AITD-00768|Gradient Exploding Problem|梯度爆炸问题||[[1]](https://www.jiqizhixin.com/articles/2017-12-21-14)||
AITD-00769|Gradient Field|梯度场||[1]||
AITD-00770|Gradual Warmup|逐渐预热||[1]||
AITD-00771|Gram Matrix|Gram 矩阵||[1]||
AITD-00772|Graph|图||[1]||
AITD-00773|Graph Analytics|图分析||[1]||
AITD-00774|Graph Attention Network|图注意力网络|GAT|[1]||
AITD-00775|Graph Convolutional Network|图卷积神经网络/图卷积网络|GCN|[1]||
AITD-00776|Graph Neural Network|图神经网络|GNN|[1]||
AITD-00777|Graph Theory|图论||[[1]](https://www.jiqizhixin.com/articles/2017-04-04-4)||
AITD-00778|Graphical Model|图模型|GM|[1]||
AITD-00779|Graphics Processing Unit|图形处理器||[1]||
AITD-00780|Greedy|贪心||[1]||
AITD-00781|Greedy Algorithm|贪心算法||[1]||
AITD-00782|Greedy Layer-Wise Pretraining|贪心逐层预训练||[1]||
AITD-00783|Greedy Layer-Wise Training|贪心逐层训练||[1]||
AITD-00784|Greedy Layer-Wise Unsupervised Pretraining|贪心逐层无监督预训练||[1]||
AITD-00785|Greedy Search|贪心搜索||[1]||
AITD-00786|Greedy Supervised Pretraining|贪心监督预训练||[1]||
AITD-00787|Greedy Unsupervised Pretraining|贪心无监督预训练||[1]||
AITD-00788|Grid Search|网格搜索||[[1]](https://pubs.rsc.org/en/content/chapter/bk9781788017893-00424/978-1-78801-789-3)||
AITD-00789|Grid World|网格世界||[1]||
AITD-00790|Ground Truth|真实值||[[1]](https://pubs.rsc.org/en/content/chapter/bk9781788017893-00169/978-1-78801-789-3)||
AITD-00791|Growth Function|增长函数||[1]||
AITD-00792|Hadamard Product|Hadamard积||[1]||
AITD-00793|Hamming Distance|汉明距离||[1]||
AITD-00794|Hard Attention|硬性注意力||[1]||
AITD-00795|Hard Clustering|硬聚类||[1]||
AITD-00796|Hard Margin|硬间隔||[1]||
AITD-00797|Hard Margin Maximization|硬间隔最大化||[1]||
AITD-00798|Hard Mixture Of Experts|硬专家混合体||[1]||
AITD-00799|Hard Tanh|硬双曲正切函数||[1]||
AITD-00800|Hard Target|硬目标||[1]||
AITD-00801|Hard Voting|硬投票||[1]||
AITD-00802|Harmonic Mean|调和平均||[1]||
AITD-00803|Harmonium|簧风琴||[1]||
AITD-00804|Harmony|Harmony||[1]||
AITD-00805|Harris Chain|哈里斯链||[1]||
AITD-00806|Hausdorff Distance|豪斯多夫距离||[1]||
AITD-00807|Hebbian Rule|赫布法则||[1]||
AITD-00808|Hebbian Theory|赫布理论||[1]||
AITD-00809|Helmholtz Machine|Helmholtz机||[1]||
AITD-00810|Hesse Matrix|海赛矩阵||[1]||
AITD-00811|Hessian|Hessian||[1]||
AITD-00812|Hessian Matrix|黑塞矩阵||[1]||
AITD-00813|Heterogeneous Information Network|异质信息网络|HIN|[1]||
AITD-00814|Heteroscedastic|异方差||[1]||
AITD-00815|Hidden Dynamic Model|隐动态模型||[1]||
AITD-00816|Hidden Layer|隐藏层||[1]||
AITD-00817|Hidden Markov Model|隐马尔可夫模型|HMM|[[1]](https://www.jiqizhixin.com/articles/2017-09-21-8)||
AITD-00818|Hidden State|隐状态||[1]||
AITD-00819|Hidden Unit|隐藏单元||[1]||
AITD-00820|Hidden Variable|隐变量||[1]||
AITD-00821|Hierarchical Clustering|层次聚类||[1]||
AITD-00822|Hierarchical Reinforcement Learning|分层强化学习|HRL|[1]||
AITD-00823|Hierarchical Softmax|层序Softmax/层序软最大化||[1]||
AITD-00824|Hilbert Space|希尔伯特空间||[1]||
AITD-00825|Hill Climbing|爬山||[1]||
AITD-00826|Hinge Loss Function|合页损失函数/Hinge损失函数||[1]||
AITD-00827|Histogram Method|直方图方法||[1]||
AITD-00828|Hold-Out|留出法||[1]||
AITD-00829|Homogeneous|同质||[1]||
AITD-00830|Hopfield Network|Hopfield网络||[1]||
AITD-00831|Huffman Coding|霍夫曼编码||[1]||
AITD-00832|Hybrid Computing|混合计算||[1]||
AITD-00833|Hyperbolic Tangent Function|双曲正切函数||[1]||
AITD-00834|Hyperparameter|超参数||[[1]](https://www.jiqizhixin.com/articles/2017-08-18-5)[[2]](https://www.jiqizhixin.com/articles/2017-11-28)||
AITD-00835|Hyperparameter Optimization|超参数优化||[1]||
AITD-00836|Hyperplane|超平面| |[[1]](https://pubs.rsc.org/en/content/chapter/bk9781788017893-00037/978-1-78801-789-3)|数学|
AITD-00837|Hypothesis|假设||[1]||
AITD-00838|Hypothesis Space|假设空间||[1]||
AITD-00839|Hypothesis Test|假设检验||[1]||
AITD-00840|I.I.D. Assumption|独立同分布假设||[1]||
AITD-00841|Identically Distributed|同分布的||[1]||
AITD-00842|Identifiable|可辨认的||[1]||
AITD-00843|Identity Function|恒等函数||[1]||
AITD-00844|Identity Mapping|恒等映射||[1]||
AITD-00845|Identity Matrix|单位矩阵||[1]||
AITD-00846|Ill Conditioning|病态||[1]||
AITD-00847|Ill-Formed Problem|病态问题||[1]||
AITD-00848|Image|图像||[1]||
AITD-00849|Image Restoration|图像还原||[1]||
AITD-00850|Imitation Learning|模仿学习||[1]||
AITD-00851|Immorality|不道德||[1]||
AITD-00852|Imperfect Information|不完美信息||[[1]](https://www.jiqizhixin.com/articles/2017-11-16-4)||
AITD-00853|Implicit Density Model|隐式密度模型||[1]||
AITD-00854|Import|导入||[1]||
AITD-00855|Importance Sampling|重要性采样||[1]||
AITD-00856|Improved Iterative Scaling|改进的迭代尺度法|IIS|[1]||
AITD-00857|Incomplete-Data|不完全数据||[1]||
AITD-00858|Incremental Learning|增量学习||[1]||
AITD-00859|Indefinite Integral|不定积分||[1]||
AITD-00860|Independence|独立||[1]||
AITD-00861|Independent|相互独立的||[1]||
AITD-00862|Independent and Identically Distributed|独立同分布|I.I.D.|[1]||
AITD-00863|Independent Component Analysis|独立成分分析|ICA|[1]||
AITD-00864|Independent Subspace Analysis|独立子空间分析||[1]||
AITD-00865|Index of Matrix|索引||[1]||
AITD-00866|Indicator Function|指示函数||[1]||
AITD-00867|Individual Learner|个体学习器||[1]||
AITD-00868|Induction|归纳||[1]||
AITD-00869|Inductive Bias|归纳偏好||[[1]](https://pubs.rsc.org/en/content/chapter/bk9781788017893-00372/978-1-78801-789-3)||
AITD-00870|Inductive Learning|归纳学习||[1]||
AITD-00871|Inductive Logic Programming|归纳逻辑程序设计|ILP|[1]||
AITD-00872|Inductive Transfer Learning|归纳迁移学习||[1]||
AITD-00873|Inequality Constraint|不等式约束||[1]||
AITD-00874|Inference|推断||[[1]](https://www.jiqizhixin.com/articles/2017-12-14-6)||
AITD-00875|Infinite|无限||[1]||
AITD-00876|Infinitely Exchangeable|无限可交换||[1]||
AITD-00877|Information Divergence|信息散度||[1]||
AITD-00878|Information Entropy|信息熵||[1]||
AITD-00879|Information Gain|信息增益||[[1]](https://pubs.rsc.org/en/content/chapter/bk9781788017893-00037/978-1-78801-789-3)|统计|
AITD-00880|Information Gain Ratio|信息增益比| |[[1]](https://pubs.rsc.org/en/content/chapter/bk9781788017893-00037/978-1-78801-789-3)|统计|
AITD-00881|Information Retrieval|信息检索||[1]||
AITD-00882|Information Theory|信息论||[1]||
AITD-00883|Inner Product|内积||[1]||
AITD-00884|Input|输入||[1]||
AITD-00885|Input Distribution|输入分布||[1]||
AITD-00886|Input Gate|输入门||[1]||
AITD-00887|Input Layer|输入层||[1]||
AITD-00888|Input Space|输入空间||[1]||
AITD-00889|Insensitive Loss|不敏感损失||[1]||
AITD-00890|Instance|示例||[1]||
AITD-00891|Instance Segmentation|实例分割||[1]||
AITD-00892|Integer Linear Programming|整数线性规划|ILP|[1]||
AITD-00893|Integer Programming|整数规划||[1]||
AITD-00894|Integration|积分||[1]||
AITD-00895|Inter-Cluster Similarity|簇间相似度||[1]||
AITD-00896|Internal Covariate Shift|内部协变量偏移||[1]||
AITD-00897|Internal Node|内部结点||[1]||
AITD-00898|International Conference For Machine Learning|国际机器学习大会|ICML|[[1]](https://www.jiqizhixin.com/articles/2017-12-31)||
AITD-00899|Intervention Query|干预查询||[1]||
AITD-00900|Intra-Attention|内部注意力||[1]||
AITD-00901|Intra-Cluster Similarity|簇内相似度||[1]||
AITD-00902|Intrinsic Value|固有值||[1]||
AITD-00903|Invariance|不变性||[[1]](https://www.jiqizhixin.com/articles/2017-12-16-3)||
AITD-00904|Invariant|不变||[1]||
AITD-00905|Inverse Matrix|逆矩阵||[1]||
AITD-00906|Inverse Reinforcement Learning|逆强化学习|IRL|[1]||
AITD-00907|Inverse Resolution|逆归结||[1]||
AITD-00908|Inverse Time Decay|逆时衰减||[1]||
AITD-00909|Invert|求逆||[1]||
AITD-00910|Irreducible|不可约的||[1]||
AITD-00911|Irrelevant Feature|无关特征||[1]||
AITD-00912|Isometric Mapping|等度量映射|Isomap|[1]||
AITD-00913|Isotonic Regression|等分回归||[1]||
AITD-00914|Isotropic|各向同性||[1]||
AITD-00915|Isotropic Gaussian Distribution|各向同性高斯分布||[1]||
AITD-00916|Iteration|迭代| |[[1]](https://pubs.rsc.org/en/content/chapter/bk9781788017893-00016/978-1-78801-789-3)[[2]](https://pubs.rsc.org/en/content/chapter/bk9781788017893-00311/978-1-78801-789-3)|数学、机器学习|
AITD-00917|Iterative Dichotomiser|迭代二分器||[1]||
AITD-00918|Jacobian|雅克比||[1]||
AITD-00919|Jacobian Matrix|雅可比矩阵||[1]||
AITD-00920|Jensen Inequality|Jensen不等式||[1]||
AITD-00921|Jensen-Shannon Divergence|JS散度|JSD|[1]||
AITD-00922|Joint Probability Density Function|联合概率密度函数||[1]||
AITD-00923|Joint Probability Distribution|联合概率分布||[1]||
AITD-00924|Junction Tree Algorithm|联合树算法||[1]||
AITD-00925|K-Armed Bandit Problem|k-摇臂老虎机||[1]||
AITD-00926|K-Fold Cross Validation|k 折交叉验证|K-FOLD CV|[[1]](https://pubs.rsc.org/en/content/chapter/bk9781788017893-00169/978-1-78801-789-3)|统计|
AITD-00927|K-Means Clustering|k-均值聚类| |[[1]](https://pubs.rsc.org/en/content/chapter/bk9781788017893-00311/978-1-78801-789-3)[[2]](https://www.jiqizhixin.com/articles/2017-11-11-3)| |
AITD-00928|K-Nearest Neighbor Classifier|k-近邻分类器||[1]||
AITD-00929|K-Nearest Neighbor Method|k-近邻|K-NN|[1]|统计|
AITD-00930|Karush-Kuhn-Tucker Condition|KKT条件||[1]||
AITD-00931|Karush–Kuhn–Tucker|Karush–Kuhn–Tucker||[1]||
AITD-00932|Kd Tree|Kd 树||[1]||
AITD-00933|Kernel Density Estimation|核密度估计||[1]||
AITD-00934|Kernel Function|核函数||[1]||
AITD-00935|Kernel Machine|核机器||[1]||
AITD-00936|Kernel Matrix|核矩阵||[1]||
AITD-00937|Kernel Method|核方法||[[1]](https://pubs.rsc.org/en/content/chapter/bk9781788017893-00280/978-1-78801-789-3)|机器学习|
AITD-00938|Kernel Regression|核回归||[1]||
AITD-00939|Kernel Trick|核技巧||[[1]](https://pubs.rsc.org/en/content/chapter/bk9781788017893-00037/978-1-78801-789-3)||
AITD-00940|Kernelized|核化||[1]||
AITD-00941|Kernelized Linear Discriminant Analysis|核线性判别分析|KLDA |[1]||
AITD-00942|Kernelized PCA|核主成分分析|KPCA|[1]||
AITD-00943|Key-Value Store|键-值数据库||[1]||
AITD-00944|KL Divergence|KL散度||[1]||
AITD-00945|Knowledge|知识||[1]||
AITD-00946|Knowledge Base|知识库||[[1]](https://www.jiqizhixin.com/articles/2017-12-21-10)||
AITD-00947|Knowledge Distillation|知识蒸馏||[1]||
AITD-00948|Knowledge Engineering|知识工程||[1]||
AITD-00949|Knowledge Graph|知识图谱||[[1]](https://www.jiqizhixin.com/articles/2017-11-03-5)[[2]](https://www.jiqizhixin.com/articles/2017-11-03-24)[[3]](https://www.jiqizhixin.com/articles/2017-09-26-8)||
AITD-00950|Knowledge Representation|知识表征||[1]||
AITD-00951|Kronecker Product|Kronecker积||[1]||
AITD-00952|Krylov Method|Krylov方法||[1]||
AITD-00953|L-BFGS|L-BFGS||[1]||
AITD-00954|Label|标签/标记||[1]||
AITD-00955|Label Propagation|标记传播||[1]||
AITD-00956|Label Smoothing|标签平滑||[1]||
AITD-00957|Label Space|标记空间||[1]||
AITD-00958|Labeled|标注||[1]||
AITD-00959|Lagrange Dual Problem|拉格朗日对偶问题||[1]||
AITD-00960|Lagrange Duality|拉格朗日对偶性||[1]||
AITD-00961|Lagrange Function|拉格朗日函数||[1]||
AITD-00962|Lagrange Multiplier|拉格朗日乘子||[1]||
AITD-00963|Language Model|语言模型||[1]||
AITD-00964|Language Modeling|语言模型化||[1]||
AITD-00965|Laplace Distribution|Laplace分布||[1]||
AITD-00966|Laplace Smoothing|拉普拉斯平滑||[1]||
AITD-00967|Laplacian Correction|拉普拉斯修正||[1]||
AITD-00968|Large Learning Step|大学习步骤||[1]||
AITD-00969|Las Vegas Method|拉斯维加斯方法||[1]||
AITD-00970|Latent|潜在||[1]||
AITD-00971|Latent Dirichlet Allocation|潜在狄利克雷分配|LDA|[[1]](https://www.jiqizhixin.com/articles/2017-09-01-7)||
AITD-00972|Latent Layer|潜层||[1]||
AITD-00973|Latent Semantic Analysis|潜在语义分析|LSA|[1]||
AITD-00974|Latent Semantic Indexing|潜在语义索引|LSI|[1]||
AITD-00975|Latent Variable|潜变量/隐变量||[1]||
AITD-00976|Law of Large Numbers|大数定律||[1]||
AITD-00977|Layer|层||[1]||
AITD-00978|Layer Normalization|层规范化||[1]||
AITD-00979|Layer-Wise|逐层的||[1]||
AITD-00980|Layer-Wise Adaptive Rate Scaling|逐层适应率缩放|LARS|[1]||
AITD-00981|Layer-Wise Normalization|逐层规范化||[1]||
AITD-00982|Layer-Wise Pretraining|逐层预训练||[1]||
AITD-00983|Layer-Wise Training|逐层训练||[1]||
AITD-00984|Lazy Learning|懒惰学习||[[1]](https://pubs.rsc.org/en/content/chapter/bk9781788017893-00037/978-1-78801-789-3)||
AITD-00985|Leaf Node|叶结点||[1]||
AITD-00986|Leaky Lelu Function|泄漏线性整流函数||[1]||
AITD-00987|Leaky Relu|泄漏修正线性单元/泄漏整流线性单元||[1]||
AITD-00988|Leaky Unit|渗漏单元||[1]||
AITD-00989|Learned|学成||[1]||
AITD-00990|Learned Approximate Inference|学习近似推断||[1]||
AITD-00991|Learner|学习器||[1]||
AITD-00992|Learning|学习||[1]||
AITD-00993|Learning Algorithm|学习算法||[1]||
AITD-00994|Learning By Analogy|类比学习||[1]||
AITD-00995|Learning Rate|学习率||[1]||
AITD-00996|Learning Rate Annealing|学习率退火||[1]||
AITD-00997|Learning Rate Decay|学习率衰减||[1]||
AITD-00998|Learning Rate Warmup|学习率预热||[1]||
AITD-00999|Learning To Learn|学习的学习||[1]||
AITD-01000|Learning Vector Quantization|学习向量量化|LVQ|[1]||
AITD-01001|Least General Generalization|最小一般泛化||[1]||
AITD-01002|Least Mean Squares|最小均方|LMS|[1]||
AITD-01003|Least Square Method|最小二乘法|LSM|[[1]](https://www.jiqizhixin.com/articles/2017-09-24-5)||
AITD-01004|Least Squares Regression Tree|最小二乘回归树||[1]||
AITD-01005|Leave-One-Out Cross Validation|留一交叉验证||[1]||
AITD-01006|Leave-One-Out|留一法|LOO|[1]||
AITD-01007|Lebesgue-Integrable|勒贝格可积||[1]||
AITD-01008|Left Eigenvector|左特征向量||[1]||
AITD-01009|Left Singular Vector|左奇异向量||[1]||
AITD-01010|Leibniz's Rule|莱布尼兹法则||[1]||
AITD-01011|Lifelong Learning|终身学习||[1]||
AITD-01012|Likelihood|似然||[1]||
AITD-01013|Line Search|线搜索||[1]||
AITD-01014|Linear Auto-Regressive Network|线性自回归网络||[1]||
AITD-01015|Linear Chain|线性链||[1]||
AITD-01016|Linear Chain Conditional Random Field|线性链条件随机场||[1]||
AITD-01017|Linear Classification Model|线性分类模型||[1]||
AITD-01018|Linear Classifier|线性分类器||[1]||
AITD-01019|Linear Combination|线性组合| |[[1]](https://pubs.rsc.org/en/content/chapter/bk9781788017893-00016/978-1-78801-789-3)|数学|
AITD-01020|Linear Dependence|线性相关||[1]||
AITD-01021|Linear Discriminant Analysis|线性判别分析|LDA|[[1]](https://pubs.rsc.org/en/content/chapter/bk9781788017893-00450/978-1-78801-789-3)|统计、机器学习|
AITD-01022|Linear Factor Model|线性因子模型||[1]||
AITD-01023|Linear Mapping|线性映射||[1]||
AITD-01024|Linear Model|线性模型|LR|[[1]](https://www.nature.com/articles/s41557-021-00716-z)[[2]](https://pubs.rsc.org/en/content/chapter/bk9781788017893-00076/978-1-78801-789-3)|统计、机器学习|
AITD-01025|Linear Programming|线性规划||[1]||
AITD-01026|Linear Regression|线性回归||[[1]](https://www.jiqizhixin.com/articles/2018-01-01)[[2]](https://www.jiqizhixin.com/articles/2017-11-17-5)[[3]](https://pubs.rsc.org/en/content/chapter/bk9781788017893-00037/978-1-78801-789-3)|统计、数学|
AITD-01027|Linear Scaling Rule|线性缩放规则||[1]||
AITD-01028|Linear Scan|线性扫描||[1]||
AITD-01029|Linear Space|线性空间||[1]||
AITD-01030|Linear Support Vector Machine|线性支持向量机||[1]||
AITD-01031|Linear Support Vector Machine In Linearly Separable Case|线性可分支持向量机||[1]||
AITD-01032|Linear Threshold Units|线性阈值单元||[1]||
AITD-01033|Linear Transformation|线性变换||[1]||
AITD-01034|Linearly Independent|线性无关||[1]||
AITD-01035|Linearly Separable|线性可分||[1]||
AITD-01036|Linearly Separable Data Set|线性可分数据集||[1]||
AITD-01037|Link Analysis|链接分析||[1]||
AITD-01038|Link Function|联系函数||[1]||
AITD-01039|Link Prediction|链接预测||[1]||
AITD-01040|Link Table|连接表||[1]||
AITD-01041|Linkage|连接||[1]||
AITD-01042|Linked Importance Sampling|链接重要采样||[1]||
AITD-01043|Lipschitz|Lipschitz||[1]||
AITD-01044|Lipschitz Constant|Lipschitz常数||[1]||
AITD-01045|Lipschitz Continuous|Lipschitz连续||[1]||
AITD-01046|Liquid State Machine|流体状态机||[1]||
AITD-01047|Local Conditional Probability Distribution|局部条件概率分布||[1]||
AITD-01048|Local Constancy Prior|局部不变性先验||[1]||
AITD-01049|Local Contrast Normalization|局部对比度规范化||[1]||
AITD-01050|Local Curvature|局部曲率||[1]||
AITD-01051|Local Descent|局部下降||[1]||
AITD-01052|Local Invariances|局部不变性||[1]||
AITD-01053|Local Kernel|局部核||[1]||
AITD-01054|Local Markov Property|局部马尔可夫性||[1]||
AITD-01055|Local Maxima|局部极大值||[1]||
AITD-01056|Local Maximum|局部极大点||[1]||
AITD-01057|Local Minima|局部极小||[1]||
AITD-01058|Local Minimizer|局部最小解||[1]||
AITD-01059|Local Minimum|局部极小||[1]||
AITD-01060|Local Representation|局部式表示/局部式表征||[1]||
AITD-01061|Local Response Normalization|局部响应规范化|LRN|[1]||
AITD-01062|Locally Linear Embedding|局部线性嵌入|LLE|[1]||
AITD-01063|Log Likelihood|对数似然函数||[1]||
AITD-01064|Log Linear Model|对数线性模型||[1]||
AITD-01065|Log-Likelihood|对数似然||[1]||
AITD-01066|Log-Likelihood Loss Function|对数似然损失函数||[1]||
AITD-01067|Log-Linear Regression|对数线性回归||[1]||
AITD-01068|Logarithmic Loss Function|对数损失函数||[1]||
AITD-01069|Logarithmic Scale|对数尺度||[1]||
AITD-01070|Logistic Distribution|对数几率分布||[1]||
AITD-01071|Logistic Function|对数几率函数||[[1]](https://pubs.rsc.org/en/content/chapter/bk9781788017893-00398/978-1-78801-789-3)||
AITD-01072|Logistic Loss|对率损失||[1]||
AITD-01073|Logistic Regression|对数几率回归|LR|[[1]](https://www.jiqizhixin.com/articles/2017-11-23-6)[[2]](https://pubs.rsc.org/en/content/chapter/bk9781788017893-00372/978-1-78801-789-3)|统计、机器学习|
AITD-01074|Logistic Sigmoid|对数几率Sigmoid||[1]||
AITD-01075|Logit|对数几率||[1]||
AITD-01076|Long Short Term Memory|长短期记忆|LSTM|[[1]](https://www.jiqizhixin.com/articles/2017-12-18-6)[[2]](https://www.jiqizhixin.com/articles/2017-10-04-2)[[3]](https://www.jiqizhixin.com/articles/2017-09-29-7)[[4]](https://pubs.rsc.org/en/content/chapter/bk9781788017893-00001/978-1-78801-789-3)||
AITD-01077|Long Short-Term Memory Network|长短期记忆网络|LSTM|[1]||
AITD-01078|Long-Term Dependencies Problem|长程依赖问题||[1]||
AITD-01079|Long-Term Dependency|长期依赖||[1]||
AITD-01080|Long-Term Memory|长期记忆||[1]||
AITD-01081|Loop|环||[1]||
AITD-01082|Loopy Belief Propagation|环状信念传播|LBP|[1]||
AITD-01083|Loss|损失||[1]||
AITD-01084|Loss Function|损失函数||[[1]](https://www.jiqizhixin.com/articles/2018-01-03-4)[[2]](https://pubs.rsc.org/en/content/chapter/bk9781788017893-00076/978-1-78801-789-3)|机器学习|
AITD-01085|Low Rank Matrix Approximation|低秩矩阵近似||[1]||
AITD-01086|Lp Distance|Lp距离||[1]||
AITD-01087|Machine Learning Model|机器学习模型||[1]||
AITD-01088|Machine Learning|机器学习|ML|[[1]](https://www.nature.com/articles/s41557-021-00716-z)|机器学习|
AITD-01089|Machine Translation|机器翻译|MT|[[1]](https://www.jiqizhixin.com/articles/2018-01-13-5)||
AITD-01090|Macro Average|宏平均||[1]||
AITD-01091|Macro-F1|宏F1||[1]||
AITD-01092|Macro-P|宏查准率||[1]||
AITD-01093|Macron-R|宏查全率||[1]||
AITD-01094|Mahalanobis Distance|马哈拉诺比斯距离||[1]||
AITD-01095|Main Diagonal|主对角线||[1]||
AITD-01096|Majority Voting|绝对多数投票||[1]||
AITD-01097|Majority Voting Rule|多数表决规则||[1]||
AITD-01098|Manhattan Distance|曼哈顿距离||[1]||
AITD-01099|Manifold|流形||[1]||
AITD-01100|Manifold Assumption|流形假设||[1]||
AITD-01101|Manifold Learning|流形学习||[1]||
AITD-01102|Manifold Tangent Classifier|流形正切分类器||[1]||
AITD-01103|Margin|间隔||[[1]](https://pubs.rsc.org/en/content/chapter/bk9781788017893-00169/978-1-78801-789-3)|统计|
AITD-01104|Margin Theory|间隔理论||[1]||
AITD-01105|Marginal Distribution|边缘分布||[1]||
AITD-01106|Marginal Independence|边缘独立性||[1]||
AITD-01107|Marginal Likelihood|边缘似然函数||[1]||
AITD-01108|Marginal Probability Distribution|边缘概率分布||[1]||
AITD-01109|Marginalization|边缘化||[1]||
AITD-01110|Markov Blanket|马尔可夫毯||[1]||
AITD-01111|Markov Chain|马尔可夫链||[1]||
AITD-01112|Markov Chain Monte Carlo|马尔可夫链蒙特卡罗|MCMC|[[1]](https://www.jiqizhixin.com/articles/2017-12-24-6)||
AITD-01113|Markov Decision Process|马尔可夫决策过程|MDP|[1]||
AITD-01114|Markov Network|马尔可夫网络||[1]||
AITD-01115|Markov Process|马尔可夫过程||[1]||
AITD-01116|Markov Property|马尔可夫性质||[1]||
AITD-01117|Markov Random Field|马尔可夫随机场|MRF|[1]||
AITD-01118|Mask|掩码||[1]||
AITD-01119|Mask Language Modeling|掩码语言模型化||[1]||
AITD-01120|Masked Self-Attention|掩蔽自注意力||[1]||
AITD-01121|Mathematical Optimization|数学优化||[1]||
AITD-01122|Matrix|矩阵||[1]||
AITD-01123|Matrix Calculus|矩阵微积分||[1]||
AITD-01124|Matrix Completion|矩阵补全||[1]||
AITD-01125|Matrix Decomposition|矩阵分解||[1]||
AITD-01126|Matrix Inversion|逆矩阵||[1]||
AITD-01127|Matrix Product|矩阵乘积||[1]||
AITD-01128|Max Norm|最大范数||[1]||
AITD-01129|Max Pooling|最大汇聚||[[1]](https://www.jiqizhixin.com/articles/2017-10-02-5)||
AITD-01130|Maxima|极大值||[1]||
AITD-01131|Maximal Clique|最大团||[1]||
AITD-01132|Maximization|极大||[1]||
AITD-01133|Maximization Step|M步||[1]||
AITD-01134|Maximization-Maximization Algorithm|极大-极大算法||[1]||
AITD-01135|Maximum A Posteriori|最大后验||[1]||
AITD-01136|Maximum A Posteriori Estimation|最大后验估计|MAP|[1]||
AITD-01137|Maximum Entropy Model|最大熵模型||[1]||
AITD-01138|Maximum Likelihood|极大似然||[1]||
AITD-01139|Maximum Likelihood Estimation|极大似然估计|MLE|[[1]](https://www.jiqizhixin.com/articles/2018-01-09-6)||
AITD-01140|Maximum Likelihood Method|极大似然法||[1]||
AITD-01141|Maximum Margin|最大间隔||[1]||
AITD-01142|Maximum Mean Discrepancy|最大平均偏差||[1]||
AITD-01143|Maximum Posterior Probability Estimation|最大后验概率估计|MAP|[1]||
AITD-01144|Maximum Weighted Spanning Tree|最大带权生成树||[1]||
AITD-01145|Maxout|Maxout||[1]||
AITD-01146|Maxout Unit|Maxout单元||[1]||
AITD-01147|Mean|均值||[1]||
AITD-01148|Mean Absolute Error|平均绝对误差||[1]||
AITD-01149|Mean And Covariance RBM|均值和协方差RBM||[1]||
AITD-01150|Mean Filed|平均场||[1]||
AITD-01151|Mean Filter|均值滤波||[1]||
AITD-01152|Mean Pooling|平均汇聚||[1]||
AITD-01153|Mean Product of Student t-Distribution|学生 t 分布均值乘积||[1]||
AITD-01154|Mean Squared Error|均方误差||[1]||
AITD-01155|Mean-Covariance Restricted Boltzmann Machine|均值-协方差受限玻尔兹曼机||[1]||
AITD-01156|Mean-Field|平均场||[1]||
AITD-01157|Meanfield|均匀场||[1]||
AITD-01158|Measure Theory|测度论||[1]||
AITD-01159|Measure Zero|零测度||[1]||
AITD-01160|Median|中位数||[1]||
AITD-01161|Memory|记忆||[1]||
AITD-01162|Memory Augmented Neural Network|记忆增强神经网络|MANN|[1]||
AITD-01163|Memory Capacity|记忆容量||[1]||
AITD-01164|Memory Cell|记忆元||[1]||
AITD-01165|Memory Network|记忆网络|MN|[1]||
AITD-01166|Memory Segment|记忆片段||[1]||
AITD-01167|Mercer Kernel|Mercer 核||[1]||
AITD-01168|Message|消息||[1]||
AITD-01169|Message Passing|消息传递||[1]||
AITD-01170|Message Passing Neural Network|消息传递神经网络|MPNN|[1]||
AITD-01171|Meta-Learner|元学习器||[1]||
AITD-01172|Meta-Learning|元学习| |[[1]](https://pubs.rsc.org/en/content/chapter/bk9781788017893-00311/978-1-78801-789-3)| |
AITD-01173|Meta-Optimization|元优化||[1]||
AITD-01174|Meta-Rule|元规则||[1]||
AITD-01175|Metric|指标| |[[1]](https://www.nature.com/articles/s41557-021-00716-z)[[2]](https://pubs.rsc.org/en/content/chapter/bk9781788017893-00016/978-1-78801-789-3)| |
AITD-01176|Metric Learning|度量学习||[1]||
AITD-01177|Micro Average|微平均||[1]||
AITD-01178|Micro-F1|微F1||[1]||
AITD-01179|Micro-P|微査准率||[1]||
AITD-01180|Micro-R|微查全率||[1]||
AITD-01181|Min-Max Normalization|最小最大值规范化||[1]||
AITD-01182|Mini-Batch Gradient|小批量梯度||[1]||
AITD-01183|Mini-Batch Gradient Descent|小批量梯度下降法||[1]||
AITD-01184|Mini-Batch SGD|小批次随机梯度下降||[1]||
AITD-01185|Minibatch|小批量||[1]||
AITD-01186|Minibatch Stochastic|小批量随机||[1]||
AITD-01187|Minima|极小值||[1]||
AITD-01188|Minimal Description Length|最小描述长度|MDL|[1]||
AITD-01189|Minimax Game|极小极大博弈||[1]||
AITD-01190|Minimum|极小点||[1]||
AITD-01191|Minkowski Distance|闵可夫斯基距离||[1]||
AITD-01192|Misclassification Cost|误分类代价||[1]||
AITD-01193|Mixing|混合||[1]||
AITD-01194|Mixing Time|混合时间||[1]||
AITD-01195|Mixture Density Network|混合密度网络||[1]||
AITD-01196|Mixture Distribution|混合分布||[1]||
AITD-01197|Mixture of Experts|混合专家模型||[1]||
AITD-01198|Mixture-of-Gaussian|高斯混合||[1]||
AITD-01199|Modality|模态||[1]||
AITD-01200|Mode|峰值||[1]||
AITD-01201|Model|模型||[1]||
AITD-01202|Model Averaging|模型平均||[1]||
AITD-01203|Model Collapse|模型坍塌||[1]||
AITD-01204|Model Complexity|模型复杂度||[1]||
AITD-01205|Model Compression|模型压缩||[1]||
AITD-01206|Model Identifiability|模型可辨识性||[1]||
AITD-01207|Model Parallelism|模型并行||[1]||
AITD-01208|Model Parameter|模型参数||[1]||
AITD-01209|Model Predictive Control|模型预测控制|MPC|[[1]](https://pubs.rsc.org/en/content/chapter/bk9781788017893-00340/978-1-78801-789-3)| |
AITD-01210|Model Selection|模型选择||[[1]](https://www.nature.com/articles/s41557-021-00716-z)||
AITD-01211|Model-Agnostic Meta-Learning|模型无关的元学习|MAML|[1]||
AITD-01212|Model-Based Learning|有模型学习||[1]||
AITD-01213|Model-Based Reinforcement Learning|基于模型的强化学习||[1]||
AITD-01214|Model-Free Learning|免模型学习||[1]||
AITD-01215|Model-Free Reinforcement Learning|模型无关的强化学习||[1]||
AITD-01216|Moment|矩||[1]||
AITD-01217|Moment Matching|矩匹配||[1]||
AITD-01218|Momentum|动量||[[1]](https://www.jiqizhixin.com/articles/2017-07-01-4)||
AITD-01219|Momentum Method|动量法||[1]||
AITD-01220|Monte Carlo|蒙特卡罗||[1]||
AITD-01221|Monte Carlo Estimate|蒙特卡罗估计||[1]||
AITD-01222|Monte Carlo Integration|蒙特卡罗积分||[1]||
AITD-01223|Monte Carlo Method|蒙特卡罗方法||[1]||
AITD-01224|Moore's Law|摩尔定律||[1]||
AITD-01225|Moore-Penrose Pseudoinverse|Moore-Penrose 伪逆||[1]||
AITD-01226|Moral Graph|端正图/道德图||[1]||
AITD-01227|Moralization|道德化||[1]||
AITD-01228|Most General Unifier|最一般合一置换||[1]||
AITD-01229|Moving Average|移动平均|MA|[1]||
AITD-01230|Multi-Armed Bandit Problem|多臂赌博机问题||[1]||
AITD-01231|Multi-Class Classification|多分类||[1]||
AITD-01232|Multi-Classifier System|多分类器系统||[1]||
AITD-01233|Multi-Document Summarization|多文档摘要||[1]||
AITD-01234|Multi-Head Attention|多头注意力||[1]||
AITD-01235|Multi-Head Self-Attention|多头自注意力||[1]||
AITD-01236|Multi-Hop|多跳||[1]||
AITD-01237|Multi-Kernel Learning|多核学习||[1]||
AITD-01238|Multi-Label Classification|多标签分类||[1]||
AITD-01239|Multi-Label Learning|多标记学习||[1]||
AITD-01240|Multi-Layer Feedforward Neural Networks|多层前馈神经网络||[1]||
AITD-01241|Multi-Layer Perceptron|多层感知机|MLP|[[1]](https://pubs.rsc.org/en/content/chapter/bk9781788017893-00340/978-1-78801-789-3)[[2]](https://pubs.rsc.org/en/content/chapter/bk9781788017893-00398/978-1-78801-789-3)[[3]](https://pubs.rsc.org/en/content/chapter/bk9781788017893-00372/978-1-78801-789-3)||
AITD-01242|Multi-Nominal Logistic Regression Model|多项对数几率回归模型||[1]||
AITD-01243|Multi-Prediction Deep Boltzmann Machine|多预测深度玻尔兹曼机||[1]||
AITD-01244|Multi-Response Linear Regression|多响应线性回归|MLR|[1]||
AITD-01245|Multi-View Learning|多视图学习||[1]||
AITD-01246|Multicollinearity|多重共线性||[1]||
AITD-01247|Multimodal|多峰值||[1]||
AITD-01248|Multimodal Learning|多模态学习||[1]||
AITD-01249|Multinomial Distribution|多项分布||[1]||
AITD-01250|Multinoulli Distribution|Multinoulli分布||[1]||
AITD-01251|Multinoulli Output Distribution|Multinoulli输出分布||[1]||
AITD-01252|Multiple Dimensional Scaling|多维缩放||[1]||
AITD-01253|Multiple Linear Regression|多元线性回归|MLR|[[1]](https://pubs.rsc.org/en/content/chapter/bk9781788017893-00311/978-1-78801-789-3)[[2]](https://pubs.rsc.org/en/content/chapter/bk9781788017893-00340/978-1-78801-789-3)[[3]](https://pubs.rsc.org/en/content/chapter/bk9781788017893-00206/978-1-83916-023-3)|统计|
AITD-01254|Multitask Learning|多任务学习||[1]||
AITD-01255|Multivariate Decision Tree|多变量决策树||[1]||
AITD-01256|Multivariate Gaussian Distribution|多元高斯分布||[1]||
AITD-01257|Multivariate Normal Distribution|多元正态分布||[1]||
AITD-01258|Mutual Information|互信息||[1]||
AITD-01259|N-Gram|N元||[1]||
AITD-01260|N-Gram Feature|N元特征||[1]||
AITD-01261|N-Gram Model|N元模型||[1]||
AITD-01262|Naive Bayes Algorithm|朴素贝叶斯算法||[1]||
AITD-01263|Naive Bayes Classifier|朴素贝叶斯分类器||[1]||
AITD-01264|Naive Bayes|朴素贝叶斯|NB|[[1]](https://www.jiqizhixin.com/articles/2017-11-20-6)||
AITD-01265|Named Entity Recognition|命名实体识别||[1]||
AITD-01266|Narrow Convolution|窄卷积||[1]||
AITD-01267|Nash Equilibrium|纳什均衡||[1]||
AITD-01268|Nash Reversion|纳什回归||[1]||
AITD-01269|Nats|奈特||[1]||
AITD-01270|Natural Exponential Decay|自然指数衰减||[1]||
AITD-01271|Natural Language Generation|自然语言生成|NLG|[1]||
AITD-01272|Natural Language Processing|自然语言处理|NLP|[[1]](https://pubs.rsc.org/en/content/chapter/bk9781788017893-00280/978-1-78801-789-3)[[2]](https://pubs.rsc.org/en/content/chapter/bk9781788017893-00037/978-1-78801-789-3)[[3]](https://www.nature.com/articles/s41557-021-00716-z)[[4]](https://pubs.rsc.org/en/content/chapter/bk9781788017893-00001/978-1-78801-789-3)[[5]](https://www.jiqizhixin.com/articles/2017-12-14-5)[[6]](https://www.jiqizhixin.com/articles/2017-11-14-4)[[7]](https://www.jiqizhixin.com/articles/2017-11-12-3)|机器学习|
AITD-01273|Nearest Neighbor|最近邻||[1]||
AITD-01274|Nearest Neighbor Classifier|最近邻分类器||[1]||
AITD-01275|Nearest Neighbor Graph|最近邻图||[1]||
AITD-01276|Nearest Neighbor Regression|最近邻回归||[1]||
AITD-01277|Nearest-Neighbor Search|最近邻搜索||[[1]](https://www.jiqizhixin.com/articles/2018-01-24-3)||
AITD-01278|Negative Class|负类||[1]||
AITD-01279|Negative Correlation|负相关法||[1]||
AITD-01280|Negative Definite|负定||[1]||
AITD-01281|Negative Log Likelihood|负对数似然函数||[1]||
AITD-01282|Negative Part Function|负部函数||[1]||
AITD-01283|Negative Phase|负相||[1]||
AITD-01284|Negative Sample|负例||[1]||
AITD-01285|Negative Sampling|负采样||[1]||
AITD-01286|Negative Semidefinite|半负定||[1]||
AITD-01287|Neighbourhood Component Analysis|近邻成分分析|NCA|[1]||
AITD-01288|Nesterov Accelerated Gradient|Nesterov加速梯度|NAG|[1]||
AITD-01289|Nesterov Momentum|Nesterov动量法||[1]||
AITD-01290|Net Activation|净活性值||[1]||
AITD-01291|Net Input|净输入||[1]||
AITD-01292|Network|网络||[1]||
AITD-01293|Network Capacity|网络容量||[1]||
AITD-01294|Neural Architecture Search|神经架构搜索|NAS|[1]||
AITD-01295|Neural Auto-Regressive Density Estimator|神经自回归密度估计器||[1]||
AITD-01296|Neural Auto-Regressive Network|神经自回归网络||[1]||
AITD-01297|Neural Language Model|神经语言模型||[1]||
AITD-01298|Neural Machine Translation|神经机器翻译||[[1]](https://www.jiqizhixin.com/articles/2017-08-22-6)||
AITD-01299|Neural Model|神经模型| |[[1]](https://pubs.rsc.org/en/content/chapter/bk9781788017893-00227/978-1-78801-789-3)| |
AITD-01300|Neural Network|神经网络|NN|[1]||
AITD-01301|Neural Turing Machine|神经图灵机|NTM|[[1]](https://www.jiqizhixin.com/articles/2017-04-11-7)||
AITD-01302|Neurodynamics|神经动力学||[1]||
AITD-01303|Neuromorphic Computing|神经形态计算||[[1]](https://www.jiqizhixin.com/articles/2017-09-26-4)[[2]](https://www.jiqizhixin.com/articles/2017-06-26-2)[[3]](https://www.jiqizhixin.com/articles/2017-06-16-6)||
AITD-01304|Neuron|神经元||[1]||
AITD-01305|Newton Method|牛顿法||[[1]](https://www.jiqizhixin.com/articles/2017-03-11-2)||
AITD-01306|No Free Lunch Theorem|没有免费午餐定理|NFL|[[1]](https://www.jiqizhixin.com/articles/2018-01-03-6)||
AITD-01307|Node|结点||[1]||
AITD-01308|Noise|噪声| |[[1]](https://pubs.rsc.org/en/content/chapter/bk9781788017893-00076/978-1-78801-789-3)| |
AITD-01309|Noise Distribution|噪声分布||[1]||
AITD-01310|Noise-Contrastive Estimation|噪声对比估计|NCE|[1]||
AITD-01311|Nominal Attribute|列名属性||[1]||
AITD-01312|Non-Autoregressive Process|非自回归过程||[1]||
AITD-01313|Non-Convex Optimization|非凸优化||[[1]](https://www.jiqizhixin.com/articles/2017-12-29-4)||
AITD-01314|Non-Informative Prior|无信息先验||[1]||
AITD-01315|Non-Linear Model|非线性模型||[1]||
AITD-01316|Non-Linear Oscillation|非线性振荡||[1]||
AITD-01317|Non-Linear Support Vector Machine|非线性支持向量机||[1]||
AITD-01318|Non-Metric Distance|非度量距离||[1]||
AITD-01319|Non-Negative Matrix Factorization|非负矩阵分解|NMF|[1]||
AITD-01320|Non-Ordinal Attribute|无序属性||[1]||
AITD-01321|Non-Parametric|非参数| |[[1]](https://pubs.rsc.org/en/content/chapter/bk9781788017893-00037/978-1-78801-789-3)| |
AITD-01322|Non-Parametric Model|非参数化模型||[1]||
AITD-01323|Non-Probabilistic Model|非概率模型||[1]||
AITD-01324|Non-Saturating Game|非饱和博弈||[1]||
AITD-01325|Non-Separable|不可分||[1]||
AITD-01326|Nonconvex|非凸||[1]||
AITD-01327|Nondistributed|非分布式||[1]||
AITD-01328|Nondistributed Representation|非分布式表示||[1]||
AITD-01329|Nonlinear Autoregressive With Exogenous Inputs Model|有外部输入的非线性自回归模型|NARX|[1]||
AITD-01330|Nonlinear Conjugate Gradients|非线性共轭梯度||[1]||
AITD-01331|Nonlinear Independent Components Estimation|非线性独立成分估计||[1]||
AITD-01332|Nonlinear Programming|非线性规划||[1]||
AITD-01333|Nonparametric Density Estimation|非参数密度估计||[1]||
AITD-01334|Norm|范数||[1]||
AITD-01335|Norm-Preserving|范数保持性||[1]||
AITD-01336|Normal Distribution|正态分布||[1]||
AITD-01337|Normal Equation|正规方程||[1]||
AITD-01338|Normalization|规范化||[[1]](https://www.nature.com/articles/s41557-021-00716-z)|统计、机器学习|
AITD-01339|Normalization Factor|规范化因子||[1]||
AITD-01340|Normalized|规范化的||[1]||
AITD-01341|Normalized Initialization|标准初始化||[1]||
AITD-01342|Nuclear Norm|核范数||[1]||
AITD-01343|Null Space|零空间||[1]||
AITD-01344|Number of Epochs|轮数||[1]||
AITD-01345|Numerator Layout|分子布局||[1]||
AITD-01346|Numeric Value|数值||[1]||
AITD-01347|Numerical Attribute|数值属性||[1]||
AITD-01348|Numerical Differentiation|数值微分||[1]||
AITD-01349|Numerical Method|数值方法||[1]||
AITD-01350|Numerical Optimization|数值优化||[1]||
AITD-01351|Object Detection|目标检测||[1]||
AITD-01352|Object Recognition|对象识别||[1]||
AITD-01353|Objective|目标||[1]||
AITD-01354|Objective Function|目标函数||[[1]](https://www.jiqizhixin.com/articles/2017-12-11-5)||
AITD-01355|Oblique Decision Tree|斜决策树||[1]||
AITD-01356|Observable Variable|观测变量||[1]||
AITD-01357|Observation Sequence|观测序列||[1]||
AITD-01358|Occam's Razor|奥卡姆剃刀||[[1]](https://pubs.rsc.org/en/content/chapter/bk9781788017893-00037/978-1-78801-789-3)[[2]](https://pubs.rsc.org/en/content/chapter/bk9781788017893-00016/978-1-78801-789-3)|机器学习|
AITD-01359|Odds|几率||[1]||
AITD-01360|Off-Policy|异策略||[1]||
AITD-01361|Offline Inference|离线推断||[[1]](https://www.jiqizhixin.com/articles/2017-11-06-5)||
AITD-01362|Offset|偏移量||[1]||
AITD-01363|Offset Vector|偏移向量||[1]||
AITD-01364|On-Policy|同策略||[1]||
AITD-01365|One-Shot Learning|单试学习||[[1]](https://www.jiqizhixin.com/articles/2017-03-13-2)[[2]](https://pubs.rsc.org/en/content/chapter/bk9781788017893-00311/978-1-78801-789-3)||
AITD-01366|One-Dependent Estimator|独依赖估计|ODE|[1]||
AITD-01367|One-Hot|独热||[1]||
AITD-01368|Online|在线||[1]||
AITD-01369|Online Inference|在线推断||[1]||
AITD-01370|Online Learning|在线学习||[1]||
AITD-01371|Operation|操作||[1]||
AITD-01372|Operator|运算符||[1]||
AITD-01373|Optimal Capacity|最佳容量||[1]||
AITD-01374|Optimization|最优化||[1]||
AITD-01375|Optimization Landscape|优化地形||[1]||
AITD-01376|Optimizer|优化器||[1]||
AITD-01377|Ordered Rule|带序规则||[1]||
AITD-01378|Ordinal Attribute|有序属性||[1]||
AITD-01379|Origin|原点||[1]||
AITD-01380|Orthogonal|正交||[[1]](https://pubs.rsc.org/en/content/chapter/bk9781788017893-00037/978-1-78801-789-3)|数学|
AITD-01381|Orthogonal Initialization|正交初始化||[1]||
AITD-01382|Orthogonal Matrix|正交矩阵||[1]||
AITD-01383|Orthonormal|标准正交||[1]||
AITD-01384|Out-Of-Bag Estimate|包外估计||[1]||
AITD-01385|Outer Product|外积||[1]||
AITD-01386|Outlier|异常点||[[1]](https://pubs.rsc.org/en/content/chapter/bk9781788017893-00037/978-1-78801-789-3)[[2]](https://www.nature.com/articles/s41557-021-00716-z)||
AITD-01387|Output|输出||[1]||
AITD-01388|Output Gate|输出门||[1]||
AITD-01389|Output Layer|输出层| |[[1]](https://pubs.rsc.org/en/content/chapter/bk9781788017893-00016/978-1-78801-789-3)|机器学习|
AITD-01390|Output Smearing|输出调制法||[1]||
AITD-01391|Output Space|输出空间||[1]||
AITD-01392|Over-Parameterized|过度参数化||[1]||
AITD-01393|Overcomplete|过完备||[1]||
AITD-01394|Overestimation|过估计||[1]||
AITD-01395|Overfitting|过拟合||[[1]](https://www.nature.com/articles/s41557-021-00716-z)[[2]](https://pubs.rsc.org/en/content/chapter/bk9781788017893-00016/978-1-78801-789-3)[[3]](https://pubs.rsc.org/en/content/chapter/bk9781788017893-00037/978-1-78801-789-3)|机器学习|
AITD-01396|Overfitting Regime|过拟合机制||[1]||
AITD-01397|Overflow|上溢||[1]||
AITD-01398|Oversampling|过采样||[1]||
AITD-01399|PAC Learning|PAC学习||[1]||
AITD-01400|Pac-Learnable|PAC可学习||[1]||
AITD-01401|Padding|填充||[1]||
AITD-01402|Paired t -Test|成对 t 检验||[1]||
AITD-01403|Pairwise|成对型||[1]||
AITD-01404|Pairwise Markov Property|成对马尔可夫性||[1]||
AITD-01405|Parallel Distributed Processing|分布式并行处理|PDP|[1]||
AITD-01406|Parallel Tempering|并行回火||[1]||
AITD-01407|Parameter|参数||[1]||
AITD-01408|Parameter Estimation|参数估计||[1]||
AITD-01409|Parameter Server|参数服务器||[1]||
AITD-01410|Parameter Sharing|参数共享||[1]||
AITD-01411|Parameter Space|参数空间||[1]||
AITD-01412|Parameter Tuning|调参||[[1]](https://www.jiqizhixin.com/articles/2018-01-03-3)[[2]](https://pubs.rsc.org/en/content/chapter/bk9781788017893-00227/978-1-78801-789-3)|机器学习|
AITD-01413|Parametric Case|有参情况||[1]||
AITD-01414|Parametric Density Estimation|参数密度估计||[1]||
AITD-01415|Parametric Model|参数化模型||[1]||
AITD-01416|Parametric ReLU|参数化修正线性单元/参数化整流线性单元|PReLU|[1]||
AITD-01417|Parse Tree|解析树| |[[1]](https://pubs.rsc.org/en/content/chapter/bk9781788017893-00001/978-1-78801-789-3)| |
AITD-01418|Part-Of-Speech Tagging|词性标注||[1]||
AITD-01419|Partial Derivative|偏导数||[1]||
AITD-01420|Partially Observable Markov Decision Processes|部分可观测马尔可夫决策过程|POMDP|[1]||
AITD-01421|Particle Swarm Optimization|粒子群优化算法|PSO|[[1]](https://pubs.rsc.org/en/content/chapter/bk9781788017893-00311/978-1-78801-789-3)| |
AITD-01422|Partition|划分||[1]||
AITD-01423|Partition Function|配分函数||[1]||
AITD-01424|Path|路径||[1]||
AITD-01425|Pattern|模式||[1]||
AITD-01426|Pattern Recognition|模式识别|PR|[[1]](https://pubs.rsc.org/en/content/chapter/bk9781788017893-00001/978-1-78801-789-3)[[2]](https://pubs.rsc.org/en/content/chapter/bk9781788017893-00488/978-1-78801-789-3)| |
AITD-01427|Penalty Term|罚项||[1]||
AITD-01428|Perceptron|感知机||[[1]](https://www.jiqizhixin.com/articles/2018-01-15-2)[[2]](https://pubs.rsc.org/en/content/chapter/bk9781788017893-00206/978-1-83916-023-3)|机器学习|
AITD-01429|Performance Measure|性能度量||[1]||
AITD-01430|Periodic|周期的||[1]||
AITD-01431|Permutation Invariant|置换不变性||[1]||
AITD-01432|Perplexity|困惑度||[1]||
AITD-01433|Persistent Contrastive Divergence|持续性对比散度||[1]||
AITD-01434|Phoneme|音素||[1]||
AITD-01435|Phonetic|语音||[1]||
AITD-01436|Pictorial Structure|图形结构||[1]||
AITD-01437|Piecewise|分段||[1]||
AITD-01438|Piecewise Constant Decay|分段常数衰减||[1]||
AITD-01439|Pipeline|流水线||[1]||
AITD-01440|Plate Notation|板块表示||[1]||
AITD-01441|Plug And Play Generative Network|即插即用生成网络||[1]||
AITD-01442|Plurality Voting|相对多数投票||[1]||
AITD-01443|Point Estimator|点估计||[1]||
AITD-01444|Pointer Network|指针网络||[1]||
AITD-01445|Polarity Detection|极性检测||[1]||
AITD-01446|Policy|策略||[1]||
AITD-01447|Policy Evaluation|策略评估 ||[1]||
AITD-01448|Policy Gradient|策略梯度||[1]||
AITD-01449|Policy Improvement|策略改进||[1]||
AITD-01450|Policy Iteration|策略迭代||[1]||
AITD-01451|Policy Search|策略搜索||[1]||
AITD-01452|Polynomial Basis Function|多项式基函数||[1]||
AITD-01453|Polynomial Kernel Function|多项式核函数||[1]||
AITD-01454|Polysemy|一词多义性||[1]||
AITD-01455|Pool|汇聚||[1]||
AITD-01456|Pooling|汇聚||[[1]](https://www.jiqizhixin.com/articles/2017-10-02-5)||
AITD-01457|Pooling Function|汇聚函数||[1]||
AITD-01458|Pooling Layer|汇聚层||[1]||
AITD-01459|Poor Conditioning|病态条件||[1]||
AITD-01460|Position Embedding|位置嵌入||[1]||
AITD-01461|Positional Encoding|位置编码||[1]||
AITD-01462|Positive Class|正类||[1]||
AITD-01463|Positive Definite|正定||[1]||
AITD-01464|Positive Definite Kernel Function|正定核函数||[1]||
AITD-01465|Positive Definite Matrix|正定矩阵||[1]||
AITD-01466|Positive Part Function|正部函数||[1]||
AITD-01467|Positive Phase|正相||[1]||
AITD-01468|Positive Recurrent|正常返的||[1]||
AITD-01469|Positive Sample|正例||[1]||
AITD-01470|Positive Semidefinite|半正定||[1]||
AITD-01471|Positive-Semidefinite Matrix|半正定矩阵||[1]||
AITD-01472|Post-Hoc Test|后续检验||[1]||
AITD-01473|Post-Pruning|后剪枝||[1]||
AITD-01474|Posterior Distribution|后验分布||[1]||
AITD-01475|Posterior Inference|后验推断||[1]||
AITD-01476|Posterior Probability|后验概率||[1]||
AITD-01477|Potential Function|势函数||[1]||
AITD-01478|Power Method|幂法||[1]||
AITD-01479|PR Curve|P-R曲线||[1]||
AITD-01480|Pre-Trained Initialization|预训练初始化||[1]||
AITD-01481|Pre-Training|预训练||[1]||
AITD-01482|Precision|查准率/准确率||[[1]](https://pubs.rsc.org/en/content/chapter/bk9781788017893-00016/978-1-78801-789-3)|数学、HPC|
AITD-01483|Precision Matrix|精度矩阵||[1]||
AITD-01484|Predictive Sparse Decomposition|预测稀疏分解||[1]||
AITD-01485|Prepruning|预剪枝||[1]||
AITD-01486|Pretrained Language Model|预训练语言模型||[1]||
AITD-01487|Primal Problem|主问题||[1]||
AITD-01488|Primary Visual Cortex|初级视觉皮层||[1]||
AITD-01489|Principal Component Analysis|主成分分析|PCA|[[1]](https://www.jiqizhixin.com/articles/2017-12-03-4)[[2]](https://pubs.rsc.org/en/content/chapter/bk9781788017893-00109/978-1-78801-789-3)[[3]](https://pubs.rsc.org/en/content/chapter/bk9781788017893-00037/978-1-78801-789-3)[[4]](https://pubs.rsc.org/en/content/chapter/bk9781788017893-00206/978-1-83916-023-3)||
AITD-01490|Principle Of Multiple Explanations|多释原则||[1]||
AITD-01491|Prior|先验||[1]||
AITD-01492|Prior Knowledge|先验知识| |[[1]](https://www.nature.com/articles/s41557-021-00716-z)|统计|
AITD-01493|Prior Probability|先验概率||[1]||
AITD-01494|Prior Probability Distribution|先验概率分布||[1]||
AITD-01495|Prior Pseudo-Counts|伪计数||[1]||
AITD-01496|Prior Shift|先验偏移||[1]||
AITD-01497|Priority Rule|优先级规则||[1]||
AITD-01498|Probabilistic Context-Free Grammar|概率上下文无关文法||[1]||
AITD-01499|Probabilistic Density Estimation|概率密度估计||[1]||
AITD-01500|Probabilistic Generative Model|概率生成模型||[1]||
AITD-01501|Probabilistic Graphical Model|概率图模型|PGM|[[1]](https://www.jiqizhixin.com/articles/2017-11-29-3)||
AITD-01502|Probabilistic Latent Semantic Analysis|概率潜在语义分析|PLSA|[1]||
AITD-01503|Probabilistic Latent Semantic Indexing|概率潜在语义索引|PLSI|[1]||
AITD-01504|Probabilistic Model|概率模型||[1]||
AITD-01505|Probabilistic PCA|概率PCA||[1]||
AITD-01506|Probabilistic Undirected Graphical Model|概率无向图模型||[1]||
AITD-01507|Probability|概率||[1]||
AITD-01508|Probability Density Function|概率密度函数|PDF|[1]||
AITD-01509|Probability Distribution|概率分布||[[1]](https://pubs.rsc.org/en/content/chapter/bk9781788017893-00195/978-1-83916-023-3)|统计|
AITD-01510|Probability Mass Function|概率质量函数||[1]||
AITD-01511|Probability Model Estimation|概率模型估计||[1]||
AITD-01512|Probably Approximately Correct|概率近似正确|PAC|[1]||
AITD-01513|Product of Expert|专家之积||[1]||
AITD-01514|Product Rule|乘法法则||[1]||
AITD-01515|Properly PAC Learnable|恰PAC可学习||[1]||
AITD-01516|Proportional|成比例||[1]||
AITD-01517|Proposal Distribution|提议分布||[1]||
AITD-01518|Propositional Atom|原子命题||[1]||
AITD-01519|Propositional Rule|命题规则||[1]||
AITD-01520|Prototype-Based Clustering|原型聚类||[1]||
AITD-01521|Proximal Gradient Descent|近端梯度下降|PGD|[1]||
AITD-01522|Pruning|剪枝||[[1]](https://www.jiqizhixin.com/articles/2017-09-26)||
AITD-01523|Pseudo-Label|伪标记||[1]||
AITD-01524|Pseudolikelihood|伪似然||[1]||
AITD-01525|Q Function|Q函数||[1]||
AITD-01526|Q-Learning|Q学习||[1]||
AITD-01527|Q-Network|Q网络||[1]||
AITD-01528|Quadratic Loss Function|平方损失函数||[1]||
AITD-01529|Quadratic Programming|二次规划||[1]||
AITD-01530|Quadrature Pair|象限对||[1]||
AITD-01531|Quantized Neural Network|量子化神经网络|QNN|[1]||
AITD-01532|Quantum Computer|量子计算机||[[1]](https://www.jiqizhixin.com/articles/2018-01-13)[[2]](https://www.jiqizhixin.com/articles/2017-11-30-5)[[3]](https://www.jiqizhixin.com/articles/2017-12-29-5)||
AITD-01533|Quantum Computing|量子计算||[[1]](https://www.jiqizhixin.com/articles/2018-01-13)[[2]](https://www.jiqizhixin.com/articles/2018-01-17)[[3]](https://www.jiqizhixin.com/articles/2017-12-29-5)||
AITD-01534|Quantum Machine Learning|量子机器学习||[[1]](https://www.jiqizhixin.com/articles/2017-12-04-5)||
AITD-01535|Quantum Mechanics|量子力学| |[[1]](https://pubs.rsc.org/en/content/chapter/bk9781788017893-00001/978-1-78801-789-3)|物理|
AITD-01536|Quasi Newton Method|拟牛顿法||[[1]](https://www.jiqizhixin.com/articles/2017-12-16-3)||
AITD-01537|Quasi-Concave|拟凹||[1]||
AITD-01538|Query|查询||[1]||
AITD-01539|Query Vector|查询向量||[1]||
AITD-01540|Query-Key-Value|查询-键-值|QKV|[1]||
AITD-01541|Radial Basis Function|径向基函数|RBF|[[1]](https://pubs.rsc.org/en/content/chapter/bk9781788017893-00398/978-1-78801-789-3)[[2]](https://pubs.rsc.org/en/content/chapter/bk9781788017893-00311/978-1-78801-789-3)||
AITD-01542|Random Access Memory|随机访问存储|RAM|[1]||
AITD-01543|Random Field|随机场||[1]||
AITD-01544|Random Forest Algorithm|随机森林算法||[1]||
AITD-01545|Random Forest|随机森林|RF、RFS|[[1]](https://pubs.rsc.org/en/content/chapter/bk9781788017893-00251/978-1-78801-789-3)[[2]](https://pubs.rsc.org/en/content/chapter/bk9781788017893-00037/978-1-78801-789-3)[[3]](https://pubs.rsc.org/en/content/chapter/bk9781788017893-00001/978-1-78801-789-3)[[4]](https://www.nature.com/articles/s41557-021-00716-z)[[5]](https://pubs.rsc.org/en/content/chapter/bk9781788017893-00136/978-1-78801-789-3)|统计|
AITD-01546|Random Initialization|随机初始化||[1]||
AITD-01547|Random Sampling|随机采样||[[1]](https://pubs.rsc.org/en/content/chapter/bk9781788017893-00109/978-1-78801-789-3)[[2]](https://pubs.rsc.org/en/content/chapter/bk9781788017893-00450/978-1-78801-789-3)|统计|
AITD-01548|Random Search|随机搜索||[1]||
AITD-01549|Random Subspace|随机子空间||[1]||
AITD-01550|Random Variable|随机变量||[1]||
AITD-01551|Random Walk|随机游走||[1]||
AITD-01552|Range|值域||[1]||
AITD-01553|Rank|秩||[1]||
AITD-01554|Ratio Matching|比率匹配||[1]||
AITD-01555|Raw Feature|原始特征||[1]||
AITD-01556|Re-Balance|再平衡||[1]||
AITD-01557|Re-Sampling|重采样||[1]||
AITD-01558|Re-Weighting|重赋权||[1]||
AITD-01559|Readout Function|读出函数||[1]||
AITD-01560|Real-Time Recurrent Learning|实时循环学习|RTRL|[1]||
AITD-01561|Recall|查全率/召回率||[[1]](https://pubs.rsc.org/en/content/chapter/bk9781788017893-00016/978-1-78801-789-3)||
AITD-01562|Recall-Oriented Understudy For Gisting Evaluation|ROUGE||[1]||
AITD-01563|Receiver Operating Characteristic|受试者工作特征|ROC|[[1]](https://pubs.rsc.org/en/content/chapter/bk9781788017893-00016/978-1-78801-789-3)| |
AITD-01564|Receptive Field|感受野||[1]||
AITD-01565|Recirculation|再循环||[1]||
AITD-01566|Recognition Weight|认知权重||[1]||
AITD-01567|Recommender System|推荐系统||[1]||
AITD-01568|Reconstruction|重构||[1]||
AITD-01569|Reconstruction Error|重构误差||[1]||
AITD-01570|Rectangular Diagonal Matrix|矩形对角矩阵||[1]||
AITD-01571|Rectified Linear|整流线性||[1]||
AITD-01572|Rectified Linear Transformation|整流线性变换||[1]||
AITD-01573|Rectified Linear Unit|修正线性单元/整流线性单元|ReLU|[[1]](https://www.jiqizhixin.com/articles/2017-10-21-4)[[2]](https://pubs.rsc.org/en/content/chapter/bk9781788017893-00016/978-1-78801-789-3)|CHAPTER 2|
AITD-01574|Rectifier Network|整流网络||[1]||
AITD-01575|Recurrence|循环||[1]||
AITD-01576|Recurrent Convolutional Network|循环卷积网络||[1]||
AITD-01577|Recurrent Multi-Layer Perceptron|循环多层感知器|RMLP|[1]||
AITD-01578|Recurrent Network|循环网络||[1]||
AITD-01579|Recurrent Neural Network|循环神经网络|RNN|[[1]](https://www.jiqizhixin.com/articles/2018-01-13-4)[[2]](https://www.jiqizhixin.com/articles/2018-01-05-5)[[3]](https://www.jiqizhixin.com/articles/2017-12-21-15)[[4]](https://pubs.rsc.org/en/content/chapter/bk9781788017893-00280/978-1-78801-789-3)[[5]](https://www.nature.com/articles/s41557-021-00716-z)[[6]](https://pubs.rsc.org/en/content/chapter/bk9781788017893-00169/978-1-78801-789-3)|机器学习|
AITD-01580|Recursive Neural Network|递归神经网络|RecNN|[1]||
AITD-01581|Reducible|可约的||[1]||
AITD-01582|Redundant Feature|冗余特征||[1]||
AITD-01583|Reference Model|参考模型||[1]||
AITD-01584|Region|区域||[1]||
AITD-01585|Regression|回归||[[1]](https://www.jiqizhixin.com/articles/2017-12-21-13)[[2]](https://pubs.rsc.org/en/content/chapter/bk9781788017893-00037/978-1-78801-789-3)[[3]](https://www.nature.com/articles/s41557-021-00716-z)|统计|
AITD-01586|Regularization|正则化||[[1]](https://www.jiqizhixin.com/articles/2017-12-20)||
AITD-01587|Regularizer|正则化项||[1]||
AITD-01588|Reinforcement Learning|强化学习|RL|[[1]](https://www.jiqizhixin.com/articles/2018-01-17-3)[[2]](https://www.jiqizhixin.com/articles/2017-12-28-6)[[3]](https://pubs.rsc.org/en/content/chapter/bk9781788017893-00136/978-1-78801-789-3)[[4]](https://pubs.rsc.org/en/content/chapter/bk9781788017893-00280/978-1-78801-789-3)[[5]](https://pubs.rsc.org/en/content/chapter/bk9781788017893-00340/978-1-78801-789-3)|机器学习|
AITD-01589|Rejection Sampling|拒绝采样||[1]||
AITD-01590|Relation|关系||[1]||
AITD-01591|Relational Database|关系型数据库||[1]||
AITD-01592|Relative Entropy|相对熵||[1]||
AITD-01593|Relevant Feature|相关特征||[1]||
AITD-01594|Reparameterization|再参数化/重参数化||[1]||
AITD-01595|Reparametrization Trick|重参数化技巧||[1]||
AITD-01596|Replay Buffer|经验池||[1]||
AITD-01597|Representation|表示||[1]||
AITD-01598|Representation Learning|表示学习||[[1]](https://pubs.rsc.org/en/content/chapter/bk9781788017893-00372/978-1-78801-789-3)||
AITD-01599|Representational Capacity|表示容量||[1]||
AITD-01600|Representer Theorem|表示定理||[1]||
AITD-01601|Reproducing Kernel Hilbert Space|再生核希尔伯特空间|RKHS|[1]||
AITD-01602|Rescaling|再缩放||[1]||
AITD-01603|Reservoir Computing|储层计算||[1]||
AITD-01604|Reset Gate|重置门||[1]||
AITD-01605|Residual Blocks|残差块||[1]||
AITD-01606|Residual Connection|残差连接||[1]||
AITD-01607|Residual Mapping|残差映射||[1]||
AITD-01608|Residual Network|残差网络|ResNet|[[1]](https://www.jiqizhixin.com/articles/2017-12-18-2)||
AITD-01609|Residual Unit|残差单元||[1]||
AITD-01610|Residue Function|残差函数||[1]||
AITD-01611|Resolution Quotient|归结商||[1]||
AITD-01612|Restricted Boltzmann Machine|受限玻尔兹曼机|RBM|[[1]](https://www.jiqizhixin.com/articles/2017-10-08-4)||
AITD-01613|Restricted Isometry Property|限定等距性|RIP|[1]||
AITD-01614|Return|总回报||[1]||
AITD-01615|Reverse Correlation|反向相关||[1]||
AITD-01616|Reverse KL Divergence|逆向KL散度||[1]||
AITD-01617|Reverse Mode Accumulation|反向模式累加||[1]||
AITD-01618|Reversible Markov Chain|可逆马尔可夫链||[1]||
AITD-01619|Reward|奖励||[1]||
AITD-01620|Reward Function|奖励函数||[1]||
AITD-01621|Ridge Regression|岭回归||[1]||
AITD-01622|Riemann Integral|黎曼积分||[1]||
AITD-01623|Right Eigenvector|右特征向量||[1]||
AITD-01624|Right Singular Vector|右奇异向量||[1]||
AITD-01625|Risk|风险||[1]||
AITD-01626|Risk Function|风险函数||[1]||
AITD-01627|Robustness|稳健性||[[1]](https://pubs.rsc.org/en/content/chapter/bk9781788017893-00037/978-1-78801-789-3)|计算机、机器学习|
AITD-01628|Root Node|根结点||[1]||
AITD-01629|Round-Off Error|舍入误差||[1]||
AITD-01630|Row|行||[1]||
AITD-01631|Rule Engine|规则引擎||[1]||
AITD-01632|Rule Learning|规则学习||[1]||
AITD-01633|S-Fold Cross Validation|S 折交叉验证||[1]||
AITD-01634|Saccade|扫视||[1]||
AITD-01635|Saddle Point|鞍点||[[1]](https://www.jiqizhixin.com/articles/2017-09-08)||
AITD-01636|Saddle-Free Newton Method|无鞍牛顿法||[1]||
AITD-01637|Saliency Map|显著图||[1]||
AITD-01638|Saliency-Based Attention|基于显著性的注意力||[1]||
AITD-01639|Same|相同||[1]||
AITD-01640|Sample|样本||[1]||
AITD-01641|Sample Complexity|样本复杂度||[1]||
AITD-01642|Sample Mean|样本均值||[1]||
AITD-01643|Sample Space|样本空间||[1]||
AITD-01644|Sample Variance|样本方差||[1]||
AITD-01645|Sampling|采样||[1]||
AITD-01646|Sampling Method|采样法||[1]||
AITD-01647|Saturate|饱和||[1]||
AITD-01648|Saturating Function|饱和函数||[1]||
AITD-01649|Scalar|标量||[1]||
AITD-01650|Scale Invariance|尺度不变性||[1]||
AITD-01651|Scatter Matrix|散布矩阵||[1]||
AITD-01652|Scheduled Sampling|计划采样||[1]||
AITD-01653|Score|得分||[1]||
AITD-01654|Score Function|评分函数||[1]||
AITD-01655|Score Matching|分数匹配||[1]||
AITD-01656|Second Derivative|二阶导数||[1]||
AITD-01657|Second Derivative Test|二阶导数测试||[1]||
AITD-01658|Second Layer|第二层||[1]||
AITD-01659|Second-Order Method|二阶方法||[1]||
AITD-01660|Selective Attention|选择性注意力||[1]||
AITD-01661|Selective Ensemble|选择性集成||[1]||
AITD-01662|Self Information|自信息||[1]||
AITD-01663|Self-Attention|自注意力||[1]||
AITD-01664|Self-Attention Model|自注意力模型||[1]||
AITD-01665|Self-Contrastive Estimation|自对比估计||[1]||
AITD-01666|Self-Driving|自动驾驶||[[1]](https://www.jiqizhixin.com/articles/2017-12-27-7)[[2]](https://www.jiqizhixin.com/articles/2018-01-16)[[3]](https://www.jiqizhixin.com/articles/2018-01-08-9)||
AITD-01667|Self-Gated|自门控||[1]||
AITD-01668|Self-Organizing Map|自组织映射网|SOM|[1]||
AITD-01669|Self-Taught Learning|自学习||[1]||
AITD-01670|Self-Training|自训练||[1]||
AITD-01671|Semantic Gap|语义鸿沟||[1]||
AITD-01672|Semantic Hashing|语义哈希||[1]||
AITD-01673|Semantic Segmentation|语义分割||[1]||
AITD-01674|Semantic Similarity|语义相似度||[1]||
AITD-01675|Semi-Definite Programming|半正定规划||[1]||
AITD-01676|Semi-Naive Bayes Classifiers|半朴素贝叶斯分类器||[1]||
AITD-01677|Semi-Restricted Boltzmann Machine|半受限玻尔兹曼机||[1]||
AITD-01678|Semi-Supervised|半监督||[1]||
AITD-01679|Semi-Supervised Clustering|半监督聚类||[1]||
AITD-01680|Semi-Supervised Learning|半监督学习||[[1]](https://www.jiqizhixin.com/articles/2017-12-22-3)[[2]](https://www.jiqizhixin.com/articles/2017-12-02)[[3]](https://www.jiqizhixin.com/articles/2018-01-07)||
AITD-01681|Semi-Supervised Support Vector Machine|半监督支持向量机|S3VM|[1]||
AITD-01682|Sentiment Analysis|情感分析||[[1]](https://www.jiqizhixin.com/articles/2017-12-07-7)||
AITD-01683|Separable|可分离的||[1]||
AITD-01684|Separate|分离的||[1]||
AITD-01685|Separating Hyperplane|分离超平面||[1]||
AITD-01686|Separation|分离||[1]||
AITD-01687|Sequence Labeling|序列标注||[1]||
AITD-01688|Sequence To Sequence Learning|序列到序列学习|Seq2Seq|[1]||
AITD-01689|Sequence-To-Sequence|序列到序列|Seq2Seq|[[1]](https://pubs.rsc.org/en/content/chapter/bk9781788017893-00136/978-1-78801-789-3)| |
AITD-01690|Sequential Covering|序贯覆盖||[1]||
AITD-01691|Sequential Minimal Optimization|序列最小最优化|SMO|[1]||
AITD-01692|Sequential Model-Based Optimization|时序模型优化|SMBO|[1]||
AITD-01693|Sequential Partitioning|顺序分区||[1]||
AITD-01694|Setting|情景||[1]||
AITD-01695|Shadow Circuit|浅度回路||[1]||
AITD-01696|Shallow Learning|浅层学习||[1]||
AITD-01697|Shannon Entropy|香农熵||[1]||
AITD-01698|Shannons|香农||[1]||
AITD-01699|Shaping|塑造||[1]||
AITD-01700|Sharp Minima|尖锐最小值||[1]||
AITD-01701|Shattering|打散||[1]||
AITD-01702|Shift Invariance|平移不变性||[1]||
AITD-01703|Short-Term Memory|短期记忆||[1]||
AITD-01704|Shortcut Connection|直连边||[1]||
AITD-01705|Shortlist|短列表||[1]||
AITD-01706|Siamese Network|孪生网络||[[1]](https://www.jiqizhixin.com/articles/2018-01-02-4)||
AITD-01707|Sigmoid|Sigmoid（一种激活函数）||[[1]](https://pubs.rsc.org/en/content/chapter/bk9781788017893-00016/978-1-78801-789-3)|统计|
AITD-01708|Sigmoid Belief Network|Sigmoid信念网络|SBN|[1]||
AITD-01709|Sigmoid Curve|S 形曲线||[1]||
AITD-01710|Sigmoid Function|Sigmoid函数||[[1]](https://www.jiqizhixin.com/articles/2017-11-02-26)||
AITD-01711|Sign Function|符号函数||[1]||
AITD-01712|Signed Distance|带符号距离||[1]||
AITD-01713|Similarity|相似度||[1]||
AITD-01714|Similarity Measure|相似度度量||[1]||
AITD-01715|Simple Cell|简单细胞||[1]||
AITD-01716|Simple Recurrent Network|简单循环网络|SRN|[1]||
AITD-01717|Simple Recurrent Neural Network|简单循环神经网络|S-RNN|[1]||
AITD-01718|Simplex|单纯形||[1]||
AITD-01719|Simulated Annealing|模拟退火||[[1]](https://pubs.rsc.org/en/content/chapter/bk9781788017893-00488/978-1-78801-789-3)|统计、机器学习|
AITD-01720|Simultaneous Localization And Mapping|即时定位与地图构建|SLAM|[1]||
AITD-01721|Single Component Metropolis-Hastings|单分量Metropolis-Hastings||[1]||
AITD-01722|Single Linkage|单连接||[1]||
AITD-01723|Singular|奇异的| |[[1]](https://pubs.rsc.org/en/content/chapter/bk9781788017893-00037/978-1-78801-789-3)| |
AITD-01724|Singular Value|奇异值||[1]||
AITD-01725|Singular Value Decomposition|奇异值分解|SVD|[1]||
AITD-01726|Singular Vector|奇异向量||[1]||
AITD-01727|Size|大小||[1]||
AITD-01728|Skip Connection|跳跃连接||[1]||
AITD-01729|Skip-Gram Model|跳元模型||[1]||
AITD-01730|Skip-Gram Model With Negative Sampling|跳元模型加负采样||[1]||
AITD-01731|Slack Variable|松弛变量||[1]||
AITD-01732|Slow Feature Analysis|慢特征分析||[1]||
AITD-01733|Slowness Principle|慢性原则||[1]||
AITD-01734|Smoothing|平滑||[1]||
AITD-01735|Smoothness Prior|平滑先验||[1]||
AITD-01736|Soft Attention Mechanism|软性注意力机制||[1]||
AITD-01737|Soft Clustering|软聚类||[1]||
AITD-01738|Soft Margin|软间隔||[1]||
AITD-01739|Soft Margin Maximization|软间隔最大化||[1]||
AITD-01740|Soft Target|软目标||[1]||
AITD-01741|Soft Voting|软投票||[1]||
AITD-01742|Softmax|Softmax/软最大化||[1]||
AITD-01743|Softmax Function|Softmax函数/软最大化函数||[[1]](https://pubs.rsc.org/en/content/chapter/bk9781788017893-00398/978-1-78801-789-3)|统计、机器学习|
AITD-01744|Softmax Regression|Softmax回归/软最大化回归||[1]||
AITD-01745|Softmax Unit|Softmax单元/软最大化单元||[1]||
AITD-01746|Softplus|Softplus||[1]||
AITD-01747|Softplus Function|Softplus函数||[1]||
AITD-01748|Source Domain|源领域||[1]||
AITD-01749|Span|张成子空间||[1]||
AITD-01750|Sparse|稀疏||[1]||
AITD-01751|Sparse Activation|稀疏激活||[1]||
AITD-01752|Sparse Auto-Encoder|稀疏自编码器||[1]||
AITD-01753|Sparse Coding|稀疏编码||[1]||
AITD-01754|Sparse Connectivity|稀疏连接||[1]||
AITD-01755|Sparse Initialization|稀疏初始化||[1]||
AITD-01756|Sparse Interactions|稀疏交互||[1]||
AITD-01757|Sparse Representation|稀疏表示||[1]||
AITD-01758|Sparse Weights|稀疏权重||[1]||
AITD-01759|Sparsity|稀疏性||[1]||
AITD-01760|Specialization|特化||[1]||
AITD-01761|Spectral Clustering|谱聚类||[1]||
AITD-01762|Spectral Radius|谱半径||[1]||
AITD-01763|Speech Recognition|语音识别| |[[1]](https://pubs.rsc.org/en/content/chapter/bk9781788017893-00109/978-1-78801-789-3)[[2]](https://pubs.rsc.org/en/content/chapter/bk9781788017893-00001/978-1-78801-789-3)[[3]](https://pubs.rsc.org/en/content/chapter/bk9781788017893-00037/978-1-78801-789-3)[[4]](https://www.jiqizhixin.com/articles/2018-01-01-3)[[5]](https://www.jiqizhixin.com/articles/2017-12-04)[[6]](https://www.jiqizhixin.com/articles/2017-12-15)| |
AITD-01764|Sphering|Sphering||[1]||
AITD-01765|Spike And Slab|尖峰和平板||[1]||
AITD-01766|Spike And Slab RBM|尖峰和平板RBM||[1]||
AITD-01767|Spiking Neural Nets|脉冲神经网络||[[1]](https://www.jiqizhixin.com/articles/2018-01-13-7)||
AITD-01768|Splitting Point|切分点||[1]||
AITD-01769|Splitting Variable|切分变量||[1]||
AITD-01770|Spurious Modes|虚假模态||[1]||
AITD-01771|Square|方阵||[1]||
AITD-01772|Square Loss|平方损失||[1]||
AITD-01773|Squared Euclidean Distance|欧氏距离平方||[1]||
AITD-01774|Squared Exponential|平方指数||[1]||
AITD-01775|Squashing Function|挤压函数||[1]||
AITD-01776|Stability|稳定性||[1]||
AITD-01777|Stability-Plasticity Dilemma|可塑性-稳定性窘境||[1]||
AITD-01778|Stable Base Learner|稳定基学习器||[1]||
AITD-01779|Stacked Auto-Encoder|堆叠自编码器|SAE|[1]||
AITD-01780|Stacked Deconvolutional Network|堆叠解卷积网络|SDN|[1]||
AITD-01781|Stacked Recurrent Neural Network|堆叠循环神经网络|SRNN|[1]||
AITD-01782|Standard Basis|标准基||[1]||
AITD-01783|Standard Deviation|标准差||[1]||
AITD-01784|Standard Error|标准差||[1]||
AITD-01785|Standard Normal Distribution|标准正态分布||[1]||
AITD-01786|Standardization|标准化||[1]||
AITD-01787|State|状态||[1]||
AITD-01788|State Action Reward State Action|SARSA算法|SARSA|[1]||
AITD-01789|State Sequence|状态序列||[1]||
AITD-01790|State Space|状态空间||[1]||
AITD-01791|State Value Function|状态值函数||[1]||
AITD-01792|State-Action Value Function|状态-动作值函数||[1]||
AITD-01793|Statement|声明||[1]||
AITD-01794|Static Computational Graph|静态计算图||[1]||
AITD-01795|Static Game|静态博弈||[1]||
AITD-01796|Stationary|平稳的||[1]||
AITD-01797|Stationary Distribution|平稳分布||[1]||
AITD-01798|Stationary Point|驻点||[1]||
AITD-01799|Statistic Efficiency|统计效率||[1]||
AITD-01800|Statistical Learning|统计学习||[[1]](https://www.nature.com/articles/s41557-021-00716-z)[[2]](https://pubs.rsc.org/en/content/chapter/bk9781788017893-00136/978-1-78801-789-3)||
AITD-01801|Statistical Learning Theory|统计学习理论||[1]||
AITD-01802|Statistical Machine Learning|统计机器学习||[1]||
AITD-01803|Statistical Relational Learning|统计关系学习||[1]||
AITD-01804|Statistical Simulation Method|统计模拟方法||[1]||
AITD-01805|Statistics|统计量||[1]||
AITD-01806|Status Feature Function|状态特征函数||[1]||
AITD-01807|Steepest Descent|最速下降法||[1]||
AITD-01808|Step Decay|阶梯衰减||[1]||
AITD-01809|Stochastic|随机||[1]||
AITD-01810|Stochastic Curriculum|随机课程||[1]||
AITD-01811|Stochastic Dynamical System|随机动力系统||[1]||
AITD-01812|Stochastic Gradient Ascent|随机梯度上升||[1]||
AITD-01813|Stochastic Gradient Descent|随机梯度下降||[[1]](https://www.jiqizhixin.com/articles/2017-12-25-10)||
AITD-01814|Stochastic Gradient Descent With Warm Restarts|带热重启的随机梯度下降|SGDR|[1]||
AITD-01815|Stochastic Matrix|随机矩阵||[1]||
AITD-01816|Stochastic Maximum Likelihood|随机最大似然||[1]||
AITD-01817|Stochastic Neighbor Embedding|随机近邻嵌入||[1]||
AITD-01818|Stochastic Neural Network|随机神经网络|SNN|[1]||
AITD-01819|Stochastic Policy|随机性策略||[1]||
AITD-01820|Stochastic Process|随机过程||[1]||
AITD-01821|Stop Words|停用词||[1]||
AITD-01822|Stratified Sampling|分层采样||[1]||
AITD-01823|Stream|流||[1]||
AITD-01824|Stride|步幅||[1]||
AITD-01825|String Kernel Function|字符串核函数||[1]||
AITD-01826|Strong Classifier|强分类器||[1]||
AITD-01827|Strong Duality|强对偶性||[1]||
AITD-01828|Strongly Connected Graph|强连通图||[1]||
AITD-01829|Strongly Learnable|强可学习||[1]||
AITD-01830|Structural Risk|结构风险||[1]||
AITD-01831|Structural Risk Minimization|结构风险最小化|SRM|[1]||
AITD-01832|Structure Learning|结构学习||[1]||
AITD-01833|Structured Learning|结构化学习||[1]||
AITD-01834|Structured Probabilistic Model|结构化概率模型||[1]||
AITD-01835|Structured Variational Inference|结构化变分推断||[1]||
AITD-01836|Student Network|学生网络||[1]||
AITD-01837|Sub-Optimal|次最优||[1]||
AITD-01838|Subatomic|亚原子||[1]||
AITD-01839|Subsample|子采样||[1]||
AITD-01840|Subsampling|下采样||[1]||
AITD-01841|Subsampling Layer|子采样层||[1]||
AITD-01842|Subset Evaluation|子集评价||[1]||
AITD-01843|Subset Search|子集搜索||[1]||
AITD-01844|Subspace|子空间||[1]||
AITD-01845|Substitution|置换||[1]||
AITD-01846|Successive Halving|逐次减半||[1]||
AITD-01847|Sum Rule|求和法则||[1]||
AITD-01848|Sum-Product|和积||[1]||
AITD-01849|Sum-Product Network|和-积网络||[1]||
AITD-01850|Super-Parent|超父||[1]||
AITD-01851|Supervised|监督||[1]||
AITD-01852|Supervised Learning|监督学习||[[1]](https://pubs.rsc.org/en/content/chapter/bk9781788017893-00037/978-1-78801-789-3)[[2]](https://www.nature.com/articles/s41557-021-00716-z)[[3]](https://pubs.rsc.org/en/content/chapter/bk9781788017893-00311/978-1-78801-789-3)|机器学习|
AITD-01853|Supervised Learning Algorithm|监督学习算法||[1]||
AITD-01854|Supervised Model|监督模型||[1]||
AITD-01855|Supervised Pretraining|监督预训练||[1]||
AITD-01856|Support Vector|支持向量||[[1]](https://pubs.rsc.org/en/content/chapter/bk9781788017893-00037/978-1-78801-789-3)|统计、机器学习|
AITD-01857|Support Vector Expansion|支持向量展式||[1]||
AITD-01858|Support Vector Machine|支持向量机|SVM|[[1]](https://www.jiqizhixin.com/articles/2017-10-08)[[2]](https://www.nature.com/articles/s41557-021-00716-z)[[3]](https://pubs.rsc.org/en/content/chapter/bk9781788017893-00037/978-1-78801-789-3)[[4]](https://pubs.rsc.org/en/content/chapter/bk9781788017893-00251/978-1-78801-789-3)|统计、机器学习|
AITD-01859|Support Vector Regression|支持向量回归|SVR|[[1]](https://pubs.rsc.org/en/content/chapter/bk9781788017893-00076/978-1-78801-789-3)[[2]](https://pubs.rsc.org/en/content/chapter/bk9781788017893-00450/978-1-78801-789-3)[[3]](https://pubs.rsc.org/en/content/chapter/bk9781788017893-00227/978-1-78801-789-3)|统计、机器学习|
AITD-01860|Surrogat Loss|替代损失||[1]||
AITD-01861|Surrogate Function|替代函数||[1]||
AITD-01862|Surrogate Loss Function|代理损失函数||[1]||
AITD-01863|Symbol|符号||[1]||
AITD-01864|Symbolic Differentiation|符号微分||[1]||
AITD-01865|Symbolic Learning|符号学习||[1]||
AITD-01866|Symbolic Representation|符号表示||[1]||
AITD-01867|Symbolism|符号主义||[1]||
AITD-01868|Symmetric|对称||[1]||
AITD-01869|Symmetric Matrix|对称矩阵||[1]||
AITD-01870|Synonymy|多词一义性||[1]||
AITD-01871|Synset|同义词集||[1]||
AITD-01872|Synthetic Feature|合成特征||[1]||
AITD-01873|T-Distribution Stochastic Neighbour Embedding|T分布随机近邻嵌入|T-SNE|[1]||
AITD-01874|Tabular Value Function|表格值函数||[1]||
AITD-01875|Tagging|标注||[1]||
AITD-01876|Tangent Distance|切面距离||[1]||
AITD-01877|Tangent Plane|切平面||[1]||
AITD-01878|Tangent Propagation|正切传播||[1]||
AITD-01879|Target|目标||[1]||
AITD-01880|Target Domain|目标领域||[1]||
AITD-01881|Taylor|泰勒||[1]||
AITD-01882|Taylor's Formula|泰勒公式||[1]||
AITD-01883|Teacher Forcing|强制教学||[1]||
AITD-01884|Teacher Network|教师网络||[1]||
AITD-01885|Temperature|温度||[1]||
AITD-01886|Tempered Transition|回火转移||[1]||
AITD-01887|Tempering|回火||[1]||
AITD-01888|Temporal-Difference Learning|时序差分学习||[1]||
AITD-01889|Tensor|张量||[1]||
AITD-01890|Tensor Processing Units|张量处理单元|TPU|[[1]](https://www.jiqizhixin.com/articles/2018-01-05-3)||
AITD-01891|Term Frequency-Inverse Document Frequency|单词频率-逆文本频率|TF-IDF|[1]||
AITD-01892|Terminal State|终止状态||[1]||
AITD-01893|Test Data|测试数据||[1]||
AITD-01894|Test Error|测试误差||[1]||
AITD-01895|Test Sample|测试样本||[1]||
AITD-01896|Test Set|测试集||[[1]](https://pubs.rsc.org/en/content/chapter/bk9781788017893-00037/978-1-78801-789-3)[[2]](https://www.nature.com/articles/s41557-021-00716-z)[[3]](https://pubs.rsc.org/en/content/chapter/bk9781788017893-00311/978-1-78801-789-3)|机器学习|
AITD-01897|The Collider Case|碰撞情况||[1]||
AITD-01898|Threshold|阈值||[[1]](https://pubs.rsc.org/en/content/chapter/bk9781788017893-00076/978-1-78801-789-3)|数学|
AITD-01899|Threshold Logic Unit|阈值逻辑单元||[1]||
AITD-01900|Threshold-Moving|阈值移动||[1]||
AITD-01901|Tied Weight|捆绑权重||[1]||
AITD-01902|Tikhonov Regularization|Tikhonov正则化||[1]||
AITD-01903|Tiled Convolution|平铺卷积||[1]||
AITD-01904|Time Delay Neural Network|时延神经网络|TDNN|[1]||
AITD-01905|Time Homogenous Markov Chain|时间齐次马尔可夫链||[1]||
AITD-01906|Time Step|时间步||[1]||
AITD-01907|Toeplitz Matrix|Toeplitz矩阵||[1]||
AITD-01908|Token|词元||[1]||
AITD-01909|Tokenize|词元化||[1]||
AITD-01910|Tokenization|词元化||[1]||
AITD-01911|Tokenizer|词元分析器||[1]||
AITD-01912|Tolerance|容差||[1]||
AITD-01913|Top-Down|自顶向下| |[[1]](https://pubs.rsc.org/en/content/chapter/bk9781788017893-00311/978-1-78801-789-3)| |
AITD-01914|Topic|话题||[1]||
AITD-01915|Topic Model|话题模型||[1]||
AITD-01916|Topic Modeling|话题分析||[1]||
AITD-01917|Topic Vector Space|话题向量空间||[1]||
AITD-01918|Topic Vector Space Model|话题向量空间模型||[1]||
AITD-01919|Topic-Document Matrix|话题-文本矩阵||[1]||
AITD-01920|Topographic ICA|地质ICA||[1]||
AITD-01921|Total Cost|总体代价||[1]||
AITD-01922|Trace|迹||[1]||
AITD-01923|Tractable|易处理的||[1]||
AITD-01924|Training|训练||[1]||
AITD-01925|Training Data|训练数据||[1]||
AITD-01926|Training Error|训练误差||[1]||
AITD-01927|Training Instance|训练实例||[1]||
AITD-01928|Training Sample|训练样本||[[1]](https://www.nature.com/articles/s41557-021-00716-z)|机器学习|
AITD-01929|Training Set|训练集||[[1]](https://pubs.rsc.org/en/content/chapter/bk9781788017893-00037/978-1-78801-789-3)[[2]](https://www.nature.com/articles/s41557-021-00716-z)|机器学习|
AITD-01930|Trajectory|轨迹| |[[1]](https://pubs.rsc.org/en/content/chapter/bk9781788017893-00076/978-1-78801-789-3)| |
AITD-01931|Transcribe|转录||[1]||
AITD-01932|Transcription System|转录系统||[1]||
AITD-01933|Transductive Learning|直推学习||[1]||
AITD-01934|Transductive Transfer Learning|直推迁移学习||[1]||
AITD-01935|Transfer Learning|迁移学习||[[1]](https://www.jiqizhixin.com/articles/2018-01-04-7)[[2]](https://www.nature.com/articles/s41557-021-00716-z)[[3]](https://pubs.rsc.org/en/content/chapter/bk9781788017893-00109/978-1-78801-789-3)[[4]](https://pubs.rsc.org/en/content/chapter/bk9781788017893-00280/978-1-78801-789-3)||
AITD-01936|Transform|变换||[1]||
AITD-01937|Transformer|Transformer||[1]||
AITD-01938|Transformer Model|Transformer模型||[1]||
AITD-01939|Transition|转移||[1]||
AITD-01940|Transition Kernel|转移核||[1]||
AITD-01941|Transition Matrix|状态转移矩阵||[1]||
AITD-01942|Transition Probability|转移概率||[1]||
AITD-01943|Transpose|转置||[1]||
AITD-01944|Transposed Convolution|转置卷积||[1]||
AITD-01945|Tree-Structured LSTM|树结构的长短期记忆模型||[1]||
AITD-01946|Treebank|树库||[1]||
AITD-01947|Trial|试验||[1]||
AITD-01948|Trial And Error|试错||[1]||
AITD-01949|Triangle Inequality|三角不等式||[1]||
AITD-01950|Triangular Cyclic Learning Rate|三角循环学习率||[1]||
AITD-01951|Triangulate|三角形化||[1]||
AITD-01952|Triangulated Graph|三角形化图||[1]||
AITD-01953|Trigram|三元语法||[1]||
AITD-01954|True Negative|真负例|TN|[[1]](https://pubs.rsc.org/en/content/chapter/bk9781788017893-00016/978-1-78801-789-3)|统计|
AITD-01955|True Positive|真正例|TP|[[1]](https://pubs.rsc.org/en/content/chapter/bk9781788017893-00016/978-1-78801-789-3)|统计|
AITD-01956|True Positive Rate|真正例率|TPR|[[1]](https://pubs.rsc.org/en/content/chapter/bk9781788017893-00016/978-1-78801-789-3)|统计|
AITD-01957|Truncated Singular Value Decomposition|截断奇异值分解||[1]||
AITD-01958|Truncation Error|截断误差||[1]||
AITD-01959|Turing Completeness|图灵完备||[1]||
AITD-01960|Turing Machine|图灵机||[[1]](https://www.jiqizhixin.com/articles/2017-04-11-7)||
AITD-01961|Twice-Learning|二次学习||[1]||
AITD-01962|Two-Dimensional Array|二维数组||[1]||
AITD-01963|Ugly Duckling Theorem|丑小鸭定理||[1]||
AITD-01964|Unbiased|无偏||[1]||
AITD-01965|Unbiased Estimate|无偏估计||[1]||
AITD-01966|Unbiased Sample Variance|无偏样本方差||[1]||
AITD-01967|Unconstrained Optimization|无约束优化||[1]||
AITD-01968|Undercomplete|欠完备||[1]||
AITD-01969|Underdetermined|欠定的||[1]||
AITD-01970|Underestimation|欠估计||[1]||
AITD-01971|Underfitting|欠拟合||[[1]](https://www.nature.com/articles/s41557-021-00716-z)|机器学习|
AITD-01972|Underfitting Regime|欠拟合机制||[1]||
AITD-01973|Underflow|下溢||[1]||
AITD-01974|Underlying|潜在||[1]||
AITD-01975|Underlying Cause|潜在成因||[1]||
AITD-01976|Undersampling|欠采样||[1]||
AITD-01977|Understandability|可理解性||[1]||
AITD-01978|Undirected|无向||[1]||
AITD-01979|Undirected Graph|无向图||[1]||
AITD-01980|Undirected Graphical Model|无向图模型||[1]||
AITD-01981|Undirected Model|无向模型||[1]||
AITD-01982|Unequal Cost|非均等代价||[1]||
AITD-01983|Unfolded Graph|展开图||[1]||
AITD-01984|Unfolding|展开||[1]||
AITD-01985|Unidirectional Language Model|单向语言模型||[1]||
AITD-01986|Unification|合一||[1]||
AITD-01987|Uniform Distribution|均匀分布||[1]||
AITD-01988|Uniform Sampling|均匀采样||[1]||
AITD-01989|Uniform Stability|均匀稳定性||[1]||
AITD-01990|Unigram|一元语法||[1]||
AITD-01991|Unimodal|单峰值||[1]||
AITD-01992|Unit|单元||[1]||
AITD-01993|Unit Norm|单位范数||[1]||
AITD-01994|Unit Test|单元测试||[1]||
AITD-01995|Unit Variance|单位方差||[1]||
AITD-01996|Unit Vector|单位向量||[1]||
AITD-01997|Unit-Step Function|单位阶跃函数||[1]||
AITD-01998|Unitary Matrix|酉矩阵||[1]||
AITD-01999|Univariate Decision Tree|单变量决策树||[1]||
AITD-02000|Universal Approximation Theorem|通用近似定理||[1]||
AITD-02001|Universal Approximator|通用近似器||[1]||
AITD-02002|Universal Function Approximator|通用函数近似器||[1]||
AITD-02003|Unknown Token|未知词元||[1]||
AITD-02004|Unlabeled|未标记||[1]||
AITD-02005|Unnormalized Probability Function|未规范化概率函数||[1]||
AITD-02006|Unprojection|反投影||[1]||
AITD-02007|Unshared Convolution|非共享卷积||[1]||
AITD-02008|Unsupervised|无监督||[1]||
AITD-02009|Unsupervised Feature Learning|无监督特征学习||[1]||
AITD-02010|Unsupervised Layer-Wise Training|无监督逐层训练||[1]||
AITD-02011|Unsupervised Learning Algorithm|无监督学习算法||[1]||
AITD-02012|Unsupervised Learning|无监督学习|UL|[[1]](https://www.jiqizhixin.com/articles/2017-11-17-5)[[2]](https://pubs.rsc.org/en/content/chapter/bk9781788017893-00037/978-1-78801-789-3)[[3]](https://pubs.rsc.org/en/content/chapter/bk9781788017893-00311/978-1-78801-789-3)||
AITD-02013|Unsupervised Pretraining|无监督预训练||[1]||
AITD-02014|Update Gate|更新门||[1]||
AITD-02015|Update Model Parameter|迭代模型参数||[1]||
AITD-02016|Upper Confidence Bounds|上置信界限||[1]||
AITD-02017|Upsampling|上采样||[1]||
AITD-02018|V-Structure|V型结构||[1]||
AITD-02019|Valid|有效||[1]||
AITD-02020|Validation Set|验证集||[[1]](https://www.nature.com/articles/s41557-021-00716-z)||
AITD-02021|Validity Index|有效性指标||[1]||
AITD-02022|Value Function|价值函数||[1]||
AITD-02023|Value Function Approximation|值函数近似||[1]||
AITD-02024|Value Iteration|值迭代||[1]||
AITD-02025|Vanishing And Exploding Gradient Problem|梯度消失与爆炸问题||[1]||
AITD-02026|Vanishing Gradient|梯度消失||[1]||
AITD-02027|Vanishing Gradient Problem|梯度消失问题||[[1]](https://www.jiqizhixin.com/articles/2018-01-07-2)||
AITD-02028|Vapnik-Chervonenkis Dimension|VC维||[1]||
AITD-02029|Variable Elimination|变量消去||[1]||
AITD-02030|Variance|方差||[[1]](https://pubs.rsc.org/en/content/chapter/bk9781788017893-00037/978-1-78801-789-3)||
AITD-02031|Variance Reduction|方差减小||[1]||
AITD-02032|Variance Scaling|方差缩放||[1]||
AITD-02033|Variational Autoencoder|变分自编码器|VAE|[[1]](https://pubs.rsc.org/en/content/chapter/bk9781788017893-00372/978-1-78801-789-3)[[2]](https://pubs.rsc.org/en/content/chapter/bk9781788017893-00280/978-1-78801-789-3)||
AITD-02034|Variational Bayesian|变分贝叶斯||[1]||
AITD-02035|Variational Derivative|变分导数||[1]||
AITD-02036|Variational Distribution|变分分布||[1]||
AITD-02037|Variational Dropout|变分暂退法||[1]||
AITD-02038|Variational EM Algorithm|变分EM算法||[1]||
AITD-02039|Variational Free Energy|变分自由能||[1]||
AITD-02040|Variational Inference|变分推断||[1]||
AITD-02041|Vector|向量||[1]||
AITD-02042|Vector Space|向量空间||[1]||
AITD-02043|Vector Space Model|向量空间模型|VSM|[1]||
AITD-02044|Vectorization|向量化||[1]||
AITD-02045|Version Space|版本空间||[1]||
AITD-02046|Virtual Adversarial Example|虚拟对抗样本||[1]||
AITD-02047|Virtual Adversarial Training|虚拟对抗训练||[1]||
AITD-02048|Visible Layer|可见层||[1]||
AITD-02049|Visible Variable|可见变量||[1]||
AITD-02050|Viterbi Algorithm|维特比算法||[1]||
AITD-02051|Vocabulary|词表||[1]||
AITD-02052|Von Neumann Architecture|冯 · 诺伊曼架构||[1]||
AITD-02053|Voted Perceptron|投票感知器||[1]||
AITD-02054|Wake Sleep|醒眠||[1]||
AITD-02055|Warp|线程束||[1]||
AITD-02056|Wasserstein Distance|Wasserstein距离||[1]||
AITD-02057|Wasserstein GAN|Wasserstein生成对抗网络|WGAN|[[1]](https://www.jiqizhixin.com/articles/2017-10-05)||
AITD-02058|Weak Classifier|弱分类器||[1]||
AITD-02059|Weak Duality|弱对偶性||[1]||
AITD-02060|Weak Learner|弱学习器||[1]||
AITD-02061|Weakly Learnable|弱可学习||[1]||
AITD-02062|Weakly Supervised Learning|弱监督学习||[1]||
AITD-02063|Weight|权重||[[1]](https://www.jiqizhixin.com/articles/2018-01-08-3)[[2]](https://pubs.rsc.org/en/content/chapter/bk9781788017893-00016/978-1-78801-789-3)[[3]](https://pubs.rsc.org/en/content/chapter/bk9781788017893-00037/978-1-78801-789-3)||
AITD-02064|Weight Decay|权重衰减||[1]||
AITD-02065|Weight Normalization|权重规范化||[1]||
AITD-02066|Weight Scaling Inference Rule|权重比例推断规则||[1]||
AITD-02067|Weight Sharing|权共享||[1]||
AITD-02068|Weight Space Symmetry|权重空间对称性||[1]||
AITD-02069|Weight Vector|权值向量||[1]||
AITD-02070|Weighted Distance|加权距离||[1]||
AITD-02071|Weighted Voting|加权投票||[1]||
AITD-02072|Whitening|白化||[1]||
AITD-02073|Wide Convolution|宽卷积||[1]||
AITD-02074|Width|宽度||[1]||
AITD-02075|Winner-Take-All|胜者通吃||[1]||
AITD-02076|Within-Class Scatter Matrix|类内散度矩阵||[1]||
AITD-02077|Word Embedding|词嵌入||[[1]](https://www.jiqizhixin.com/articles/2017-11-20-3)[[2]](https://pubs.rsc.org/en/content/chapter/bk9781788017893-00311/978-1-78801-789-3)||
AITD-02078|Word Sense Disambiguation|词义消歧||[1]||
AITD-02079|Word Vector|词向量||[1]||
AITD-02080|Word Vector Space Model|单词向量空间模型||[1]||
AITD-02081|Word-Document Matrix|单词-文本矩阵||[1]||
AITD-02082|Word-Topic Matrix|单词-话题矩阵||[1]||
AITD-02083|Working Memory|工作记忆||[1]||
AITD-02084|Wrapper Method|包裹式方法||[1]||
AITD-02085|Z-Score Normalization|Z值规范化||[1]||
AITD-02086|Zero Mean|零均值||[1]||
AITD-02087|Zero Padding|零填充||[1]||
AITD-02088|Zero Tensor|零张量||[1]||
AITD-02089|Zero-Centered|零中心化的||[1]||
AITD-02090|Zero-Data Learning|零数据学习||[1]||
AITD-02091|Zero-Shot Learning|零试学习||[[1]](https://www.jiqizhixin.com/articles/2017-03-31-6)||
AITD-02092|Zipf's Law|齐普夫定律||[1]||
AITD-02093|ε-Greedy Method|ε-贪心法||[1]||
AITD-02094|2D Qsar Models|二维定量构效关系模型| |[[1]](https://pubs.rsc.org/en/content/chapter/bk9781788017893-00037/978-1-78801-789-3)|化学|
AITD-02095|3D Cartesian|三维笛卡尔（坐标）| |[[1]](https://pubs.rsc.org/en/content/chapter/bk9781788017893-00372/978-1-78801-789-3)|数学|
AITD-02096|3D Conformation|三维构象| |[[1]](https://pubs.rsc.org/en/content/chapter/bk9781788017893-00037/978-1-78801-789-3)|化学、生化|
AITD-02097|3D Grids|三维（坐标）网格| |[[1]](https://pubs.rsc.org/en/content/chapter/bk9781788017893-00372/978-1-78801-789-3)| |
AITD-02098|3D Qsar Models|三维定量构效关系模型| |[[1]](https://pubs.rsc.org/en/content/chapter/bk9781788017893-00037/978-1-78801-789-3)|化学|
AITD-02099|Aberration-Corrected|像差矫正| |[[1]](https://pubs.rsc.org/en/content/chapter/bk9781788017893-00488/978-1-78801-789-3)|物理|
AITD-02100|Active Machine Learning|主动机器学习| |[[1]](https://pubs.rsc.org/en/content/chapter/bk9781788017893-00450/978-1-78801-789-3)|机器学习|
AITD-02101|Adaptive Fuzzy Neural Network|自适应模糊神经网络| |[[1]](https://pubs.rsc.org/en/content/chapter/bk9781788017893-00227/978-1-78801-789-3)|机器学习|
AITD-02102|Adaptive Sampling|自适应采样| |[[1]](https://pubs.rsc.org/en/content/chapter/bk9781788017893-00076/978-1-78801-789-3)|机器学习|
AITD-02103|Admet Evaluation|毒性评估| |[[1]](https://pubs.rsc.org/en/content/chapter/bk9781788017893-00251/978-1-78801-789-3)|化学|
AITD-02104|Alexnet|AlexNet| |[[1]](https://pubs.rsc.org/en/content/chapter/bk9781788017893-00372/978-1-78801-789-3)|机器学习|
AITD-02105|Alphago|阿尔法狗| |[[1]](https://pubs.rsc.org/en/content/chapter/bk9781788017893-00136/978-1-78801-789-3)[[2]](https://pubs.rsc.org/en/content/chapter/bk9781788017893-00340/978-1-78801-789-3)|机器学习|
AITD-02106|Adaptive Neuro Fuzzy Inference System|自适应神经模糊推理系统| ANFIS|[[1]](https://pubs.rsc.org/en/content/chapter/bk9781788017893-00340/978-1-78801-789-3)|机器学习|
AITD-02107|Approximate Probabilistic Models|近似概率模型| |[[1]](https://pubs.rsc.org/en/content/chapter/bk9781788017893-00311/978-1-78801-789-3)|机器学习|
AITD-02108|Artificial Neurons|人工神经元| |[[1]](https://pubs.rsc.org/en/content/chapter/bk9781788017893-00016/978-1-78801-789-3)|机器学习|
AITD-02109|Artificial Synapses|人工突触| |[[1]](https://pubs.rsc.org/en/content/chapter/bk9781788017893-00016/978-1-78801-789-3)|机器学习|
AITD-02110|Attention-Based|基于注意力（机制）的| |[[1]](https://pubs.rsc.org/en/content/chapter/bk9781788017893-00169/978-1-78801-789-3)|机器学习|
AITD-02111|Automating Synthetic Planning|自动化综合规划| |[[1]](https://pubs.rsc.org/en/content/chapter/bk9781788017893-00136/978-1-78801-789-3)|机器学习|
AITD-02112|Automation|自动化| |[[1]](https://pubs.rsc.org/en/content/chapter/bk9781788017893-00206/978-1-83916-023-3)|机器学习|
AITD-02113|Autonomous Decision-Making|自主决策| |[[1]](https://pubs.rsc.org/en/content/chapter/bk9781788017893-00450/978-1-78801-789-3)|机器学习|
AITD-02114|B-Clustering Algorithms|B树聚类算法| |[[1]](https://pubs.rsc.org/en/content/chapter/bk9781788017893-00450/978-1-78801-789-3)|机器学习|
AITD-02115|Balanced Accuracy|平衡精度| |[[1]](https://pubs.rsc.org/en/content/chapter/bk9781788017893-00016/978-1-78801-789-3)|机器学习|
AITD-02116|Bandgap Energy|带隙能量| |[[1]](https://pubs.rsc.org/en/content/chapter/bk9781788017893-00311/978-1-78801-789-3)|物理|
AITD-02117|Baseline Test|基准测试| |[[1]](https://www.nature.com/articles/s41557-021-00716-z)|机器学习|
AITD-02118|Basin Hopping|盆地跳跃（算法）| |[[1]](https://pubs.rsc.org/en/content/chapter/bk9781788017893-00488/978-1-78801-789-3)|机器学习|
AITD-02119|Bayesian Approach|贝叶斯方法| |[[1]](https://pubs.rsc.org/en/content/chapter/bk9781788017893-00195/978-1-83916-023-3)|统计，机器学习|
AITD-02120|Bayesian Induction|贝叶斯归纳| |[[1]](https://pubs.rsc.org/en/content/chapter/bk9781788017893-00227/978-1-78801-789-3)|统计，机器学习|
AITD-02121|Bayesian Mcmc Methods|贝叶斯马尔可夫链蒙特卡洛方法| |[[1]](https://pubs.rsc.org/en/content/chapter/bk9781788017893-00195/978-1-83916-023-3)|统计，机器学习|
AITD-02122|Bayesian Methods|贝叶斯方法| |[[1]](https://pubs.rsc.org/en/content/chapter/bk9781788017893-00109/978-1-78801-789-3)|统计，机器学习|
AITD-02123|Bayesian Molecular|贝叶斯分子（设计方法）| |[[1]](https://pubs.rsc.org/en/content/chapter/bk9781788017893-00280/978-1-78801-789-3)|统计，机器学习，化学|
AITD-02124|Bayesian Prior|贝叶斯先验| |[[1]](https://pubs.rsc.org/en/content/chapter/bk9781788017893-00206/978-1-83916-023-3)|统计，机器学习|
AITD-02125|Bayesian Program Learning|贝叶斯程序学习|BPL|[[1]](https://pubs.rsc.org/en/content/chapter/bk9781788017893-00311/978-1-78801-789-3)|统计，机器学习|
AITD-02126|Bayesian Regularized Neural Network|贝叶斯正则化神经网络| |[[1]](https://pubs.rsc.org/en/content/chapter/bk9781788017893-00206/978-1-83916-023-3)|统计，机器学习|
AITD-02127|Beam-Scanning|波束扫描| |[[1]](https://pubs.rsc.org/en/content/chapter/bk9781788017893-00450/978-1-78801-789-3)|物理|
AITD-02128|Best Separates|最优分离| |[[1]](https://pubs.rsc.org/en/content/chapter/bk9781788017893-00169/978-1-78801-789-3)|机器学习|
AITD-02129|Biased Dataset|有偏数据集| |[[1]](https://www.nature.com/articles/s41557-021-00716-z)|机器学习|
AITD-02130|Bit Collisions|字节碰撞/冲突| |[[1]](https://pubs.rsc.org/en/content/chapter/bk9781788017893-00372/978-1-78801-789-3)|数据库|
AITD-02131|Black Box|黑盒子| |[[1]](https://pubs.rsc.org/en/content/chapter/bk9781788017893-00227/978-1-78801-789-3)| |
AITD-02132|Black-Box Attack|黑盒攻击| |[[1]](https://pubs.rsc.org/en/content/chapter/bk9781788017893-00450/978-1-78801-789-3)| |
AITD-02133|Bonding Environments|成键环境| |[[1]](https://www.nature.com/articles/s41557-021-00716-z)| |
AITD-02134|Bonferroni Correction|邦弗朗尼校正| |[[1]](https://pubs.rsc.org/en/content/chapter/bk9781788017893-00169/978-1-78801-789-3)|统计|
AITD-02135|Bootstrap Aggregation|引导聚合|bagging|[[1]](https://pubs.rsc.org/en/content/chapter/bk9781788017893-00037/978-1-78801-789-3)|机器学习|
AITD-02136|Broyden–Fletcher–Goldfarb–Shanno|BFGS（算法）|BFGS|[[1]](https://pubs.rsc.org/en/content/chapter/bk9781788017893-00488/978-1-78801-789-3)|一种拟牛顿法，数学计算|
AITD-02137|Buchwald−Hartwig Cross-Coupling|Buchwald–Hartwig 偶联（反应）| |[[1]](https://www.nature.com/articles/s41557-021-00716-z)|化学|
AITD-02138|C4.5 Algorithm|C4.5 算法| |[[1]](https://pubs.rsc.org/en/content/chapter/bk9781788017893-00037/978-1-78801-789-3)|一种决策树算法，数据挖掘|
AITD-02139|Calculation Uncertainties|计算不确定性| |[[1]](https://pubs.rsc.org/en/content/chapter/bk9781788017893-00311/978-1-78801-789-3)| |
AITD-02140|Canonical Ml Methods|经典机器学习方法| |[[1]](https://pubs.rsc.org/en/content/chapter/bk9781788017893-00037/978-1-78801-789-3)| |
AITD-02141|Cartesian Distance Vector|笛卡尔距离向量| |[[1]](https://pubs.rsc.org/en/content/chapter/bk9781788017893-00488/978-1-78801-789-3)| |
AITD-02142|CASP|国际蛋白质结构预测竞赛| |[[1]](https://pubs.rsc.org/en/content/chapter/bk9781788017893-00136/978-1-78801-789-3)|生物|
AITD-02143|Categorical Data|分类数据| |[[1]](https://pubs.rsc.org/en/content/chapter/bk9781788017893-00206/978-1-83916-023-3)| |
AITD-02144|Categorization Algorithms|分类算法| |[[1]](https://pubs.rsc.org/en/content/chapter/bk9781788017893-00227/978-1-78801-789-3)| |
AITD-02145|ChemDataExtractor|化学数据提取器|CDE|[[1]](https://pubs.rsc.org/en/content/chapter/bk9781788017893-00280/978-1-78801-789-3)| |
AITD-02146|Chi-Squared|卡方（分布）| |[[1]](https://pubs.rsc.org/en/content/chapter/bk9781788017893-00311/978-1-78801-789-3)| |
AITD-02147|Classification Model|分类模型| |[[1]](https://pubs.rsc.org/en/content/chapter/bk9781788017893-00195/978-1-83916-023-3)[[2]](https://pubs.rsc.org/en/content/chapter/bk9781788017893-00037/978-1-78801-789-3)| |
AITD-02148|Cluster Resolution Feature Selection|聚类分辨率特征选择|CR-FS|[[1]](https://pubs.rsc.org/en/content/chapter/bk9781788017893-00311/978-1-78801-789-3)| |
AITD-02149|Cluster-Based Splitting|基于聚类的分离方法| |[[1]](https://www.nature.com/articles/s41557-021-00716-z)| |
AITD-02150|Clustering Methods|聚类方法| |[[1]](https://pubs.rsc.org/en/content/chapter/bk9781788017893-00169/978-1-78801-789-3)| |
AITD-02151|Code Pipeline|代码流水线| |[[1]](https://www.nature.com/articles/s41557-021-00716-z)| |
AITD-02152|Coefficient of Determination|决定系数|r^2 or R^2|[[1]](https://pubs.rsc.org/en/content/chapter/bk9781788017893-00037/978-1-78801-789-3)|统计|
AITD-02153|Combined Gradient|组合梯度（算法）| |[[1]](https://pubs.rsc.org/en/content/chapter/bk9781788017893-00076/978-1-78801-789-3)|机器学习|
AITD-02154|Complex Data|复合数据| |[[1]](https://pubs.rsc.org/en/content/chapter/bk9781788017893-00206/978-1-83916-023-3)| |
AITD-02155|Computational Cost|计算成本| |[[1]](https://pubs.rsc.org/en/content/chapter/bk9781788017893-00076/978-1-78801-789-3)| |
AITD-02156|Computational Optimisation|计算优化| |[[1]](https://pubs.rsc.org/en/content/chapter/bk9781788017893-00280/978-1-78801-789-3)| |
AITD-02157|Computational Science|计算科学| |[[1]](https://www.nature.com/articles/s41557-021-00716-z)| |
AITD-02158|Computational Toxicology|计算毒理学| |[[1]](https://pubs.rsc.org/en/content/chapter/bk9781788017893-00001/978-1-78801-789-3)| |
AITD-02159|Computer Science|计算机科学| |[[1]](https://pubs.rsc.org/en/content/chapter/bk9781788017893-00016/978-1-78801-789-3)| |
AITD-02160|Computer Simulations|计算机模拟| |[[1]](https://pubs.rsc.org/en/content/chapter/bk9781788017893-00512/978-1-78801-789-3)| |
AITD-02161|Computer-Aided|计算机辅助| |[[1]](https://pubs.rsc.org/en/content/chapter/bk9781788017893-00136/978-1-78801-789-3)| |
AITD-02162|Constraint|约束| |[[1]](https://pubs.rsc.org/en/content/chapter/bk9781788017893-00169/978-1-78801-789-3)| |
AITD-02163|Core-Loss Spectrum|（电子能量损失谱中的）高能区域| |[[1]](https://pubs.rsc.org/en/content/chapter/bk9781788017893-00424/978-1-78801-789-3)| |
AITD-02164|Coulomb Matrix|库仑矩阵| |[[1]](https://pubs.rsc.org/en/content/chapter/bk9781788017893-00076/978-1-78801-789-3)| |
AITD-02165|Coupled-Cluster Predictions|耦合簇预测| |[[1]](https://pubs.rsc.org/en/content/chapter/bk9781788017893-00372/978-1-78801-789-3)| |
AITD-02166|Cross-Validated Coefficient of Determination|交叉验证的决定系数|q^2 or Q^2|[[1]](https://pubs.rsc.org/en/content/chapter/bk9781788017893-00037/978-1-78801-789-3)| |
AITD-02167|Cross-Validation|交叉验证|CV|[[1]](https://pubs.rsc.org/en/content/chapter/bk9781788017893-00037/978-1-78801-789-3)[[2]](https://www.nature.com/articles/s41557-021-00716-z)[[3]](https://pubs.rsc.org/en/content/chapter/bk9781788017893-00311/978-1-78801-789-3)| |
AITD-02168|Crowd-Sourcing|众包| |[[1]](https://pubs.rsc.org/en/content/chapter/bk9781788017893-00311/978-1-78801-789-3)| 商业模式|
AITD-02169|Cut-Points|切点| |[[1]](https://pubs.rsc.org/en/content/chapter/bk9781788017893-00311/978-1-78801-789-3)| |
AITD-02170|Cutoff Radial Function|截断径向函数| |[[1]](https://pubs.rsc.org/en/content/chapter/bk9781788017893-00488/978-1-78801-789-3)| |
AITD-02171|Data Availability|数据可用性| |[[1]](https://www.nature.com/articles/s41557-021-00716-z)[[2]](https://pubs.rsc.org/en/content/chapter/bk9781788017893-00206/978-1-83916-023-3)| |
AITD-02172|Data Cleaning|数据清洗| |[[1]](https://www.nature.com/articles/s41557-021-00716-z)[[2]](https://pubs.rsc.org/en/content/chapter/bk9781788017893-00311/978-1-78801-789-3)| |
AITD-02173|Data Collection|数据采集| |[[1]](https://pubs.rsc.org/en/content/chapter/bk9781788017893-00109/978-1-78801-789-3)[[2]](https://www.nature.com/articles/s41557-021-00716-z)[[3]](https://pubs.rsc.org/en/content/chapter/bk9781788017893-00311/978-1-78801-789-3)| |
AITD-02174|Data Considerations|数据注意事项| |[[1]](https://pubs.rsc.org/en/content/chapter/bk9781788017893-00372/978-1-78801-789-3)| |
AITD-02175|Data Curation|数据监管| |[[1]](https://www.nature.com/articles/s41557-021-00716-z)| |
AITD-02176|Data Disparity|数据差异| |[[1]](https://pubs.rsc.org/en/content/chapter/bk9781788017893-00136/978-1-78801-789-3)| |
AITD-02177|Data Dredging|数据挖掘| |[[1]](https://www.nature.com/articles/s41557-021-00716-z)| |
AITD-02178|Data Imputation|数据填补| |[[1]](https://pubs.rsc.org/en/content/chapter/bk9781788017893-00311/978-1-78801-789-3)| |
AITD-02179|Data Labels|数据标签| |[[1]](https://pubs.rsc.org/en/content/chapter/bk9781788017893-00109/978-1-78801-789-3)| |
AITD-02180|Data Leakage|数据泄露| |[[1]](https://www.nature.com/articles/s41557-021-00716-z)| |
AITD-02181|Data Pre-Processing|数据预处理| |[[1]](https://pubs.rsc.org/en/content/chapter/bk9781788017893-00311/978-1-78801-789-3)| |
AITD-02182|Data Processing|数据处理| |[[1]](https://pubs.rsc.org/en/content/chapter/bk9781788017893-00340/978-1-78801-789-3)| |
AITD-02183|Data Quality|数据质量| |[[1]](https://www.nature.com/articles/s41557-021-00716-z)[[2]](https://pubs.rsc.org/en/content/chapter/bk9781788017893-00372/978-1-78801-789-3)| |
AITD-02184|Data Reduction|数据缩减| |[[1]](https://pubs.rsc.org/en/content/chapter/bk9781788017893-00109/978-1-78801-789-3)[[2]](https://pubs.rsc.org/en/content/chapter/bk9781788017893-00311/978-1-78801-789-3)| |
AITD-02185|Data Representation|数据表示| |[[1]](https://www.nature.com/articles/s41557-021-00716-z)[[2]](https://pubs.rsc.org/en/content/chapter/bk9781788017893-00311/978-1-78801-789-3)| |
AITD-02186|Data Selection|数据选择| |[[1]](https://www.nature.com/articles/s41557-021-00716-z)| |
AITD-02187|Data Sources|数据源| |[[1]](https://www.nature.com/articles/s41557-021-00716-z)| |
AITD-02188|Data Splitting|数据拆分| |[[1]](https://www.nature.com/articles/s41557-021-00716-z)| |
AITD-02189|Data Transformation|数据转换| |[[1]](https://pubs.rsc.org/en/content/chapter/bk9781788017893-00311/978-1-78801-789-3)| |
AITD-02190|Data-Driven|数据驱动| |[[1]](https://www.nature.com/articles/s41557-021-00716-z)[[2]](https://pubs.rsc.org/en/content/chapter/bk9781788017893-00424/978-1-78801-789-3)| |
AITD-02191|Data-Driven Decision-Making|数据驱动的决策| |[[1]](https://pubs.rsc.org/en/content/chapter/bk9781788017893-00450/978-1-78801-789-3)| |
AITD-02192|Data-Driven Methods|数据驱动的方法| |[[1]](https://www.nature.com/articles/s41557-021-00716-z)| |
AITD-02193|Data-Driven Spectral Analysis|数据驱动的光谱分析| |[[1]](https://pubs.rsc.org/en/content/chapter/bk9781788017893-00424/978-1-78801-789-3)| |
AITD-02194|Data-Mining|数据挖掘| |[[1]](https://www.nature.com/articles/s41557-021-00716-z)| |
AITD-02195|Database|数据库| |[[1]](https://www.nature.com/articles/s41557-021-00716-z)| |
AITD-02196|DE Algorithm|差分进化算法| |[[1]](https://pubs.rsc.org/en/content/chapter/bk9781788017893-00227/978-1-78801-789-3)| |
AITD-02197|Deeplift|DeepLift模型| |[[1]](https://pubs.rsc.org/en/content/chapter/bk9781788017893-00340/978-1-78801-789-3)| |
AITD-02198|Dendrogram|树状图| |[[1]](https://pubs.rsc.org/en/content/chapter/bk9781788017893-00424/978-1-78801-789-3)| |
AITD-02199|Density Functional Theory|密度泛函理论|DFT|[[1]](https://pubs.rsc.org/en/content/chapter/bk9781788017893-00512/978-1-78801-789-3)[[2]](https://pubs.rsc.org/en/content/chapter/bk9781788017893-00280/978-1-78801-789-3)[[3]](https://pubs.rsc.org/en/content/chapter/bk9781788017893-00424/978-1-78801-789-3)[[4]](https://pubs.rsc.org/en/content/chapter/bk9781788017893-00169/978-1-78801-789-3)[[5]](https://pubs.rsc.org/en/content/chapter/bk9781788017893-00311/978-1-78801-789-3)[[6]](https://www.nature.com/articles/s41557-021-00716-z)| |
AITD-02200|Density-Based Spatial Clustering Of Applications With Noise|DBSCAN密度聚类|DBSCAN|[[1]](https://pubs.rsc.org/en/content/chapter/bk9781788017893-00311/978-1-78801-789-3)| |
AITD-02201|Descriptor|描述符| |[[1]](https://pubs.rsc.org/en/content/chapter/bk9781788017893-00037/978-1-78801-789-3)| |
AITD-02202|DFT Calculations|DFT计算| |[[1]](https://pubs.rsc.org/en/content/chapter/bk9781788017893-00280/978-1-78801-789-3)| |
AITD-02203|Dice Similarity|戴斯相似度| |[[1]](https://pubs.rsc.org/en/content/chapter/bk9781788017893-00037/978-1-78801-789-3)| |
AITD-02204|Differential Evolution|差分进化|DE|[[1]](https://pubs.rsc.org/en/content/chapter/bk9781788017893-00227/978-1-78801-789-3)| |
AITD-02205|Dimensionality Reduction|降维| |[[1]](https://pubs.rsc.org/en/content/chapter/bk9781788017893-00037/978-1-78801-789-3)| |
AITD-02206|Direct Neural Network Modeling|正向神经网络建模| |[[1]](https://pubs.rsc.org/en/content/chapter/bk9781788017893-00227/978-1-78801-789-3)| |
AITD-02207|Discrete Manner|离散方式| |[[1]](https://pubs.rsc.org/en/content/chapter/bk9781788017893-00136/978-1-78801-789-3)| |
AITD-02208|Discrete Quanta|离散量子| |[[1]](https://pubs.rsc.org/en/content/chapter/bk9781788017893-00001/978-1-78801-789-3)| |
AITD-02209|Discretization|离散化| |[[1]](https://pubs.rsc.org/en/content/chapter/bk9781788017893-00311/978-1-78801-789-3)| |
AITD-02210|Distillation|蒸馏| |[[1]](https://pubs.rsc.org/en/content/chapter/bk9781788017893-00340/978-1-78801-789-3)| |
AITD-02211|Dynamic Datasets|动态数据集| |[[1]](https://www.nature.com/articles/s41557-021-00716-z)| |
AITD-02212|Dynamic Filter Networks|动态过滤网络| |[[1]](https://pubs.rsc.org/en/content/chapter/bk9781788017893-00227/978-1-78801-789-3)| |
AITD-02213|Dynamic Sampling|动态采样| |[[1]](https://pubs.rsc.org/en/content/chapter/bk9781788017893-00450/978-1-78801-789-3)| |
AITD-02214|Dynamics Simulations|动力学模拟| |[[1]](https://pubs.rsc.org/en/content/chapter/bk9781788017893-00076/978-1-78801-789-3)| |
AITD-02215|Eigenfunction|特征函数| |[[1]](https://pubs.rsc.org/en/content/chapter/bk9781788017893-00076/978-1-78801-789-3)| |
AITD-02216|Electronegativity|电负性| |[[1]](https://www.nature.com/articles/s41557-021-00716-z)| |
AITD-02217|Elman|埃尔曼| |[[1]](https://pubs.rsc.org/en/content/chapter/bk9781788017893-00227/978-1-78801-789-3)| |
AITD-02218|Empirical Models|经验模型| |[[1]](https://pubs.rsc.org/en/content/chapter/bk9781788017893-00227/978-1-78801-789-3)| |
AITD-02219|Energy Derivatives|能源衍生品| |[[1]](https://pubs.rsc.org/en/content/chapter/bk9781788017893-00076/978-1-78801-789-3)| 在DP模型中：能量的导数|
AITD-02220|Energy Potentials|能量潜力| |[[1]](https://pubs.rsc.org/en/content/chapter/bk9781788017893-00076/978-1-78801-789-3)| |
AITD-02221|Ensemble Methods|集成方法| |[[1]](https://pubs.rsc.org/en/content/chapter/bk9781788017893-00037/978-1-78801-789-3)[[2]](https://www.nature.com/articles/s41557-021-00716-z)| |
AITD-02222|Entity Normalisation|实体规范化| |[[1]](https://pubs.rsc.org/en/content/chapter/bk9781788017893-00280/978-1-78801-789-3)| |
AITD-02223|Ethical Considerations|道德考虑| |[[1]](https://pubs.rsc.org/en/content/chapter/bk9781788017893-00109/978-1-78801-789-3)| |
AITD-02224|Euclidean Distances|欧几里得距离| |[[1]](https://pubs.rsc.org/en/content/chapter/bk9781788017893-00512/978-1-78801-789-3)| |
AITD-02225|Evolutionary Algorithms|进化算法|EA|[[1]](https://pubs.rsc.org/en/content/chapter/bk9781788017893-00206/978-1-83916-023-3)[[2]](https://pubs.rsc.org/en/content/chapter/bk9781788017893-00280/978-1-78801-789-3)| |
AITD-02226|Evolutionary Method|进化方法| |[[1]](https://pubs.rsc.org/en/content/chapter/bk9781788017893-00227/978-1-78801-789-3)| |
AITD-02227|Exchange–Correlation|交换关联（的能量/泛函）| |[[1]](https://pubs.rsc.org/en/content/chapter/bk9781788017893-00424/978-1-78801-789-3)| |
AITD-02228|Excited-State Potentials|激发态能量| |[[1]](https://pubs.rsc.org/en/content/chapter/bk9781788017893-00076/978-1-78801-789-3)| |
AITD-02229|Expected Reduction In Distortion|符合预期的失真减少|ERD|[[1]](https://pubs.rsc.org/en/content/chapter/bk9781788017893-00450/978-1-78801-789-3)| |
AITD-02230|Experimental Validation Data|实验验证数据| |[[1]](https://pubs.rsc.org/en/content/chapter/bk9781788017893-00227/978-1-78801-789-3)| |
AITD-02231|Expert Systems|专家系统|ESS|[[1]](https://pubs.rsc.org/en/content/chapter/bk9781788017893-00340/978-1-78801-789-3)| |
AITD-02232|Extended-Connectivity Circular Fingerprint|扩展连接环形指纹|ECFP|[[1]](https://pubs.rsc.org/en/content/chapter/bk9781788017893-00372/978-1-78801-789-3)| |
AITD-02233|Extraction Techniques|提取技术| |[[1]](https://pubs.rsc.org/en/content/chapter/bk9781788017893-00136/978-1-78801-789-3)| |
AITD-02234|Faber-Christensen-Huang-Lilienfeld|Faber-Christensen-Huang-Lilienfeld|FCHL|[[1]](https://pubs.rsc.org/en/content/chapter/bk9781788017893-00076/978-1-78801-789-3)| 四个人提出的化学结构量子机器学习方法|
AITD-02235|Facial Recognition|面部识别| |[[1]](https://pubs.rsc.org/en/content/chapter/bk9781788017893-00001/978-1-78801-789-3)| |
AITD-02236|FAIR Data Principles|FAIR数据原则| |[[1]](https://pubs.rsc.org/en/content/chapter/bk9781788017893-00136/978-1-78801-789-3)| Findability可找寻 Accessibility可访问 Interoperability可交互 Reuse可再用|
AITD-02237|False Negatives|假阴性|FNs|[[1]](https://pubs.rsc.org/en/content/chapter/bk9781788017893-00016/978-1-78801-789-3)| |
AITD-02238|False Positives|假阳性|FPs|[[1]](https://pubs.rsc.org/en/content/chapter/bk9781788017893-00016/978-1-78801-789-3)| |
AITD-02239|Fchl Representation|Fchl 表示| |[[1]](https://pubs.rsc.org/en/content/chapter/bk9781788017893-00076/978-1-78801-789-3)| |
AITD-02240|Feature Binarization|特征二值化| |[[1]](https://pubs.rsc.org/en/content/chapter/bk9781788017893-00311/978-1-78801-789-3)| |
AITD-02241|Feature Transform|特征变换| |[[1]](https://pubs.rsc.org/en/content/chapter/bk9781788017893-00136/978-1-78801-789-3)| |
AITD-02242|Feature Vectors|特征向量| |[[1]](https://pubs.rsc.org/en/content/chapter/bk9781788017893-00372/978-1-78801-789-3)| |
AITD-02243|Features|特征| |[[1]](https://pubs.rsc.org/en/content/chapter/bk9781788017893-00109/978-1-78801-789-3)| |
AITD-02244|Feed Back|反馈| |[[1]](https://pubs.rsc.org/en/content/chapter/bk9781788017893-00037/978-1-78801-789-3)| |
AITD-02245|Feed-Forward Neural Networks|前馈神经网络|FFNN|[[1]](https://pubs.rsc.org/en/content/chapter/bk9781788017893-00227/978-1-78801-789-3)[[2]](https://pubs.rsc.org/en/content/chapter/bk9781788017893-00488/978-1-78801-789-3)[[3]](https://pubs.rsc.org/en/content/chapter/bk9781788017893-00340/978-1-78801-789-3)| |
AITD-02246|Feedback Structure|反馈结构| |[[1]](https://pubs.rsc.org/en/content/chapter/bk9781788017893-00109/978-1-78801-789-3)| |
AITD-02247|Final Evaluation|最终评估| |[[1]](https://www.nature.com/articles/s41557-021-00716-z)| |
AITD-02248|Findable, Accessible, Interoperable, Reusable|可查找、可访问、可互操作、可重用|FAIR|[[1]](https://www.nature.com/articles/s41557-021-00716-z)| |
AITD-02249|First-Principles|第一性原理| |[[1]](https://www.nature.com/articles/s41557-021-00716-z)| |
AITD-02250|Flow Rate|流速| |[[1]](https://pubs.rsc.org/en/content/chapter/bk9781788017893-00136/978-1-78801-789-3)| |
AITD-02251|Forward Cross-Validation|前向交叉验证| |[[1]](https://www.nature.com/articles/s41557-021-00716-z)| |
AITD-02252|Forward Prediction|前向预测| |[[1]](https://pubs.rsc.org/en/content/chapter/bk9781788017893-00136/978-1-78801-789-3)| |
AITD-02253|Forward Reaction Prediction|前向反应预测| |[[1]](https://pubs.rsc.org/en/content/chapter/bk9781788017893-00169/978-1-78801-789-3)| |
AITD-02254|Fuzzy Logic|模糊逻辑|FL|[[1]](https://pubs.rsc.org/en/content/chapter/bk9781788017893-00340/978-1-78801-789-3)| |
AITD-02255|Fuzzy Neural Networks|模糊神经网络|FNN|[[1]](https://pubs.rsc.org/en/content/chapter/bk9781788017893-00311/978-1-78801-789-3)| |
AITD-02256|Ga-Based Approaches|基于遗传算法的方法| |[[1]](https://pubs.rsc.org/en/content/chapter/bk9781788017893-00280/978-1-78801-789-3)| |
AITD-02257|Garbage In, Garbage Out|无用数据入、无用数据出|GIGO|[[1]](https://pubs.rsc.org/en/content/chapter/bk9781788017893-00037/978-1-78801-789-3)| |
AITD-02258|Gas-Phase Networks|气相网络| |[[1]](https://pubs.rsc.org/en/content/chapter/bk9781788017893-00195/978-1-83916-023-3)| |
AITD-02259|Gaussian Kernels|高斯核| |[[1]](https://pubs.rsc.org/en/content/chapter/bk9781788017893-00076/978-1-78801-789-3)| |
AITD-02260|Gaussian-Type Structure Descriptors|高斯型结构描述符|GTSD|[[1]](https://pubs.rsc.org/en/content/chapter/bk9781788017893-00488/978-1-78801-789-3)| |
AITD-02261|General Intelligence|通用智能|GI|[[1]](https://pubs.rsc.org/en/content/chapter/bk9781788017893-00016/978-1-78801-789-3)| |
AITD-02262|Generalized Gradient Approximation|广义梯度近似|GGA|[[1]](https://pubs.rsc.org/en/content/chapter/bk9781788017893-00424/978-1-78801-789-3)| |
AITD-02263|Generative Adversarial Networks|生成对抗网络|GAN|[[1]](https://pubs.rsc.org/en/content/chapter/bk9781788017893-00450/978-1-78801-789-3)[[2]](https://pubs.rsc.org/en/content/chapter/bk9781788017893-00280/978-1-78801-789-3)|机器学习|
AITD-02264|Gradient Boosting Decision Tree|梯度提升决策树|GBDT|[[1]](https://pubs.rsc.org/en/content/chapter/bk9781788017893-00311/978-1-78801-789-3)| |
AITD-02265|Gradient-Based|基于梯度的| |[[1]](https://pubs.rsc.org/en/content/chapter/bk9781788017893-00169/978-1-78801-789-3)| |
AITD-02266|Grain-Surface Networks|粒面网络| |[[1]](https://pubs.rsc.org/en/content/chapter/bk9781788017893-00195/978-1-83916-023-3)| |
AITD-02267|Graph Convolutional|图卷积|GC|[[1]](https://pubs.rsc.org/en/content/chapter/bk9781788017893-00372/978-1-78801-789-3)| |
AITD-02268|Graph Models|图模型| |[[1]](https://pubs.rsc.org/en/content/chapter/bk9781788017893-00372/978-1-78801-789-3)| |
AITD-02269|Graph Neural Networks|图神经网络|GNNS|[[1]](https://pubs.rsc.org/en/content/chapter/bk9781788017893-00372/978-1-78801-789-3)| |
AITD-02270|Graph-Based|基于图形| |[[1]](https://pubs.rsc.org/en/content/chapter/bk9781788017893-00169/978-1-78801-789-3)| |
AITD-02271|Graph-Based Models|基于图的模型| |[[1]](https://pubs.rsc.org/en/content/chapter/bk9781788017893-00169/978-1-78801-789-3)| |
AITD-02272|Graph-Based Neural Networks|基于图的神经网络| |[[1]](https://www.nature.com/articles/s41557-021-00716-z)| |
AITD-02273|Graph-Based Representation|基于图的表示|GB-GA|[[1]](https://pubs.rsc.org/en/content/chapter/bk9781788017893-00280/978-1-78801-789-3)[[2]](https://pubs.rsc.org/en/content/chapter/bk9781788017893-00136/978-1-78801-789-3)| |
AITD-02274|Graph-Convolutional Neural Network|图卷积神经网络| |[[1]](https://pubs.rsc.org/en/content/chapter/bk9781788017893-00280/978-1-78801-789-3)[[2]](https://pubs.rsc.org/en/content/chapter/bk9781788017893-00450/978-1-78801-789-3)| |
AITD-02275|Graphics Processing Units|图形处理器| |[[1]](https://www.nature.com/articles/s41557-021-00716-z)| |
AITD-02276|Gravimetric Polymerization Degree|比重聚合度| |[[1]](https://pubs.rsc.org/en/content/chapter/bk9781788017893-00227/978-1-78801-789-3)| |
AITD-02277|Hamiltonian Matrix|哈密顿矩阵| |[[1]](https://pubs.rsc.org/en/content/chapter/bk9781788017893-00076/978-1-78801-789-3)|物理|
AITD-02278|Hamiltonian Operator|哈密顿算符| |[[1]](https://pubs.rsc.org/en/content/chapter/bk9781788017893-00001/978-1-78801-789-3)|物理|
AITD-02279|Heterogeneous Data|异构数据| |[[1]](https://pubs.rsc.org/en/content/chapter/bk9781788017893-00398/978-1-78801-789-3)[[2]](https://pubs.rsc.org/en/content/chapter/bk9781788017893-00206/978-1-83916-023-3)| |
AITD-02280|Hidden Layers|隐藏层| |[[1]](https://pubs.rsc.org/en/content/chapter/bk9781788017893-00227/978-1-78801-789-3)| |
AITD-02281|High Data Throughput|高数据吞吐量| |[[1]](https://pubs.rsc.org/en/content/chapter/bk9781788017893-00001/978-1-78801-789-3)| |
AITD-02282|High Throughput|高通量|HT|[[1]](https://pubs.rsc.org/en/content/chapter/bk9781788017893-00450/978-1-78801-789-3)| |
AITD-02283|High Throughput Screening|高通量筛选|HTS|[[1]](https://pubs.rsc.org/en/content/chapter/bk9781788017893-00016/978-1-78801-789-3)| |
AITD-02284|High Variance Models|高方差模型| |[[1]](https://pubs.rsc.org/en/content/chapter/bk9781788017893-00311/978-1-78801-789-3)| |
AITD-02285|High-Dimensional Data|高维数据| |[[1]](https://pubs.rsc.org/en/content/chapter/bk9781788017893-00037/978-1-78801-789-3)| |
AITD-02286|High-Dimensional NN|高维神经网络|HDNN|[[1]](https://pubs.rsc.org/en/content/chapter/bk9781788017893-00488/978-1-78801-789-3)| |
AITD-02287|High-Dimensional Objects|高维对象| |[[1]](https://pubs.rsc.org/en/content/chapter/bk9781788017893-00450/978-1-78801-789-3)| |
AITD-02288|High-Throughput|高通量| |[[1]](https://pubs.rsc.org/en/content/chapter/bk9781788017893-00311/978-1-78801-789-3)| |
AITD-02289|Higher-Dimensional Space|高维空间| |[[1]](https://pubs.rsc.org/en/content/chapter/bk9781788017893-00076/978-1-78801-789-3)|数学|
AITD-02290|Higher-Dimensional Spectral Space|高维光谱空间| |[[1]](https://pubs.rsc.org/en/content/chapter/bk9781788017893-00450/978-1-78801-789-3)| |
AITD-02291|Homogenization|同质化| |[[1]](https://www.nature.com/articles/s41557-021-00716-z)| |
AITD-02292|Homomorphic Encryption|同态加密| |[[1]](https://pubs.rsc.org/en/content/chapter/bk9781788017893-00136/978-1-78801-789-3)| |
AITD-02293|Human Face Recognition|人脸识别| |[[1]](https://pubs.rsc.org/en/content/chapter/bk9781788017893-00001/978-1-78801-789-3)|机器学习|
AITD-02294|Human-Encoded|人工编码的| |[[1]](https://pubs.rsc.org/en/content/chapter/bk9781788017893-00136/978-1-78801-789-3)| |
AITD-02295|Hybrid Model|混合模型| |[[1]](https://pubs.rsc.org/en/content/chapter/bk9781788017893-00227/978-1-78801-789-3)| |
AITD-02296|Hybrid Technique|混合技术|HM|[[1]](https://pubs.rsc.org/en/content/chapter/bk9781788017893-00227/978-1-78801-789-3)| |
AITD-02297|Hybrid-Neural Model|混合神经模型| |[[1]](https://pubs.rsc.org/en/content/chapter/bk9781788017893-00227/978-1-78801-789-3)| |
AITD-02298|Hyperparameter Opimization|超参数优化| |[[1]](https://www.nature.com/articles/s41557-021-00716-z)| |
AITD-02299|Hyperparameters|超参数| |[[1]](https://www.nature.com/articles/s41557-021-00716-z)[[2]](https://pubs.rsc.org/en/content/chapter/bk9781788017893-00076/978-1-78801-789-3)[[3]](https://pubs.rsc.org/en/content/chapter/bk9781788017893-00424/978-1-78801-789-3)|机器学习|
AITD-02300|Hyperplanes Separate|超平面分离| |[[1]](https://pubs.rsc.org/en/content/chapter/bk9781788017893-00169/978-1-78801-789-3)| |
AITD-02301|Id3 Algorithm|Id3 算法| |[[1]](https://pubs.rsc.org/en/content/chapter/bk9781788017893-00037/978-1-78801-789-3)| |
AITD-02302|Image And Speech Recognition|图像和语音识别| |[[1]](https://pubs.rsc.org/en/content/chapter/bk9781788017893-00450/978-1-78801-789-3)| |
AITD-02303|Image Classification|图像分类| |[[1]](https://pubs.rsc.org/en/content/chapter/bk9781788017893-00016/978-1-78801-789-3)| |
AITD-02304|Image Classifier|图像分类器| |[[1]](https://pubs.rsc.org/en/content/chapter/bk9781788017893-00450/978-1-78801-789-3)| |
AITD-02305|Image Recognition|图像识别| |[[1]](https://pubs.rsc.org/en/content/chapter/bk9781788017893-00109/978-1-78801-789-3)|机器学习|
AITD-02306|Informative Priors|信息先验| |[[1]](https://pubs.rsc.org/en/content/chapter/bk9781788017893-00195/978-1-83916-023-3)| |
AITD-02307|Input-Output Pairs|输入输出对| |[[1]](https://pubs.rsc.org/en/content/chapter/bk9781788017893-00169/978-1-78801-789-3)| |
AITD-02308|Instance-Based|基于实例的| |[[1]](https://pubs.rsc.org/en/content/chapter/bk9781788017893-00311/978-1-78801-789-3)| |
AITD-02309|Intelligent Machine|智能机器| |[[1]](https://pubs.rsc.org/en/content/chapter/bk9781788017893-00001/978-1-78801-789-3)| |
AITD-02310|Intermediate Neurons|中间神经元| |[[1]](https://pubs.rsc.org/en/content/chapter/bk9781788017893-00227/978-1-78801-789-3)|机器学习|
AITD-02311|Internet Of Things|物联网|IoT|[[1]](https://pubs.rsc.org/en/content/chapter/bk9781788017893-00340/978-1-78801-789-3)| |
AITD-02312|Interpolation Coordinate|插值坐标| |[[1]](https://pubs.rsc.org/en/content/chapter/bk9781788017893-00076/978-1-78801-789-3)| |
AITD-02313|Interpretability|可解释性| |[[1]](https://pubs.rsc.org/en/content/chapter/bk9781788017893-00109/978-1-78801-789-3)| |
AITD-02314|Inverse Neural Modeling|逆神经建模|INN|[[1]](https://pubs.rsc.org/en/content/chapter/bk9781788017893-00227/978-1-78801-789-3)| |
AITD-02315|Inverse Neural Network Modeling|逆神经网络建模| |[[1]](https://pubs.rsc.org/en/content/chapter/bk9781788017893-00227/978-1-78801-789-3)| |
AITD-02316|Iterative Learning|迭代学习| |[[1]](https://pubs.rsc.org/en/content/chapter/bk9781788017893-00001/978-1-78801-789-3)| |
AITD-02317|Joint Distribution|联合分布| |[[1]](https://pubs.rsc.org/en/content/chapter/bk9781788017893-00136/978-1-78801-789-3)| |
AITD-02318|Jordan-Elman Neural Networks|Jordan-Elman 神经网络| |[[1]](https://pubs.rsc.org/en/content/chapter/bk9781788017893-00227/978-1-78801-789-3)| |
AITD-02319|K Clusters|K聚类| |[[1]](https://pubs.rsc.org/en/content/chapter/bk9781788017893-00169/978-1-78801-789-3)| |
AITD-02320|K Nearest Points|K 最近点| |[[1]](https://pubs.rsc.org/en/content/chapter/bk9781788017893-00311/978-1-78801-789-3)|统计|
AITD-02321|K-1 Folds|K-1 折| |[[1]](https://pubs.rsc.org/en/content/chapter/bk9781788017893-00169/978-1-78801-789-3)| |
AITD-02322|K-Edge (O-K Edge)|K-边缘（O-K 边缘）| |[[1]](https://pubs.rsc.org/en/content/chapter/bk9781788017893-00424/978-1-78801-789-3)| |
AITD-02323|K-Means|K-均值| |[[1]](https://pubs.rsc.org/en/content/chapter/bk9781788017893-00169/978-1-78801-789-3)|统计|
AITD-02324|Kendall’S Tau|肯德尔等级相关系数| |[[1]](https://pubs.rsc.org/en/content/chapter/bk9781788017893-00037/978-1-78801-789-3)| |
AITD-02325|Kernel Ridge Regression|核岭回归|KRR|[[1]](https://pubs.rsc.org/en/content/chapter/bk9781788017893-00311/978-1-78801-789-3)[[2]](https://pubs.rsc.org/en/content/chapter/bk9781788017893-00280/978-1-78801-789-3)[[3]](https://pubs.rsc.org/en/content/chapter/bk9781788017893-00076/978-1-78801-789-3)| |
AITD-02326|Kernels|内核| |[[1]](https://pubs.rsc.org/en/content/chapter/bk9781788017893-00037/978-1-78801-789-3)| |
AITD-02327|Kinetic Curve|动力学曲线| |[[1]](https://pubs.rsc.org/en/content/chapter/bk9781788017893-00227/978-1-78801-789-3)| |
AITD-02328|KNN Model|K 近邻模型| |[[1]](https://pubs.rsc.org/en/content/chapter/bk9781788017893-00037/978-1-78801-789-3)| |
AITD-02329|Knowledge Extraction|知识提取| |[[1]](https://pubs.rsc.org/en/content/chapter/bk9781788017893-00311/978-1-78801-789-3)| |
AITD-02330|Knowledge Gradient|知识梯度|KG|[[1]](https://pubs.rsc.org/en/content/chapter/bk9781788017893-00311/978-1-78801-789-3)| |
AITD-02331|L1 And L2 Regularization|L1与L2正则化| |[[1]](https://pubs.rsc.org/en/content/chapter/bk9781788017893-00076/978-1-78801-789-3)| |
AITD-02332|Laboratory Level|实验室级别| |[[1]](https://pubs.rsc.org/en/content/chapter/bk9781788017893-00136/978-1-78801-789-3)| |
AITD-02333|Language Processing|语言处理| |[[1]](https://pubs.rsc.org/en/content/chapter/bk9781788017893-00001/978-1-78801-789-3)| |
AITD-02334|Laplacian Prior|拉普拉斯先验| |[[1]](https://pubs.rsc.org/en/content/chapter/bk9781788017893-00206/978-1-83916-023-3)| |
AITD-02335|Large-Scale Data Storage|大规模数据存储| |[[1]](https://pubs.rsc.org/en/content/chapter/bk9781788017893-00001/978-1-78801-789-3)| |
AITD-02336|Lasers|激光器| |[[1]](https://pubs.rsc.org/en/content/chapter/bk9781788017893-00136/978-1-78801-789-3)| |
AITD-02337|Lasso Regression|拉索回归| |[[1]](https://pubs.rsc.org/en/content/chapter/bk9781788017893-00206/978-1-83916-023-3)| |
AITD-02338|LBP|局部二值模式| |[[1]](https://pubs.rsc.org/en/content/chapter/bk9781788017893-00340/978-1-78801-789-3)| |
AITD-02339|Least Absolute Shrinkage And Selection Operator|Lasso回归|LASSO|[[1]](https://pubs.rsc.org/en/content/chapter/bk9781788017893-00037/978-1-78801-789-3)| |
AITD-02340|Least Square Support Vector Machine|最小二乘支持向量机|LSSVM|[[1]](https://pubs.rsc.org/en/content/chapter/bk9781788017893-00311/978-1-78801-789-3)| |
AITD-02341|Ligand-Field|配位场| |[[1]](https://www.nature.com/articles/s41557-021-00716-z)| |
AITD-02342|Linear|线性的| |[[1]](https://pubs.rsc.org/en/content/chapter/bk9781788017893-00037/978-1-78801-789-3)[[2]](https://pubs.rsc.org/en/content/chapter/bk9781788017893-00016/978-1-78801-789-3)|数学|
AITD-02343|Linear Dimension Reduction Methods|线性降维方法| |[[1]](https://pubs.rsc.org/en/content/chapter/bk9781788017893-00450/978-1-78801-789-3)| |
AITD-02344|Linear Vibronic Coupling Model|线性振子耦合模型| |[[1]](https://pubs.rsc.org/en/content/chapter/bk9781788017893-00076/978-1-78801-789-3)| |
AITD-02345|Local Recurrent|本地卷积| |[[1]](https://pubs.rsc.org/en/content/chapter/bk9781788017893-00227/978-1-78801-789-3)| |
AITD-02346|Logic And Heuristics Applied To Synthetic Analysis|LHASA 程序|LHASA|[[1]](https://pubs.rsc.org/en/content/chapter/bk9781788017893-00169/978-1-78801-789-3)| |
AITD-02347|Long-Range Prediction|长期预测| |[[1]](https://pubs.rsc.org/en/content/chapter/bk9781788017893-00227/978-1-78801-789-3)| |
AITD-02348|Long-Range Prediction Models|长期预测模型| |[[1]](https://pubs.rsc.org/en/content/chapter/bk9781788017893-00227/978-1-78801-789-3)| |
AITD-02349|Long-Term Planning|长期规划| |[[1]](https://pubs.rsc.org/en/content/chapter/bk9781788017893-00001/978-1-78801-789-3)| |
AITD-02350|Long-Term Reward|长期回报| |[[1]](https://pubs.rsc.org/en/content/chapter/bk9781788017893-00136/978-1-78801-789-3)| |
AITD-02351|Machine-Readable Data|机器可读的数据| |[[1]](https://pubs.rsc.org/en/content/chapter/bk9781788017893-00169/978-1-78801-789-3)| |
AITD-02352|Mae|平均绝对误差|MAE|[[1]](https://pubs.rsc.org/en/content/chapter/bk9781788017893-00076/978-1-78801-789-3)| |
AITD-02353|Mahalanobis Distances|马氏距离| |[[1]](https://pubs.rsc.org/en/content/chapter/bk9781788017893-00311/978-1-78801-789-3)|统计|
AITD-02354|Matrices|矩阵| |[[1]](https://pubs.rsc.org/en/content/chapter/bk9781788017893-00037/978-1-78801-789-3)|数学|
AITD-02355|Matthews Correlation Coefficient|马修斯相关系数|MCC|[[1]](https://pubs.rsc.org/en/content/chapter/bk9781788017893-00016/978-1-78801-789-3)| |
AITD-02356|Maximum Likelihood Methods|最大似然法| |[[1]](https://pubs.rsc.org/en/content/chapter/bk9781788017893-00109/978-1-78801-789-3)|统计|
AITD-02357|Maximum Likelihood Procedures|最大似然估计法| |[[1]](https://pubs.rsc.org/en/content/chapter/bk9781788017893-00311/978-1-78801-789-3)|统计|
AITD-02358|MCTS Method|蒙特卡洛树搜索方法| |[[1]](https://pubs.rsc.org/en/content/chapter/bk9781788017893-00136/978-1-78801-789-3)| |
AITD-02359|Mean-Squared Error|均方误差| |[[1]](https://www.nature.com/articles/s41557-021-00716-z)|统计、机器学习|
AITD-02360|Mechanical Sympathy|机械同感，软硬件协同编程| |[[1]](https://pubs.rsc.org/en/content/chapter/bk9781788017893-00109/978-1-78801-789-3)| |
AITD-02361|Merging|合并| |[[1]](https://pubs.rsc.org/en/content/chapter/bk9781788017893-00311/978-1-78801-789-3)| |
AITD-02362|Message Passing Neural Networks|消息传递神经网络|MPNNS|[[1]](https://pubs.rsc.org/en/content/chapter/bk9781788017893-00372/978-1-78801-789-3)| |
AITD-02363|Microarray Data|微阵列数据| |[[1]](https://pubs.rsc.org/en/content/chapter/bk9781788017893-00206/978-1-83916-023-3)| |
AITD-02364|Mini Batch|小批次| |[[1]](https://pubs.rsc.org/en/content/chapter/bk9781788017893-00076/978-1-78801-789-3)| |
AITD-02365|Mining|挖掘| |[[1]](https://www.nature.com/articles/s41557-021-00716-z)| |
AITD-02366|Mining Out|挖掘| |[[1]](https://pubs.rsc.org/en/content/chapter/bk9781788017893-00037/978-1-78801-789-3)| |
AITD-02367|Missing Values|缺失值| |[[1]](https://pubs.rsc.org/en/content/chapter/bk9781788017893-00311/978-1-78801-789-3)|统计|
AITD-02368|ML Algorithm|机器学习算法| |[[1]](https://pubs.rsc.org/en/content/chapter/bk9781788017893-00016/978-1-78801-789-3)| |
AITD-02369|ML Modelling|机器学习建模| |[[1]](https://pubs.rsc.org/en/content/chapter/bk9781788017893-00206/978-1-83916-023-3)| |
AITD-02370|ML Potentials|机器学习势能| |[[1]](https://pubs.rsc.org/en/content/chapter/bk9781788017893-00076/978-1-78801-789-3)| |
AITD-02371|ML-Driven|机器学习驱动的| |[[1]](https://pubs.rsc.org/en/content/chapter/bk9781788017893-00136/978-1-78801-789-3)| |
AITD-02372|ML-Driven Optimization|机器学习驱动的最优化| |[[1]](https://pubs.rsc.org/en/content/chapter/bk9781788017893-00136/978-1-78801-789-3)| |
AITD-02373|MLP Neural Model|多层感知机神经模型| |[[1]](https://pubs.rsc.org/en/content/chapter/bk9781788017893-00227/978-1-78801-789-3)| |
AITD-02374|Model Construction|模型构建| |[[1]](https://pubs.rsc.org/en/content/chapter/bk9781788017893-00311/978-1-78801-789-3)| |
AITD-02375|Model Evaluation|模型评估| |[[1]](https://pubs.rsc.org/en/content/chapter/bk9781788017893-00311/978-1-78801-789-3)| |
AITD-02376|Model Performance|模型性能| |[[1]](https://www.nature.com/articles/s41557-021-00716-z)| |
AITD-02377|Model Statistics|模型统计| |[[1]](https://pubs.rsc.org/en/content/chapter/bk9781788017893-00037/978-1-78801-789-3)| |
AITD-02378|Model Training|模型训练| |[[1]](https://www.nature.com/articles/s41557-021-00716-z)|机器学习|
AITD-02379|Model Validation|模型验证| |[[1]](https://pubs.rsc.org/en/content/chapter/bk9781788017893-00016/978-1-78801-789-3)| |
AITD-02380|Model-Based Iterative Reconstruction|基于模型的迭代重建|MBIR|[[1]](https://pubs.rsc.org/en/content/chapter/bk9781788017893-00450/978-1-78801-789-3)| |
AITD-02381|Model-Construction|模型构建| |[[1]](https://www.nature.com/articles/s41557-021-00716-z)| |
AITD-02382|Modelling Scenario|建模场景| |[[1]](https://pubs.rsc.org/en/content/chapter/bk9781788017893-00016/978-1-78801-789-3)| |
AITD-02383|Molecular Graph Theory|分子图论| |[[1]](https://pubs.rsc.org/en/content/chapter/bk9781788017893-00037/978-1-78801-789-3)| |
AITD-02384|Molecular Modelling|分子建模| |[[1]](https://www.nature.com/articles/s41557-021-00716-z)| |
AITD-02385|Monte Carlo Tree Search|蒙特卡洛树搜索|MCTS|[[1]](https://pubs.rsc.org/en/content/chapter/bk9781788017893-00136/978-1-78801-789-3)[[2]](https://pubs.rsc.org/en/content/chapter/bk9781788017893-00169/978-1-78801-789-3)[[3]](https://pubs.rsc.org/en/content/chapter/bk9781788017893-00280/978-1-78801-789-3)|数学|
AITD-02386|Moore’S Law|摩尔定律| |[[1]](https://pubs.rsc.org/en/content/chapter/bk9781788017893-00512/978-1-78801-789-3)|计算机|
AITD-02387|ms-QSBER-EL Model|基于人工神经网络组合的结构生物学效应定量关系多尺度模型| |[[1]](https://pubs.rsc.org/en/content/chapter/bk9781788017893-00398/978-1-78801-789-3)| |
AITD-02388|Multi-Agent Control System|多智能体控制系统| |[[1]](https://pubs.rsc.org/en/content/chapter/bk9781788017893-00340/978-1-78801-789-3)| |
AITD-02389|Multi-Core Desktop Computer|多核台式计算机| |[[1]](https://pubs.rsc.org/en/content/chapter/bk9781788017893-00136/978-1-78801-789-3)|计算机|
AITD-02390|Multi-Dimensional Big Data Analysis|多维度大数据分析| |[[1]](https://pubs.rsc.org/en/content/chapter/bk9781788017893-00424/978-1-78801-789-3)| |
AITD-02391|Multi-Layer Feed-Forward|多层前馈|MLFF|[[1]](https://pubs.rsc.org/en/content/chapter/bk9781788017893-00076/978-1-78801-789-3)| |
AITD-02392|Multi-Objective Genetic Algorithm|多目标遗传算法|MOGA|[[1]](https://pubs.rsc.org/en/content/chapter/bk9781788017893-00311/978-1-78801-789-3)| |
AITD-02393|Multi-Objective Optimization|多目标优化| |[[1]](https://pubs.rsc.org/en/content/chapter/bk9781788017893-00227/978-1-78801-789-3)|机器学习|
AITD-02394|Multi-Reaction Synthesis|多反应合成| |[[1]](https://pubs.rsc.org/en/content/chapter/bk9781788017893-00136/978-1-78801-789-3)| |
AITD-02395|Multilayer Perceptron|多层感知机| |[[1]](https://pubs.rsc.org/en/content/chapter/bk9781788017893-00227/978-1-78801-789-3)| |
AITD-02396|Multivariate Regression|多变量回归| |[[1]](https://pubs.rsc.org/en/content/chapter/bk9781788017893-00037/978-1-78801-789-3)| |
AITD-02397|N-Dimensional Space|N维空间| |[[1]](https://pubs.rsc.org/en/content/chapter/bk9781788017893-00372/978-1-78801-789-3)| |
AITD-02398|Naive Bayesian|朴素贝叶斯| |[[1]](https://pubs.rsc.org/en/content/chapter/bk9781788017893-00251/978-1-78801-789-3)|统计|
AITD-02399|Naive Bayesian Methods|朴素贝叶斯方法| |[[1]](https://pubs.rsc.org/en/content/chapter/bk9781788017893-00251/978-1-78801-789-3)|统计|
AITD-02400|Named Entity Recognition，NER|命名实体识别|NER|[[1]](https://pubs.rsc.org/en/content/chapter/bk9781788017893-00280/978-1-78801-789-3)| |
AITD-02401|Nearest Neighbors|近邻| |[[1]](https://pubs.rsc.org/en/content/chapter/bk9781788017893-00311/978-1-78801-789-3)| |
AITD-02402|Nearest Neighbour Model|近邻模型| |[[1]](https://pubs.rsc.org/en/content/chapter/bk9781788017893-00001/978-1-78801-789-3)| |
AITD-02403|Negative Predictive Value|阴性预测值|NPV|[[1]](https://pubs.rsc.org/en/content/chapter/bk9781788017893-00016/978-1-78801-789-3)| |
AITD-02404|Network Architecture|网络结构| |[[1]](https://pubs.rsc.org/en/content/chapter/bk9781788017893-00076/978-1-78801-789-3)|机器学习|
AITD-02405|Network Geometry|网络几何| |[[1]](https://pubs.rsc.org/en/content/chapter/bk9781788017893-00109/978-1-78801-789-3)| |
AITD-02406|Neural Turing Machines|神经图灵机|NTM|[[1]](https://pubs.rsc.org/en/content/chapter/bk9781788017893-00311/978-1-78801-789-3)[[2]](https://pubs.rsc.org/en/content/chapter/bk9781788017893-00109/978-1-78801-789-3)| |
AITD-02407|Neural-Network-Based Function|基于神经网络的函数| |[[1]](https://pubs.rsc.org/en/content/chapter/bk9781788017893-00251/978-1-78801-789-3)| |
AITD-02408|Neurons|神经元| |[[1]](https://pubs.rsc.org/en/content/chapter/bk9781788017893-00016/978-1-78801-789-3)|机器学习|
AITD-02409|Nuclear Magnetic Resonance|核磁共振|NMR|[[1]](https://pubs.rsc.org/en/content/chapter/bk9781788017893-00001/978-1-78801-789-3)| |
AITD-02410|Noise Filters|噪声过滤器| |[[1]](https://pubs.rsc.org/en/content/chapter/bk9781788017893-00311/978-1-78801-789-3)| |
AITD-02411|Noise-Free|无噪的| |[[1]](https://pubs.rsc.org/en/content/chapter/bk9781788017893-00311/978-1-78801-789-3)| |
AITD-02412|Non-Linear|非线性| |[[1]](https://pubs.rsc.org/en/content/chapter/bk9781788017893-00016/978-1-78801-789-3)|数学、统计|
AITD-02413|Non-Linear Correlation|非线性相关| |[[1]](https://pubs.rsc.org/en/content/chapter/bk9781788017893-00195/978-1-83916-023-3)|统计|
AITD-02414|Non-Linearity|非线性| |[[1]](https://pubs.rsc.org/en/content/chapter/bk9781788017893-00016/978-1-78801-789-3)| |
AITD-02415|Non-Parametric Algorithm|非参数化学习算法| |[[1]](https://pubs.rsc.org/en/content/chapter/bk9781788017893-00311/978-1-78801-789-3)| |
AITD-02416|Non-Safety-Critical Applications|非安全关键型应用| |[[1]](https://pubs.rsc.org/en/content/chapter/bk9781788017893-00109/978-1-78801-789-3)| |
AITD-02417|Non-Steady-State|非稳态| |[[1]](https://pubs.rsc.org/en/content/chapter/bk9781788017893-00340/978-1-78801-789-3)| |
AITD-02418|Non-Stochastic|非随机的| |[[1]](https://pubs.rsc.org/en/content/chapter/bk9781788017893-00398/978-1-78801-789-3)| |
AITD-02419|Non-Template|非模板| |[[1]](https://pubs.rsc.org/en/content/chapter/bk9781788017893-00136/978-1-78801-789-3)| |
AITD-02420|Non-Template Methods|非模板方法| |[[1]](https://pubs.rsc.org/en/content/chapter/bk9781788017893-00136/978-1-78801-789-3)| |
AITD-02421|Non-Zero Weight|非零权重| |[[1]](https://pubs.rsc.org/en/content/chapter/bk9781788017893-00037/978-1-78801-789-3)| |
AITD-02422|On-The-Fly Optimization|运行中优化| |[[1]](https://pubs.rsc.org/en/content/chapter/bk9781788017893-00136/978-1-78801-789-3)|计算机|
AITD-02423|One-Hot Vector|独热向量| |[[1]](https://pubs.rsc.org/en/content/chapter/bk9781788017893-00136/978-1-78801-789-3)|整个矢量中之后一个数为1 其余为0|
AITD-02424|Open-Source|开源| |[[1]](https://www.nature.com/articles/s41557-021-00716-z)|软件工程|
AITD-02425|Open-Source Dataset|开源数据集| |[[1]](https://pubs.rsc.org/en/content/chapter/bk9781788017893-00169/978-1-78801-789-3)|机器学习|
AITD-02426|Predicted Label|预测值| |[[1]](https://pubs.rsc.org/en/content/chapter/bk9781788017893-00016/978-1-78801-789-3)|机器学习|
AITD-02427|Prediction|预测| |[[1]](https://www.nature.com/articles/s41557-021-00716-z)|机器学习|
AITD-02428|Prediction Accuracy|预测准确率| |[[1]](https://pubs.rsc.org/en/content/chapter/bk9781788017893-00001/978-1-78801-789-3)|机器学习|
AITD-02429|Predictor|预测器/决策函数| |[[1]](https://pubs.rsc.org/en/content/chapter/bk9781788017893-00251/978-1-78801-789-3)|机器学习|
AITD-02430|Protein Folding|蛋白折叠| |[[1]](https://pubs.rsc.org/en/content/chapter/bk9781788017893-00109/978-1-78801-789-3)[[2]](https://pubs.rsc.org/en/content/chapter/bk9781788017893-00037/978-1-78801-789-3)|生物|
AITD-02431|Quantum Chemistry|量子化学| |[[1]](https://pubs.rsc.org/en/content/chapter/bk9781788017893-00076/978-1-78801-789-3)|化学|
AITD-02432|Quantum Theory|量子理论| |[[1]](https://pubs.rsc.org/en/content/chapter/bk9781788017893-00001/978-1-78801-789-3)|物理|
AITD-02433|Random Selection|随机选择| |[[1]](https://pubs.rsc.org/en/content/chapter/bk9781788017893-00037/978-1-78801-789-3)|统计|
AITD-02434|Raw Datasets|原始数据集| |[[1]](https://www.nature.com/articles/s41557-021-00716-z)|机器学习|
AITD-02435|Root Mean Square Errors|均方根|RMSE|[[1]](https://pubs.rsc.org/en/content/chapter/bk9781788017893-00488/978-1-78801-789-3)|统计|
AITD-02436|Scaling|缩放||[[1]](https://pubs.rsc.org/en/content/chapter/bk9781788017893-00109/978-1-78801-789-3)|图像处理|
AITD-02437|Simulation|仿真||[[1]](https://pubs.rsc.org/en/content/chapter/bk9781788017893-00340/978-1-78801-789-3)||
AITD-02438|The Global Minimum|全局最小值||[[1]](https://pubs.rsc.org/en/content/chapter/bk9781788017893-00016/978-1-78801-789-3)|机器学习|
AITD-02439|Turing Test|图灵测试||[[1]](https://pubs.rsc.org/en/content/chapter/bk9781788017893-00001/978-1-78801-789-3)|AI，CS|
AITD-02440|Version Control|版本控制||[[1]](https://www.nature.com/articles/s41557-021-00716-z)||
AITD-02441|Workflow|工作流||[[1]](https://www.nature.com/articles/s41557-021-00716-z)||
AITD-02442|Sequence-Function|序列-功能||[1]||
AITD-03425|teleoperation|遥操作||||
AITD-03426|transition|转移元组||||
AITD-03427|buffer|经验池||||
AITD-03428|replay|回放||||
AITD-03429|experience|经验||||
AITD-03430|long| horizon长视角||||
AITD-03431|demonstration|示教||||
AITD-03432|value|-based基于价值||||
AITD-03433|policy|-based基于策略||||
AITD-03434|exploitation|利用||||
AITD-03435|LLM|大语言模型||||
AITD-03436|DRL|深度强化学习||||
AITD-03437|MARL|多智能体强化学习||||
AITD-03438|NLP|自然语言处理||||
AITD-03439|ASR|自动语音识别||||
AITD-03440|TTS|文本语音合成||||
"""




black_list_ai_terms = """
AITD-00007|Accuracy|准确率||[[1]](https://www.nature.com/articles/s41557-021-00716-z)||
AITD-00075|Attribute|属性||[1]||
AITD-00096|Backward|后向||[1]||
AITD-00103|Base|基||[1]||
AITD-00110|Batch|批量||[1]||
AITD-00958|Labeled|标注||[1]||
AITD-00954|Label|标签/标记||[1]||
AITD-00977|Layer|层||[1]||
AITD-00989|Learned|学成||[1]||
AITD-00992|Learning|学习||[1]||
AITD-01083|Loss|损失||[1]||
AITD-01122|Matrix|矩阵||[1]||
AITD-01130|Maxima|极大值||[1]||
AITD-01187|Minima|极小值||[1]||
AITD-00237|Code|编码||[1]||
AITD-01147|Mean|均值||[1]||
AITD-01193|Mixing|混合||[1]||
AITD-01200|Mode|峰值||[1]||
AITD-00208|Channel|通道||[1]||
AITD-01201|Model|模型||[1]||
AITD-01292|Network|网络||[1]||
AITD-01307|Node|结点||[1]||
AITD-01308|Noise|噪声| |[[1]](https://pubs.rsc.org/en/content/chapter/bk9781788017893-00076/978-1-78801-789-3)| |
AITD-01371|Operation|操作||[1]||
AITD-01387|Output|输出||[1]||
AITD-01424|Path|路径||[1]||
AITD-01639|Same|相同||[1]||
AITD-01630|Row|行||[1]||
AITD-01625|Risk|风险||[1]||
AITD-01619|Reward|奖励||[1]||
AITD-01640|Sample|样本||[1]||
AITD-01653|Score|得分||[1]||
AITD-01684|Separate|分离的||[1]||
AITD-01686|Separation|分离||[1]||
AITD-01683|Separable|可分离的||[1]||
AITD-01694|Setting|情景||[1]||
AITD-01699|Shaping|塑造||[1]||
AITD-01705|Shortlist|短列表||[1]||
AITD-01727|Size|大小||[1]||
AITD-01734|Smoothing|平滑||[1]||
AITD-01742|Softmax|Softmax/软最大化||[1]||
AITD-01750|Sparse|稀疏||[1]||
AITD-01771|Square|方阵||[1]||
AITD-01776|Stability|稳定性||[1]||
AITD-01786|Standardization|标准化||[1]||
AITD-01787|State|状态||[1]||
AITD-01793|Statement|声明||[1]||
AITD-01809|Stochastic|随机||[1]||
AITD-01823|Stream|流||[1]||
AITD-01851|Supervised|监督||[1]||
AITD-01863|Symbol|符号||[1]||
AITD-01868|Symmetric|对称||[1]||
AITD-01879|Target|目标||[1]||
AITD-01875|Tagging|标注||[1]||
AITD-01885|Temperature|温度||[1]||
AITD-01898|Threshold|阈值||[[1]](https://pubs.rsc.org/en/content/chapter/bk9781788017893-00076/978-1-78801-789-3)|数学|
AITD-01914|Topic|话题||[1]||
AITD-01923|Tractable|易处理的||[1]||
AITD-01924|Training|训练||[1]||
AITD-01943|Transpose|转置||[1]||
AITD-01947|Trial|试验||[1]||
AITD-02004|Unlabeled|未标记||[1]||
AITD-02008|Unsupervised|无监督||[1]||
AITD-02019|Valid|有效||[1]||
AITD-02030|Variance|方差||[[1]](https://pubs.rsc.org/en/content/chapter/bk9781788017893-00037/978-1-78801-789-3)||
AITD-02041|Vector|向量||[1]||
AITD-02051|Vocabulary|词表||[1]||
AITD-02063|Weight|权重||[[1]](https://www.jiqizhixin.com/articles/2018-01-08-3)[[2]](https://pubs.rsc.org/en/content/chapter/bk9781788017893-00016/978-1-78801-789-3)[[3]](https://pubs.rsc.org/en/content/chapter/bk9781788017893-00037/978-1-78801-789-3)||
AITD-02074|Width|宽度||[1]||
AITD-02162|Constraint|约束| |[[1]](https://pubs.rsc.org/en/content/chapter/bk9781788017893-00169/978-1-78801-789-3)| |
AITD-02195|Database|数据库| |[[1]](https://www.nature.com/articles/s41557-021-00716-z)| |
AITD-02201|Descriptor|描述符| |[[1]](https://pubs.rsc.org/en/content/chapter/bk9781788017893-00037/978-1-78801-789-3)| |
AITD-02243|Features|特征| |[[1]](https://pubs.rsc.org/en/content/chapter/bk9781788017893-00109/978-1-78801-789-3)| |
AITD-02336|Lasers|激光器| |[[1]](https://pubs.rsc.org/en/content/chapter/bk9781788017893-00136/978-1-78801-789-3)| |
AITD-02342|Linear|线性的| |[[1]](https://pubs.rsc.org/en/content/chapter/bk9781788017893-00037/978-1-78801-789-3)[[2]](https://pubs.rsc.org/en/content/chapter/bk9781788017893-00016/978-1-78801-789-3)|数学|
AITD-02436|Scaling|缩放||[[1]](https://pubs.rsc.org/en/content/chapter/bk9781788017893-00109/978-1-78801-789-3)|图像处理|
"""


# 拉黑上表中太显然的术语翻译
all_blacklist_terms_predefine = [
    "Domain",
    "Workflow",
    "Optimization",
    "Edge",
    "Experience",
    "Feedback",
    "Benchmark",
    "Baseline",
    "Inference",
    "Cost",
    "Data",
    "Full",
    "Instance",
    "Diversity",
    "Range",
    "Objective",
    "Algorithm",
    "Iteration",
    "Image",
    "Metric",
    "Action",
    "Gate",
    "Exploration",
    "Graph",
    "Operator",
    "Block",
    "Probability",
    "Parameter",
    "Function",
    "Example",
    "Input",
    "Description",
    "Neural Network",
    "Loop",
    "Optimizer",
    "Token",
    "Robustness",
    "Return",
    "Bit",
    "Depth",
    "Distribution",
    "Prediction",
    "Precision",
]




from dataclasses import dataclass
import time
import re

@dataclass
class Term:
    id: str = ""
    words: str = ""
    translation: str = ""

    def __str__(self):
        return f"{self.words} => {self.translation}"

class DFA:
    def __init__(self):
        self.states = {}
        self.build_dfa()

    def build_dfa(self):
        all_terms = []
        all_blacklist_terms = all_blacklist_terms_predefine

        # 将markdown格式的术语转换为Term对象
        for term_raw in black_list_ai_terms.split('\n'):
            if '|' not in term_raw: continue
            if 'AITD' not in term_raw: continue
            t = Term()
            t.id, t.words, t.translation, _, _, _, _ = term_raw.split('|')
            all_blacklist_terms.append(t.words)

        # 将markdown格式的术语转换为Term对象
        for term_raw in ai_terms_from_web.split('\n'):
            if '|' not in term_raw: continue
            if 'AITD' not in term_raw: continue
            t = Term()
            t.id, t.words, t.translation, _, _, _, _ = term_raw.split('|')
            if t.words in all_blacklist_terms: continue
            all_terms.append(t)

        # 构建DFA
        for term in all_terms:
            current_state = self.states
            for char in term.words.lower():
                if char not in current_state:
                    current_state[char] = {}
                current_state = current_state[char]
            current_state['#'] = term  # 用特殊字符标记一个完整的词

    def is_at_word_end(self, text, j):
        # 如果单词后面不足5个字符，则返回False
        if j+5 >= len(text):
            return False
        # 使用正则表达式匹配字符是否在 a-z 或 A-Z 范围内
        is_letter = lambda char: bool(re.match(r'^[a-zA-Z]$', char))
        # 如果单词后面是非字母，则返回True
        if not is_letter(text[j]):
            return True
        # 如果是复数变体，也返回True
        if text[j] == 's' and not is_letter(text[j+1]):
            return True
        # 其他情况
        return False

    def search(self, text):
        # 逐个单词进行匹配搜索
        text = text.lower()
        found_terms = []
        n = len(text)
        max_word_wrap = 30
        for i in range(n):
            current_state = self.states
            j = i
            found = False
            while j < n + max_word_wrap and text[j] in current_state:
                current_state = current_state[text[j]]
                j += 1
                if '#' in current_state and self.is_at_word_end(text, j):
                    if current_state['#'] not in found_terms:
                        if found: found_terms.pop(-1)   # greedy search for longer matched term
                        found_terms.append(current_state['#'])
                        found = True
        return found_terms

def main():

    # 创建DFA
    dfa = DFA()

    tic = time.time()
    # 搜索专有名词
    found_terms = dfa.search(text)
    toc = time.time()

    print(f"文本长度{len(text)}；搜索耗时: {toc - tic:.4f} 秒")

    # 打印找到的术语
    for term in found_terms:  # 使用set去重
        print(f"找到术语: {term}")

if __name__ == "__main__":
    main()