:root {
    --gpt-academic-message-font-size: 15px;
}

.message {
    font-size: var(--gpt-academic-message-font-size) !important;
}

#plugin_arg_menu {
    transform: translate(-50%, -50%);
    border: dashed;
}

/* hide remove all button */
.remove-all.svelte-aqlk7e.svelte-aqlk7e.svelte-aqlk7e {
    visibility: hidden;
}

/* hide selector border */
#input-plugin-group .wrap.svelte-aqlk7e.svelte-aqlk7e.svelte-aqlk7e {
    border: 0px;
    box-shadow: none;
}

#input-plugin-group .secondary-wrap.svelte-aqlk7e.svelte-aqlk7e.svelte-aqlk7e {
    border: none;
    min-width: 0;
}

/* hide selector label */
#input-plugin-group .svelte-1gfkn6j {
    visibility: hidden;
}

/* height of the upload box */
.wrap.svelte-xwlu1w {
    min-height: var(--size-32);
}

/* status bar height */
.min.svelte-1yrv54 {
    min-height: var(--size-12);
}

/* copy btn */
.message-btn-row {
    width: 19px;
    height: 19px;
    position: absolute;
    left: calc(100% + 3px);
    top: 0;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
}
/* .message-btn-row-leading, .message-btn-row-trailing {
    display: inline-flex;
    gap: 4px;
} */
.message-btn-row button {
    font-size: 18px;
    align-self: center;
    align-items: center;
    flex-wrap: nowrap;
    white-space: nowrap;
    display: inline-flex;
    flex-direction: row;
    gap: 4px;
    padding-block: 2px !important;
}


/* Scrollbar Width */
::-webkit-scrollbar {
    height: 12px;
    width: 12px;
}

/* Scrollbar Track */
::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 12px;
}

/* Scrollbar Handle */
::-webkit-scrollbar-thumb {
    background: #888;
    border-radius: 12px;
}

/* Scrollbar Handle on hover */
::-webkit-scrollbar-thumb:hover {
    background: #555;
}

/* input btns: clear, reset, stop */
#input-panel button {
    min-width: min(80px, 100%);
}

/* input btns: clear, reset, stop */
#input-panel2 button {
    min-width: min(80px, 100%);
}

#cbs,
#cbsc {
    background-color: rgba(var(--block-background-fill), 0.5) !important;
}

#interact-panel .form {
    border: hidden
}

.drag-area {
    border: solid;
    border-width: thin;
    user-select: none;
    padding-left: 2%;
    text-align: center;
}

.floating-component #input-panel2 {
    border-top-left-radius: 0px;
    border-top-right-radius: 0px;
    border: solid;
    border-width: thin;
    border-top-width: 0;
}

.floating-component #plugin_arg_panel {
    border-top-left-radius: 0px;
    border-top-right-radius: 0px;
    border: solid;
    border-width: thin;
    border-top-width: 0;
}

.floating-component #edit-panel {
    border-top-left-radius: 0px;
    border-top-right-radius: 0px;
    border: solid;
    border-width: thin;
    border-top-width: 0;
}


.welcome-card-container {
    text-align: center;
    margin: 0 auto;
    display: flex;
    position: absolute;
    width: inherit;
    padding: 50px;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    flex-wrap: wrap;
    justify-content: center;
    transition: opacity 0.6s ease-in-out;
    opacity: 0;
}
.welcome-card-container.show {
    opacity: 1;
}
.welcome-card-container.hide {
    opacity: 0;
}
.welcome-card {
    border-radius: 10px;
    box-shadow: 0px 0px 6px 3px #e5e7eb6b;
    padding: 15px;
    margin: 10px;
    flex: 1 0 calc(30% - 5px);
    transform: rotateY(0deg);
    transition: transform 0.1s;
    transform-style: preserve-3d;
}
.welcome-card.show {
    transform: rotateY(0deg);
}
.welcome-card.hide {
    transform: rotateY(90deg);
}
.welcome-title {
    font-size: 40px;
    padding: 20px;
    margin: 10px;
    flex: 0 0 calc(90%);
}
.welcome-card-title {
    font-size: 20px;
    margin: 2px;
    flex: 0 0 calc(95%);
    padding-bottom: 8px;
    padding-top: 8px;
    padding-right: 8px;
    padding-left: 8px;
    display: flex;
    justify-content: center;
}
.welcome-svg {
    padding-right: 10px;
}

.welcome-title-text {
    text-wrap: nowrap;
}
.welcome-content {
    text-wrap: balance;
    height: 55px;
    font-size: 13px;
    display: flex;
    align-items: center;
}


#gpt-submit-row {
    display: flex;
    gap: 0 !important;
    border-radius: var(--button-large-radius);
    border: var(--button-border-width) solid var(--button-primary-border-color);
    /* background: var(--button-primary-background-fill); */
    background: var(--button-primary-background-fill-hover);
    color: var(--button-primary-text-color);
    box-shadow: var(--button-shadow);
    transition: var(--button-transition);
    display: flex;
}
#gpt-submit-row:hover {
    border-color: var(--button-primary-border-color-hover);
    /* background: var(--button-primary-background-fill-hover); */
    /* color: var(--button-primary-text-color-hover); */
}
#gpt-submit-row button#elem_submit_visible {
    border-top-right-radius: 0px;
    border-bottom-right-radius: 0px;
    box-shadow: none !important;
    flex-grow: 1;
}
#gpt-submit-row #gpt-submit-dropdown {
    border-top-left-radius: 0px;
    border-bottom-left-radius: 0px;
    border-left: 0.5px solid #FFFFFF88 !important;
    display: flex;
    overflow: unset !important;
    max-width: 40px !important;
    min-width: 40px !important;
}
#gpt-submit-row #gpt-submit-dropdown input {
    pointer-events: none;
    opacity: 0; /* 隐藏输入框 */
    width: 0;
    margin-inline: 0;
    cursor: pointer;
}
#gpt-submit-row #gpt-submit-dropdown label {
    display: flex;
    width: 0;
}
#gpt-submit-row #gpt-submit-dropdown label div.wrap {
    background: none;
    box-shadow: none;
    border: none;
}
#gpt-submit-row #gpt-submit-dropdown label div.wrap div.wrap-inner {
    background: none;
    padding-inline: 0;
    height: 100%;
}
#gpt-submit-row #gpt-submit-dropdown svg.dropdown-arrow {
    transform: scale(2) translate(4.5px, -0.3px);
}
#gpt-submit-row #gpt-submit-dropdown > *:hover {
    cursor: context-menu;
}

.tooltip.svelte-p2nen8.svelte-p2nen8 {
    box-shadow: 10px 10px 15px rgba(0, 0, 0, 0.5);
    left: 10px;
}

#tooltip .hidden {
    /* display: none; */
    opacity: 0;
    transition: opacity 0.5s ease;
}

#tooltip .visible {
    /* display: block; */
    opacity: 1;
    transition: opacity 0.5s ease;
}

#elem_fontsize,
#elem_top_p,
#elem_temperature,
#elem_max_length_sl,
#elem_prompt {
    /* 左右为0；顶部为0，底部为2px */
    padding: 0 0 4px 0;
    backdrop-filter: blur(10px);
    background-color: rgba(var(--block-background-fill), 0.5);
}


#tooltip #cbs,
#tooltip #cbsc,
#tooltip .svelte-b6y5bg,
#tooltip .tabitem {
    backdrop-filter: blur(10px);
    background-color: rgba(var(--block-background-fill), 0.5);
}


.reasoning_process {
    font-size: smaller;
    font-style: italic;
    margin: 0px;
    padding: 1em;
    line-height: 1.5;
    text-wrap: wrap;
    opacity: 0.8;
}

.search_result {
    font-size: smaller;
    font-style: italic;
    margin: 0px;
    padding: 1em;
    line-height: 1.5;
    text-wrap: wrap;
    opacity: 0.8;
}

.raw_text {
    display: none;
}

.message_tail {
    justify-content: center;
    align-items: center;
}
.message_tail_stop {
    border: dotted !important;
    margin-top: 5px !important;
    padding-left: 8px !important;
    padding-top: 1px !important;
    padding-bottom: 1px !important;
    padding-right: 12px !important;
}

/* ant 开始 */

/* 通用按钮样式 */
.ant-btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    padding: 8px 16px;
    font-size: 14px;
    font-weight: bold;
    border-radius: 4px;
    cursor: pointer;
    transition: background-color 0.3s;
}

/* 按钮颜色和状态 */
.ant-btn-primary {
    background-color: #1890ff;
    color: white;
    border: none;
}

.ant-btn-primary:hover {
    background-color: #40a9ff;
}

.ant-btn-loading {
    pointer-events: none;
    opacity: 0.7;
}

/* 图标样式 */
.ant-btn-icon {
    display: inline-flex;
    margin-right: 8px;
}

.anticon {
    width: 1em;
    height: 1em;
    fill: currentColor;
}

.anticon-spin {
    animation: spin 1s linear infinite;
}

/* 动画效果 */
@keyframes spin {
    from {
        transform: rotate(0deg);
    }
    to {
        transform: rotate(360deg);
    }
}