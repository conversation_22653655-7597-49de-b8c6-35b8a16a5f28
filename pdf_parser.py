import re
from typing import *
import time
import cv2
import fitz
from PIL import Image
import easyocr
import numpy as np


def content_parser(page_text: str, fields: List[str]):
    res_dict = {}
    for content in page_text.split('\n'):
        content = content.strip()
        for field in fields:
            _p = f"{field}[:：]*?(.*)"
            matched = re.search(_p, content)
            if matched is not None:
                res_dict[field] = matched.group(1).strip().strip(":").strip("：")
                break

    return res_dict


def pdf_text_parser(pdf_path):
    pdf_doc = fitz.open(pdf_path)
    page_count = len(pdf_doc)
    
    # Extract text from available pages, up to 10
    pages_to_extract = min(page_count, 10)
    
    page_texts = []
    for page_num in range(pages_to_extract):
        page_text = pdf_doc.load_page(page_num).get_text().replace('\n', ' ')
        page_texts.append(page_text)
    
    output_str = " ".join(page_texts)
    return output_str


def pdf_to_image(pdf_path, pg_range: int = 1000):
    images = []
    pdf_document = fitz.open(pdf_path)
    
    for page_num in range(min(pg_range, len(pdf_document))):
        page = pdf_document[page_num]
        # Increase DPI and set a maximum image size to prevent excessive memory usage
        pix = page.get_pixmap(dpi=300, matrix=fitz.Matrix(2, 2))
        
        # Convert to numpy array and resize if too large
        img = np.frombuffer(pix.samples, dtype=np.uint8).reshape(pix.h, pix.w, pix.n)
        
        # Resize image if it's too large to prevent OpenCV errors
        max_dimension = 10000  # Adjust this value as needed
        if img.shape[0] > max_dimension or img.shape[1] > max_dimension:
            scale_factor = min(max_dimension / img.shape[0], max_dimension / img.shape[1])
            img = cv2.resize(img, None, fx=scale_factor, fy=scale_factor, interpolation=cv2.INTER_AREA)
        
        images.append(img)
    
    return images



def pdf_ocr_parser(pdf_path, conf_thr: float = 0.2):
    img_list = pdf_to_image(pdf_path)

    reader = easyocr.Reader(["en", "ch_sim"],model_storage_directory='./asset/model')

    output_str = ""
    for idx, img in enumerate(img_list):
        t0 = time.time()
        # Convert to grayscale to improve OCR performance
        img_gray = cv2.cvtColor(img, cv2.COLOR_BGR2GRAY) if len(img.shape) == 3 else img
        
        try:
            result = reader.readtext(img_gray)
            for _, text, conf in result:
                if conf > conf_thr:
                    output_str += text + " "
        except Exception as e:
            print(f"OCR error on page {idx}: {e}")
        
        t1 = time.time()
    
    return output_str.strip()



def parse_pdf_document(pdf_path, output_text_path):
    output_text = pdf_text_parser(pdf_path)
    # Replace double quotes with single quotes
    output_text = output_text.replace('""', '"')
    # output_text = output_text.replace('"', "'")
    output_text = output_text.replace('"', '\\"')  # Escape double quotes
    output_text = output_text.replace('\n', ' ')   # Replace newlines with spaces
    output_text = output_text.replace('\r', ' ')   # Replace carriage returns with spaces
    
    if len(output_text) <= 60:
        output_text = pdf_ocr_parser(pdf_path)
    with open(output_text_path, 'w', encoding='utf-8') as text_file:
        text_file.write(output_text)

