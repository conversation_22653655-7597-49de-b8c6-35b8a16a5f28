from word_parser import parse_word_document
from pdf_parser import parse_pdf_document
import os
import zipfile
import rarfile
import shutil
import subprocess

def convert_doc_to_docx(input_file):
    """
    Convert .doc files to .docx using unoconv

    Args:
        input_file (str): Path to the .doc file

    Returns:
        str: Path to the converted .docx file, or None if conversion fails
    """
    try:
        # Generate output filename
        output_file = os.path.splitext(input_file)[0] + '.docx'

        # Use unoconv to convert the file
        result = subprocess.run(['unoconv', '-f', 'docx', input_file],
                                capture_output=True, text=True, timeout=60)

        if result.returncode == 0 and os.path.exists(output_file):
            return output_file
        else:
            print(f"Conversion failed for {input_file}. Error: {result.stderr}")
            return None

    except subprocess.TimeoutExpired:
        print(f"Conversion timed out for {input_file}")
        return None
    except Exception as e:
        print(f"Unexpected error converting {input_file}: {e}")
        return None

def extract_archive(file_path, extract_dir):
    """
    解压 ZIP 和 RAR 文件
    
    Args:
        file_path (str): 压缩文件路径
        extract_dir (str): 解压目标目录
    
    Returns:
        list: 解压后的文件列表
    """
    extracted_files = []
    try:
        # 创建解压目录
        os.makedirs(extract_dir, exist_ok=True)
        
        # ZIP文件处理
        if file_path.lower().endswith('.zip'):
            with zipfile.ZipFile(file_path, 'r') as zip_ref:
                zip_ref.extractall(extract_dir)
                extracted_files = [
                    os.path.join(extract_dir, name) 
                    for name in zip_ref.namelist()
                ]
        
        # RAR文件处理
        elif file_path.lower().endswith('.rar'):
            with rarfile.RarFile(file_path, 'r') as rar_ref:
                rar_ref.extractall(extract_dir)
                extracted_files = [
                    os.path.join(extract_dir, name) 
                    for name in rar_ref.namelist()
                ]
        
        return extracted_files
    except Exception as e:
        print(f"解压 {file_path} 时发生错误: {e}")
        return []


def parse_zip_file(input_file, output_text_path, word_extensions, pdf_extensions):
    # Get the directory of the input file
    directory = os.path.dirname(input_file)
    
    # Get the filename without extension
    filename_without_ext = os.path.splitext(os.path.basename(input_file))[0]
    
    # Create extraction directory
    extract_dir = os.path.join(directory, f"{filename_without_ext}_extracted")
    
    try:
        # Ensure extraction directory exists
        os.makedirs(extract_dir, exist_ok=True)
        
        # Extract archive
        extracted_files = extract_archive(input_file, extract_dir)
        
        # Process extracted files
        for extracted_file in extracted_files:
            file_ext = os.path.splitext(extracted_file)[1].lower()
            if file_ext in word_extensions:
                if file_ext == '.doc':
                    converted_file = convert_doc_to_docx(extracted_file)
                    if converted_file:
                        extracted_file = converted_file
                    else:
                        print(f"Error processing zip file {input_file}: {e}")
                        continue
                parse_word_document(extracted_file, output_text_path)
            elif file_ext in pdf_extensions:
                parse_pdf_document(extracted_file, output_text_path)
    
    except Exception as e:
        print(f"Error processing zip file {input_file}: {e}")
    
    finally:
        # Always attempt to remove the extraction directory
        try:
            shutil.rmtree(extract_dir)
            print(f"Removed temporary extraction directory: {extract_dir}")
        except Exception as cleanup_error:
            print(f"Error removing extraction directory {extract_dir}: {cleanup_error}")
